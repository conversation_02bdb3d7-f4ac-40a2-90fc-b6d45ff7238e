//package com.labway.lims.specialty;
//
//import cn.hutool.core.date.StopWatch;
//import cn.hutool.core.thread.GlobalThreadPool;
//import cn.hutool.core.util.RandomUtil;
//import com.labway.lims.api.service.SnowflakeService;
//import com.labway.lims.specialty.model.*;
//import com.labway.lims.specialty.service.*;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.io.IOUtils;
//import org.apache.commons.lang3.RandomUtils;
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.junit.BeforeClass;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.core.io.ClassPathResource;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//import java.nio.charset.StandardCharsets;
//import java.time.LocalDateTime;
//import java.time.ZoneId;
//import java.util.*;
//import java.util.concurrent.LinkedBlockingDeque;
//import java.util.concurrent.Semaphore;
//import java.util.concurrent.TimeUnit;
//import java.util.stream.Collectors;
//import java.util.stream.IntStream;
//
//@Slf4j
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class SpecialtyTest {
//
//    private static final List<String> words = new ArrayList<String>();
//
//    private static final Deque<Long> ids = new LinkedBlockingDeque<>();
//    @Resource
//    private TbApplyService applyService;
//    @Resource
//    private TbApplySampleService applySampleService;
//    @Resource
//    private TbApplySampleItemService applySampleItemService;
//    @Resource
//    private TbRackLogicService rackLogicService;
//    @Resource
//    private TbRackLogicSpaceService rackLogicSpaceService;
//    @DubboReference
//    private SnowflakeService snowflakeService;
//
//
//    @BeforeClass
//    public static void init() throws Exception {
//        final ClassPathResource resource = new ClassPathResource("cncity_fulllist.txt");
//        if (resource.exists()) {
//            for (String e : IOUtils.readLines(resource.getInputStream(), StandardCharsets.UTF_8)) {
//                words.add(e.split(" ")[1]);
//            }
//        } else {
//            for (int i = 0; i < 2500; i++) {
//                words.add(new String(new char[]{RandomUtil.randomChinese(), RandomUtil.randomChinese(), RandomUtil.randomChinese()}));
//            }
//        }
//    }
//
//    public static String random() {
//        return words.get(RandomUtils.nextInt(0, words.size()));
//    }
//
//
//    public static Date randomDate() {
//        final LocalDateTime time = LocalDateTime.of(RandomUtils.nextInt(2020, 2024), RandomUtils.nextInt(1, 12), RandomUtils.nextInt(1, 28), RandomUtils.nextInt(0, 24), RandomUtils.nextInt(0, 60), RandomUtils.nextInt(0, 60));
//        return Date.from(time.atZone(ZoneId.systemDefault()).toInstant());
//    }
//
//    private long genId() {
//        final Long poll = ids.poll();
//        if (Objects.nonNull(poll)) {
//            return poll;
//        } else {
//            synchronized (SpecialtyTest.class) {
//                ids.addAll(snowflakeService.genIds(100000));
//            }
//        }
//        return genId();
//    }
//
//    @Test
//    public void test() throws Exception {
//        final int count = Runtime.getRuntime().availableProcessors() << 2;
//        final Semaphore latch = new Semaphore(count);
//
//        while (!Thread.currentThread().isInterrupted()) {
//            for (int k = 0; k < count; k++) {
//                latch.acquire();
//                GlobalThreadPool.execute(() -> {
//                    try {
//                        final List<TbApply> applies = new ArrayList<>();
//                        final List<TbRackLogic> rackLogics = new ArrayList<>();
//                        final List<TbApplySample> applySamples = new ArrayList<>();
//                        final List<TbApplySampleItem> applySampleItems = new ArrayList<>();
//                        final List<TbRackLogicSpace> rackLogicSpaces = new ArrayList<>();
//
//                        for (int i = 0; i < 1000; i++) {
//                            final TbApply apply = createApply();
//                            applies.add(apply);
//
//                            final TbRackLogic rackLogic = createRackLogic();
//                            rackLogics.add(rackLogic);
//
//
//                            for (TbApplySample applySample : createApplySample(apply, rackLogic)) {
//                                applySamples.add(applySample);
//
//                                applySampleItems.addAll(createApplySampleItem(apply, applySample));
//
//                                rackLogicSpaces.addAll(createRackLogicSpace(rackLogic, applySample));
//                            }
//
//                        }
//                        final StopWatch watch = new StopWatch();
//
//                        watch.start("applyService " + applies.size());
//                        applyService.saveBatch(applies);
//                        watch.stop();
//
//                        watch.start("rackLogicService " + rackLogics.size());
//                        rackLogicService.saveBatch(rackLogics);
//                        watch.stop();
//
//                        watch.start("applySampleService " + applySamples.size());
//                        applySampleService.saveBatch(applySamples);
//                        watch.stop();
//
//                        watch.start("applySampleItemService " + applySampleItems.size());
//                        applySampleItemService.saveBatch(applySampleItems);
//                        watch.stop();
//
//                        watch.start("rackLogicSpaceService " + rackLogicSpaces.size());
//                        rackLogicSpaceService.saveBatch(rackLogicSpaces);
//                        watch.stop();
//
//
//                        log.info("新增 1000 申请单成功 {}", watch.prettyPrint(TimeUnit.MILLISECONDS));
//                    } finally {
//                        latch.release();
//                    }
//                });
//            }
//        }
//    }
//
//    public TbApply createApply() {
//        final TbApply apply = new TbApply();
//        apply.setApplyId(genId());
//        apply.setMasterBarcode(String.valueOf(RandomUtils.nextLong(10000000, 100000000)));
//        apply.setPatientName(random());
//        apply.setPatientAge(RandomUtils.nextInt(0, 10));
//        apply.setPatientSubage(RandomUtils.nextInt(0, 10));
//        apply.setPatientSubageUnit("年");
//        apply.setPatientBirthday(randomDate());
//        apply.setPatientCard(random());
//        apply.setPatientCardType(random());
//        apply.setPatientBed(random());
//        apply.setPatientSex(RandomUtils.nextInt(0, 10));
//        apply.setPatientVisitCard(random());
//        apply.setPatientMobile(random());
//        apply.setPatientAddress(random());
//        apply.setSource(random());
//        apply.setApplyType(random());
//        apply.setRemark(random());
//        apply.setSampleCount(random());
//        apply.setSampleProperty(random());
//        apply.setDept(random());
//        apply.setDiagnosis(random());
//        apply.setSendDoctorName(random());
//        apply.setSendDoctorCode(random());
//        apply.setHspOrgId(RandomUtils.nextLong());
//        apply.setHspOrgCode(random());
//        apply.setHspOrgName(random());
//        apply.setOrgId(RandomUtils.nextLong());
//        apply.setOrgName(random());
//        apply.setUrgent(RandomUtils.nextInt(0, 10));
//        apply.setSupplier(random());
//        apply.setApplyDate(randomDate());
//        apply.setSamplingDate(randomDate());
//        apply.setCreateDate(randomDate());
//        apply.setUpdateDate(randomDate());
//        apply.setCreatorName(random());
//        apply.setCreatorId(RandomUtils.nextLong());
//        apply.setUpdaterName(random());
//        apply.setUpdaterId(RandomUtils.nextLong());
//        apply.setStatus(RandomUtils.nextInt(0, 10));
//        apply.setIsDelete(YesOrNoEnum.NO.getCode());
//        return apply;
//    }
//
//    public List<TbApplySample> createApplySample(TbApply apply, TbRackLogic rackLogic) {
//        return IntStream.of(RandomUtils.nextInt(1, 10))
//                .boxed().map(e -> {
//                    final TbApplySample applySample = new TbApplySample();
//                    applySample.setApplySampleId(genId());
//                    applySample.setApplyId(apply.getApplyId());
//                    applySample.setBarcode(String.valueOf(RandomUtils.nextLong(10000000, 100000000)));
//                    applySample.setOutBarcode(String.valueOf(RandomUtils.nextLong(10000000, 100000000)));
//                    applySample.setTube(random());
//                    applySample.setSampleType(random());
//                    applySample.setGroupId(genId());
//                    applySample.setRackId(rackLogic.getRackId());
//                    applySample.setOnePickerId(genId());
//                    applySample.setOnePickerName(random());
//                    applySample.setUrgent(YesOrNoEnum.NO.getCode());
//                    applySample.setCreateDate(randomDate());
//                    applySample.setUpdateDate(randomDate());
//                    applySample.setCreatorId(genId());
//                    applySample.setCreatorName(random());
//                    applySample.setUpdaterId(genId());
//                    applySample.setUpdaterName(random());
//                    applySample.setIsDelete(YesOrNoEnum.NO.getCode());
//                    applySample.setIsOnePick(0);
//                    applySample.setOnePickDate(randomDate());
//                    applySample.setIsSplitBlood(0);
//                    applySample.setSplitterId(genId());
//                    applySample.setSplitterName(random());
//                    applySample.setSplitDate(randomDate());
//                    applySample.setStatus(0);
//                    applySample.setPrinterId(genId());
//                    applySample.setPrinterName(random());
//                    applySample.setPrintDate(randomDate());
//                    return applySample;
//                }).collect(Collectors.toList());
//    }
//
//    public List<TbApplySampleItem> createApplySampleItem(TbApply apply, TbApplySample applySample) {
//        return IntStream.of(RandomUtils.nextInt(3, 20))
//                .boxed().map(e -> {
//                    final TbApplySampleItem applySampleItem = new TbApplySampleItem();
//                    applySampleItem.setApplySampleItemId(genId());
//                    applySampleItem.setApplySampleId(applySample.getApplySampleId());
//                    applySampleItem.setApplyId(apply.getApplyId());
//                    applySampleItem.setTestItemId(genId());
//                    applySampleItem.setTestItemCode(random());
//                    applySampleItem.setTestItemName(random());
//                    applySampleItem.setTestItemType(random());
//                    applySampleItem.setOutItemId(genId());
//                    applySampleItem.setOutItemCode(random());
//                    applySampleItem.setOutItemName(random());
//                    applySampleItem.setSampleType(random());
//                    applySampleItem.setTube(random());
//                    applySampleItem.setGroupId(genId());
//                    applySampleItem.setGroupName(random());
//                    applySampleItem.setRemark(random());
//                    applySampleItem.setCreateDate(randomDate());
//                    applySampleItem.setUpdateDate(randomDate());
//                    applySampleItem.setCreatorId(genId());
//                    applySampleItem.setCreatorName(random());
//                    applySampleItem.setUpdaterId(genId());
//                    applySampleItem.setUpdaterName(random());
//                    applySampleItem.setIsDelete(YesOrNoEnum.NO.getCode());
//                    applySampleItem.setCount(0);
//                    applySampleItem.setUrgent(YesOrNoEnum.NO.getCode());
//                    applySampleItem.setSplitCode(random());
//
//
//                    return applySampleItem;
//                }).collect(Collectors.toList());
//    }
//
//    public TbRackLogic createRackLogic() {
//        final TbRackLogic rackLogic = new TbRackLogic();
//        rackLogic.setRackLogicId(genId());
//        rackLogic.setRackId(genId());
//        rackLogic.setRackName(random());
//        rackLogic.setRackCode(random());
//        rackLogic.setRow(0);
//        rackLogic.setColumn(0);
//        rackLogic.setPosition(0);
//        rackLogic.setCurrentGroupId(genId());
//        rackLogic.setCurrentGroupName(random());
//        rackLogic.setNextGroupId(genId());
//        rackLogic.setNextGroupName(random());
//        rackLogic.setLastHandover(random());
//        rackLogic.setCreateDate(randomDate());
//        rackLogic.setUpdateDate(randomDate());
//        rackLogic.setCreatorId(genId());
//        rackLogic.setCreatorName(random());
//        rackLogic.setUpdaterId(genId());
//        rackLogic.setUpdaterName(random());
//        rackLogic.setIsDelete(YesOrNoEnum.NO.getCode());
//
//        return rackLogic;
//    }
//
//
//    public List<TbRackLogicSpace> createRackLogicSpace(TbRackLogic rackLogic, TbApplySample applySample) {
//        return IntStream.of(RandomUtils.nextInt(10, 50)).boxed().map(e -> {
//            final TbRackLogicSpace rackLogicSpace = new TbRackLogicSpace();
//            rackLogicSpace.setRackLogicSpaceId(genId());
//            rackLogicSpace.setRackLogicId(rackLogic.getRackLogicId());
//            rackLogicSpace.setRackId(rackLogic.getRackId());
//
//
//            rackLogicSpace.setApplySampleId(applySample.getApplySampleId());
//            rackLogicSpace.setStatus(0);
//
//
//            rackLogicSpace.setRow(RandomUtils.nextInt(0, 100));
//            rackLogicSpace.setColumn(RandomUtils.nextInt(0, 100));
//
//            rackLogicSpace.setCreateDate(randomDate());
//            rackLogicSpace.setUpdateDate(randomDate());
//            rackLogicSpace.setCreatorId(genId());
//            rackLogicSpace.setCreatorName(random());
//            rackLogicSpace.setUpdaterId(genId());
//            rackLogicSpace.setUpdaterName(random());
//            rackLogicSpace.setIsDelete(YesOrNoEnum.NO.getCode());
//
//            return rackLogicSpace;
//        }).collect(Collectors.toList());
//    }
//}
