package com.labway.lims.specialty.controller;

import com.google.common.collect.Lists;
import com.labway.lims.api.BarcodeUtils;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.*;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.SpecialtyInspectionDto;
import com.labway.lims.apply.api.service.ElasticSearchSampleService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.RandomStringService;
import com.labway.lims.base.api.service.TestItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@SpringBootTest
class SpecialtySampleResultControllerTest {

    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;
    @DubboReference
    private RandomStringService randomStringService;
    @Resource
    private SnowflakeService snowflakeService;
    @Resource
    private BarcodeUtils barcodeUtils;
    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @DubboReference
    private TestItemService testItemService;

    /**
     * 添加 es 特检数据
     */
    @Test
    void addSpecialtyInspectionDto() {
        final List<HspOrganizationDto> hspOrganizationDtos = hspOrganizationService.selectAll();
        final HspOrganizationDto hspOrganizationDto =
            hspOrganizationDtos.get(RandomUtils.nextInt(0, hspOrganizationDtos.size() - 1));

        final List<TestItemDto> testItems1 = testItemService.selectByOrgId(1);
        final TestItemDto testItemDto = testItems1.get(RandomUtils.nextInt(0, testItems1.size() - 1));

        SpecialtyInspectionDto temp = new SpecialtyInspectionDto();
        temp.setApplySampleId(136401460167508067l);
        temp.setApplyId(136401460146536546l);
        temp.setMasterBarcode(barcodeUtils.genBarcode());
        temp.setDept("科室");
        temp.setUrgent(RandomUtils.nextInt(0, 1));
        temp.setApplyStatus(Arrays.stream(ApplyStatusEnum.values()).map(ApplyStatusEnum::getCode)
            .collect(Collectors.toList()).get(RandomUtils.nextInt(0, ApplyStatusEnum.values().length)));
        temp.setPatientName(randomStringService.randomChineseString());
        temp.setPatientAge(RandomUtils.nextInt(0, 100));
        temp.setPatientSubage(RandomUtils.nextInt(0, 10));
        temp.setPatientSubageUnit(List.of("岁", "月", "天").get(RandomUtils.nextInt(0, 3)));
        temp.setPatientBirthday(new Date());
        temp.setPatientCard("MANUAL001");
        temp.setPatientCardType(PatientCardTypeEnum.ID_CARD.name());
        temp.setPatientBed(String.valueOf(RandomUtils.nextInt(0, 1000)));
        temp.setPatientSex(RandomUtils.nextInt(0, 1));
        temp.setPatientVisitCard("MANUAL001");
        temp.setPatientMobile("**********");
        temp.setPatientAddress("地址");
        // 提供一个数组 随机返回其中一个值
        temp.setApplyTypeCode(List.of("MZ", "ZY", "LNTJ", "TJ").get(RandomUtils.nextInt(0, 3)));
        temp.setApplyTypeName(List.of("MZ", "ZY", "LNTJ", "TJ").get(RandomUtils.nextInt(0, 3)));
        temp.setSampleCount(3);
        temp.setSampleProperty("样本性状");
        temp.setDiagnosis("临床诊断");
        temp.setSendDoctorName("送检医生名称");
        temp.setSendDoctorCode("送检医生编码");
        temp.setApplyDate(new Date());
        temp.setSamplingDate(new Date());
        temp.setRemark("MANUAL001");
        temp.setSource(
            List.of(ApplySourceEnum.MANUAL.name(), ApplySourceEnum.SUPPLEMENTARY.name(), ApplySourceEnum.HIS.name())
                .get(RandomUtils.nextInt(0, 2)));
        temp.setSupplier(ApplySupplierEnum.LIMS.name());

        temp.setHspOrgId(hspOrganizationDto.getHspOrgId());
        temp.setHspOrgCode(hspOrganizationDto.getHspOrgCode());
        temp.setHspOrgName(hspOrganizationDto.getHspOrgName());
        temp.setOutBarcode("MANUAL001");
        temp.setBarcode(barcodeUtils.genBarcode());
        temp.setTubeName("血液");
        temp.setTubeCode(List.of("XY", "N", "XN", "SHI", "FENG").get(RandomUtils.nextInt(0, 4)));
        temp.setSampleTypeCode("样本类型");
        temp.setSampleTypeName("样本类型");

        temp.setGroupId(118601367112884225l);
        temp.setGroupName("专业组名称");
        temp.setSampleStatus(
            List.of(Arrays.stream(SampleStatusEnum.values()).map(SampleStatusEnum::getCode).toArray(Integer[]::new))
                .get(RandomUtils.nextInt(0, SampleStatusEnum.values().length - 1)));
        temp.setRackId(NumberUtils.LONG_ZERO);
        temp.setOnePickerId(NumberUtils.LONG_ZERO);
        temp.setOnePickerName(randomStringService.randomChineseString());
        temp.setOnePickDate(new Date());
        temp.setIsOnePick(List.of(0, 1).get(RandomUtils.nextInt(0, 2)));
        temp.setTwoPickerId(NumberUtils.LONG_ZERO);
        temp.setTwoPickerName(randomStringService.randomChineseString());
        temp.setTwoPickDate(new Date());
        temp.setIsTwoPick(List.of(0, 1).get(RandomUtils.nextInt(0, 2)));
        temp.setIsSplitBlood(List.of(0, 1).get(RandomUtils.nextInt(0, 2)));
        temp.setSplitterId(snowflakeService.genId());
        temp.setSplitterName(randomStringService.randomChineseString());
        temp.setSplitDate(new Date());
        temp.setSampleId(136401715655970817l);
        temp.setSampleNo("MANUAL001");
        temp.setInstrumentGroupId(NumberUtils.LONG_ZERO);
        temp.setInstrumentGroupName("MANUAL001");
        temp.setTestDate(new Date());
        temp.setSampleRemark("MANUAL001");
        temp.setResultRemark("MANUAL001");
        temp.setFinalCheckerId(21314132131L);
        temp.setFinalCheckerName("终审人");
        temp.setFinalCheckDate(new Date());
        temp.setItemType(ItemTypeEnum.GENETICS.name());

        final ArrayList<BaseSampleEsModelDto.TestItem> testItems = Lists.newArrayList();
        final BaseSampleEsModelDto.TestItem testItem = new BaseSampleEsModelDto.TestItem();
        testItem.setTestItemId(131512321512313213L);
        testItem.setTestItemCode("23133232582");
        testItem.setTestItemName("体检");
        testItem.setOutTestItemId(NumberUtils.LONG_ZERO);
        testItem.setOutTestItemCode(StringUtils.EMPTY);
        testItem.setOutTestItemName(StringUtils.EMPTY);
        testItem.setGroupId(123L);
        testItem.setCreateDate(new Date());
        testItem.setPrice(new BigDecimal(RandomUtils.nextInt(0, 199)));
        testItem.setGroupName("专业组名称");
        testItems.add(testItem);

        final BaseSampleEsModelDto.TestItem testItem2 = new BaseSampleEsModelDto.TestItem();
        testItem2.setTestItemId(testItemDto.getTestItemId());
        testItem2.setTestItemCode(testItemDto.getTestItemCode());
        testItem2.setTestItemName(testItemDto.getTestItemName());
        testItem2.setOutTestItemId(NumberUtils.LONG_ZERO);
        testItem2.setCreateDate(new Date());
        testItem2.setPrice(testItemDto.getFeePrice());
        testItem2.setOutTestItemCode(StringUtils.EMPTY);
        testItem2.setOutTestItemName(StringUtils.EMPTY);
        testItem2.setGroupId(123L);
        testItem2.setGroupName("专业组名称");
        testItems.add(testItem2);
        temp.setTestItems(testItems);

        List<BaseSampleEsModelDto.Report> reports = Lists.newArrayList();
        BaseSampleEsModelDto.Report report1 = new BaseSampleEsModelDto.Report();
        report1.setUrl("url");
        report1.setFileType("type");
        reports.add(report1);
        temp.setReports(reports);

        temp.setOrgId(NumberUtils.LONG_ZERO);
        temp.setOrgName("MANUAL001");
        temp.setCreatorId(123456L);
        temp.setCreatorName("MANUAL001");
        temp.setCreateDate(new Date());
        temp.setIsDelete(YesOrNoEnum.NO.getCode());
        temp.setIsPrint(0);
        temp.setTesterId(0L);
        temp.setTesterName("检验人姓名");

        elasticSearchSampleService.insert(temp);
    }
}
