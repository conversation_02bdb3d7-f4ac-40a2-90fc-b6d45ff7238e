package com.labway.lims.specialty.service;

import com.labway.lims.apply.api.dto.SampleTwoPickDto;
import com.labway.lims.apply.api.service.ISampleTwoPicker;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest
class SpecialtySampleTwoPickerTest {
    @DubboReference
    private ISampleTwoPicker iSampleTwoPicker;

    @Test
    void twoPick() {
        SampleTwoPickDto twoPickDto = new SampleTwoPickDto();
        twoPickDto.setApplySampleId(127659747138096933L);
        twoPickDto.setSampleNo("test005");
        twoPickDto.setInstrumentGroupId(120805035415539892L);
        iSampleTwoPicker.twoPick(twoPickDto);
    }

}