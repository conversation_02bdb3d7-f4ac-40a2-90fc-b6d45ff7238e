package com.labway.lims.specialty.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 特检样本 信息 List
 * 
 * <AUTHOR>
 * @since 2023/4/24 15:41
 */
@Getter
@Setter
public class SpecialtySampleListRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 检验日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateStart;

    /**
     * 检验日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDateEnd;
    /**
     * 专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 样本状态
     *
     * @see SampleStatusEnum
     */
    private String sampleStatus;
}
