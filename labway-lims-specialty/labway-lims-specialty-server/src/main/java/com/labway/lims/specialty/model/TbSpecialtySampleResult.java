package com.labway.lims.specialty.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.specialty.SpecialtySampleResultPositionEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 特检样本结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_specialty_sample_result")
public class TbSpecialtySampleResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId
    private Long specialtySampleResultId;

    /**
     * 申请单ID
     */
    private Long applyId;


    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 特检样本ID
     */
    private Long specialtySampleId;

    /**
     * 因为特检报告比较特殊，这个 position 通常指的是此结果的位置
     * 
     * @see SpecialtySampleResultPositionEnum
     *
     */
    private String position;

    /**
     * 1. 文字2. 图片
     */
    private Integer type;

    /**
     * 顺序，当是图片的时候，区分顺序
     */
    private Long sort;

    /**
     * 结果
     */
    private String result;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建人ID
     */
    private String creatorName;

    /**
     * 1:已经删除 0 未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;

}
