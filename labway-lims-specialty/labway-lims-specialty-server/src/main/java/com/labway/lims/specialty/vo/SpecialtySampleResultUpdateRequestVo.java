package com.labway.lims.specialty.vo;

import com.labway.lims.specialty.api.dto.SpecialtySampleResultStandardDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleResultTypeOneDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleResultTypeThreeDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 修改 特检样本 结果 请求 Vo
 *
 * <AUTHOR>
 * @since 2023/4/24 16:34
 */
@Getter
@Setter
public class SpecialtySampleResultUpdateRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 特检样本ID
     */
    private Long specialtySampleId;

    /**
     * 标准 结果模板
     */
    private SpecialtySampleResultStandardDto standardResult;

    /**
     * 特版 1
     */
    private SpecialtySampleResultTypeOneDto typeTwoResult;

    /**
     * 特版 3
     */
    private SpecialtySampleResultTypeThreeDto typeThreeResult;
}
