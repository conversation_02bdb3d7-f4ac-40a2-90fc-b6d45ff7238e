package com.labway.lims.specialty.vo;

import com.labway.lims.api.enums.specialty.SpecialtySampleResultPositionEnum;
import com.labway.lims.api.enums.specialty.SpecialtySampleResultTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 特检样本检验结果
 * 
 * <AUTHOR>
 * @since 2023/4/25 11:31
 */
@Getter
@Setter
public class SpecialtySampleResultItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long specialtySampleResultId;
    /**
     * 因为特检报告比较特殊，这个 position 通常指的是此结果的位置
     * 
     * @see SpecialtySampleResultPositionEnum
     */
    private String position;

    /**
     * 1. 文字2. 图片
     * 
     * @see SpecialtySampleResultTypeEnum
     */
    private Integer type;

    /**
     * 顺序，当是图片的时候，区分顺序
     */
    private Long sort;

    /**
     * 结果
     */
    private String result;

}