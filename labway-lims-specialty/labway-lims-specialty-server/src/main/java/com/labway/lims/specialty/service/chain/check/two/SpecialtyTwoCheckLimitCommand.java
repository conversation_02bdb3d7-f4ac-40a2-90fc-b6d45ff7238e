package com.labway.lims.specialty.service.chain.check.two;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.exception.LimsException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * 加锁
 * 
 * <AUTHOR>
 * @since 2023/6/27 14:17
 */
@Slf4j
@Component
public class SpecialtyTwoCheckLimitCommand implements Command, Filter {
    private static final String MARK = IdUtil.objectId();
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean execute(Context context) throws Exception {
        final SpecialtyTwoCheckContext from = SpecialtyTwoCheckContext.from(context);
        log.info("mark:{}", MARK);
        from.getSpecialtySampleIds().forEach(id -> {
            log.info("mark:{}", MARK + id);
            if (BooleanUtils.isNotTrue(
                stringRedisTemplate.opsForValue().setIfAbsent(MARK + id, StringUtils.EMPTY, Duration.ofSeconds(10)))) {
                throw new LimsException("存在正在审核条码");
            }
        });
        from.put(MARK, StringUtils.EMPTY);

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context context, Exception e) {
        final SpecialtyTwoCheckContext from = SpecialtyTwoCheckContext.from(context);

        if (from.containsKey(MARK)) {
            from.getSpecialtySampleIds().forEach(id -> {
                stringRedisTemplate.delete(MARK + id);

                log.info("删除key:{}", MARK + id);
            });
        }
        return CONTINUE_PROCESSING;
    }

}
