package com.labway.lims.specialty.mapstruct;

import com.labway.lims.specialty.api.dto.SelectSpecialtySampleDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.model.TbSpecialtySample;
import com.labway.lims.specialty.vo.SpecialtySampleListRequestVo;
import com.labway.lims.specialty.vo.SpecialtySampleResultListRequestVo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 特检样本 相关 转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 10:28
 */
@Mapper(componentModel = "spring")
public interface SpecialtySampleConverter {

    SpecialtySampleDto fromTbSpecialtySample(TbSpecialtySample obj);

    List<SpecialtySampleDto> fromTbSpecialtySampleList(List<TbSpecialtySample> list);
    SelectSpecialtySampleDto selectSpecialtySampleDtoFromRequestVo(SpecialtySampleListRequestVo obj);
    SelectSpecialtySampleDto selectSpecialtySampleDtoFromRequestVo(SpecialtySampleResultListRequestVo obj);
}
