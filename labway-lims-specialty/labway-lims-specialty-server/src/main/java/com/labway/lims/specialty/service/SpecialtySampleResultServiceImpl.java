package com.labway.lims.specialty.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.specialty.SpecialtySampleResultTemplateTypeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.base.api.dto.ReportTemplateBindDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.ReportTemplateBindService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.specialty.api.dto.*;
import com.labway.lims.specialty.api.service.SpecialtySampleResultService;
import com.labway.lims.specialty.api.service.SpecialtySampleService;
import com.labway.lims.specialty.mapper.SpecialtySampleResultMapper;
import com.labway.lims.specialty.mapstruct.SpecialtySampleResultConverter;
import com.labway.lims.specialty.model.TbSpecialtySampleResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.core.env.Environment;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 特检样本结果 Service impl
 *
 * <AUTHOR>
 * @since 2023/4/24 16:29
 */
@Slf4j
@DubboService
public class SpecialtySampleResultServiceImpl implements SpecialtySampleResultService {

    @Resource
    private SpecialtySampleResultMapper specialtySampleResultMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private SpecialtySampleResultConverter specialtySampleResultConverter;
    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private SpecialtySampleService specialtySampleService;
    @DubboReference
    private ReportTemplateBindService reportTemplateBindService;
    @DubboReference
    private TestItemService testItemService;
    @Resource
    private Environment environment;
    @DubboReference
    private SampleFlowService sampleFlowService;

    @Override
    public List<SpecialtySampleResultDto> selectBySpecialtySampleId(long specialtySampleId) {
        if (specialtySampleId < 1) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbSpecialtySampleResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbSpecialtySampleResult::getSpecialtySampleId, specialtySampleId);
        queryWrapper.eq(TbSpecialtySampleResult::getIsDelete, YesOrNoEnum.NO.getCode());
        return specialtySampleResultConverter
                .fromTbSpecialtySampleResultList(specialtySampleResultMapper.selectList(queryWrapper));
    }

    @Override
    public List<SpecialtySampleResultDto> selectBySpecialtySampleIds(Collection<Long> specialtySampleIds) {
        if (CollectionUtils.isEmpty(specialtySampleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbSpecialtySampleResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbSpecialtySampleResult::getSpecialtySampleId, specialtySampleIds);
        queryWrapper.eq(TbSpecialtySampleResult::getIsDelete, YesOrNoEnum.NO.getCode());
        return specialtySampleResultConverter
                .fromTbSpecialtySampleResultList(specialtySampleResultMapper.selectList(queryWrapper));
    }

    @Override
    public Map<Long, List<SpecialtySampleResultDto>>
    selectBySpecialtySampleIdsAsMap(Collection<Long> specialtySampleIds) {
        return selectBySpecialtySampleIds(specialtySampleIds).stream()
                .collect(Collectors.groupingBy(SpecialtySampleResultDto::getSpecialtySampleId));
    }

    @Override
    public List<SpecialtySampleResultDto> selectBySpecialtySampleResultIds(Collection<Long> specialtySampleResultIds) {
        if (CollectionUtils.isEmpty(specialtySampleResultIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbSpecialtySampleResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbSpecialtySampleResult::getSpecialtySampleResultId, specialtySampleResultIds);
        queryWrapper.eq(TbSpecialtySampleResult::getIsDelete, YesOrNoEnum.NO.getCode());
        return specialtySampleResultConverter
                .fromTbSpecialtySampleResultList(specialtySampleResultMapper.selectList(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBySpecialtySampleResultIds(Collection<Long> specialtySampleResultIds) {
        if (CollectionUtils.isEmpty(specialtySampleResultIds)) {
            return;
        }

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        log.info("用户 [{}] 删除特检样本结果成功 [{}] 结果 [{}]", loginUser.getNickname(), specialtySampleResultIds,
                specialtySampleResultMapper.deleteBatchIds(specialtySampleResultIds) > 0);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBySpecialtySampleResultIds(SpecialtySampleResultDto dto) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        final TbSpecialtySampleResult target = new TbSpecialtySampleResult();
        BeanUtils.copyProperties(dto, target);

        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setUpdateDate(new Date());

        if (specialtySampleResultMapper.updateById(target) < 1) {
            throw new LimsException("修改特检样本结果失败");
        }

        log.info("用户 [{}] 修改特检样本结果成功 [{}]", loginUser.getNickname(), JSON.toJSONString(target));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long addSpecialtySampleResult(SpecialtySampleResultDto dto) {
        final TbSpecialtySampleResult target = new TbSpecialtySampleResult();

        BeanUtils.copyProperties(dto, target);
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        target.setSpecialtySampleResultId(
                ObjectUtils.defaultIfNull(dto.getSpecialtySampleResultId(), snowflakeService.genId()));
        target.setCreateDate(new Date());
        target.setUpdateDate(new Date());
        target.setCreatorId(loginUser.getUserId());
        target.setCreatorName(loginUser.getNickname());
        target.setUpdaterId(loginUser.getUserId());
        target.setUpdaterName(loginUser.getNickname());
        target.setIsDelete(YesOrNoEnum.NO.getCode());

        if (specialtySampleResultMapper.insert(target) < 1) {
            throw new IllegalStateException("添加特检样本结果失败");
        }
        log.info("用户 [{}] 新增特检样本结果 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(target));

        return target.getSpecialtySampleResultId();

    }

    @Override
    public boolean deleteBySpecialtySampleId(long specialtySampleId) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        LambdaUpdateWrapper<TbSpecialtySampleResult> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(TbSpecialtySampleResult::getIsDelete, YesOrNoEnum.YES.getCode());
        updateWrapper.eq(TbSpecialtySampleResult::getSpecialtySampleId, specialtySampleId);

        if (specialtySampleResultMapper.update(null, updateWrapper) < 1) {
            return false;
        }

        log.info("用户 [{}] 删除遗传样本结果成功 样本 [{}]", loginUser.getNickname(), specialtySampleId);
        return true;
    }

    @Override
    public void deleteBySpecialtySampleIds(Collection<Long> specialtySampleIds) {

        LambdaUpdateWrapper<TbSpecialtySampleResult> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(TbSpecialtySampleResult::getIsDelete, YesOrNoEnum.YES.getCode());

        updateWrapper.in(TbSpecialtySampleResult::getSpecialtySampleId, specialtySampleIds);

        specialtySampleResultMapper.update(null, updateWrapper);

        log.info("用户 [{}] 删除遗传样本结果成功 样本 [{}]", LoginUserHandler.get().getNickname(), specialtySampleIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void alterSpecialtySampleResult(SpecialtySampleDto sampleDto, List<Long> needDeleteResultIdList,
                                           List<SpecialtySampleResultDto> updateList, List<SpecialtySampleResultDto> addList) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 删除项
        if (CollectionUtils.isNotEmpty(needDeleteResultIdList)) {
            this.deleteBySpecialtySampleResultIds(needDeleteResultIdList);
        }

        // 更新项
        updateList.forEach(this::updateBySpecialtySampleResultIds);

        // 新增项
        addList.forEach(this::addSpecialtySampleResult);

        // 修改 特检样本检验时间
//        SpecialtySampleDto specialtySampleDto = new SpecialtySampleDto();
//        specialtySampleDto.setTestDate(new Date());
//        specialtySampleDto.setSpecialtySampleId(sampleDto.getSpecialtySampleId());
//        specialtySampleService.updateBySpecialtySampleId(specialtySampleDto);

        // 更新对应申请单样本 检验人取操作用户
        ApplySampleDto applySampleDto = new ApplySampleDto();
        applySampleDto.setApplySampleId(sampleDto.getApplySampleId());
        applySampleDto.setTesterId(loginUser.getUserId());
        applySampleDto.setTesterName(loginUser.getNickname());
        if (!applySampleService.updateByApplySampleId(applySampleDto)) {
            throw new LimsException("修改对应申请单样本检验人失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSpecialtySampleResult(SpecialtySampleDto sampleDto,
                                            List<SpecialtySampleResultDto> resultDtoList, SpecialtySampleResultTemplateTypeEnum resultTemplateType,
                                            SpecialtySampleResultTemplateDto resultNew) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        // 现有数据 结果模板
        SpecialtySampleResultTemplateDto resultNow = resultNew.getBySpecialtySampleResults(resultDtoList);

        // 需要删除的 结果记录
        final List<Long> needDeleteResultIdList = Lists.newArrayList();
        // 需要更新 的结果记录
        final List<SpecialtySampleResultDto> updateList = Lists.newArrayList();

        // 需要新增 的结果记录
        final List<SpecialtySampleResultDto> addList = Lists.newArrayList();

        // 获取 变更数据
        resultNew.getChangeData(sampleDto, resultDtoList, needDeleteResultIdList, updateList, addList);

        // 删除项
        if (CollectionUtils.isNotEmpty(needDeleteResultIdList)) {
            this.deleteBySpecialtySampleResultIds(needDeleteResultIdList);
        }

        // 更新项
        updateList.forEach(this::updateBySpecialtySampleResultIds);

        // 新增项
        addList.forEach(this::addSpecialtySampleResult);

        // 修改 特检样本检验时间
//        SpecialtySampleDto specialtySampleDto = new SpecialtySampleDto();
//        specialtySampleDto.setTestDate(new Date());
//        specialtySampleDto.setSpecialtySampleId(sampleDto.getSpecialtySampleId());
//        specialtySampleService.updateBySpecialtySampleId(specialtySampleDto);

        // 更新对应申请单样本 检验人取操作用户
        ApplySampleDto applySampleDto = new ApplySampleDto();
        applySampleDto.setApplySampleId(sampleDto.getApplySampleId());
        applySampleDto.setTesterId(user.getUserId());
        applySampleDto.setTesterName(user.getNickname());
        if (!applySampleService.updateByApplySampleId(applySampleDto)) {
            throw new LimsException("修改对应申请单样本检验人失败");
        }

        boolean isAdd = CollectionUtils.isEmpty(resultDtoList);

        // 比对信息
        String compare = new CompareUtils<>().compare(resultNow, resultNew);
        if (!isAdd && StringUtils.isBlank(compare)) {
            // 更新调用 但信息没有变化
            return;
        }
        final SampleFlowDto sampleFlow = new SampleFlowDto();
        sampleFlow.setSampleFlowId(snowflakeService.genId());
        sampleFlow.setApplyId(sampleDto.getApplyId());
        sampleFlow.setApplySampleId(sampleDto.getApplySampleId());
        sampleFlow.setBarcode(sampleDto.getBarcode());
        sampleFlow.setOperateCode(isAdd ? BarcodeFlowEnum.ADD_RESULT.name() : BarcodeFlowEnum.UPDATE_RESULT.name());
        sampleFlow
                .setOperateName(isAdd ? BarcodeFlowEnum.ADD_RESULT.getDesc() : BarcodeFlowEnum.UPDATE_RESULT.getDesc());
        sampleFlow.setOperator(user.getNickname());
        sampleFlow.setOperatorId(user.getUserId());
        String content;
        if (StringUtils.isBlank(compare)) {
            content = String.format("%s",
                    isAdd ? BarcodeFlowEnum.ADD_RESULT.getDesc() : BarcodeFlowEnum.UPDATE_RESULT.getDesc());
        } else {
            content = String.format("%s:%s",
                    isAdd ? BarcodeFlowEnum.ADD_RESULT.getDesc() : BarcodeFlowEnum.UPDATE_RESULT.getDesc(), compare);
        }

        sampleFlow.setContent(content);

        sampleFlowService.addSampleFlow(sampleFlow);
    }

    @Override
    public SpecialtySampleResultTemplateDto
    selectResultTemplateDto(SpecialtySampleResultTemplateTypeEnum templateTypeEnum, long specialtySampleId) {
        // 所有结果
        List<SpecialtySampleResultDto> specialtySampleResults = this.selectBySpecialtySampleId(specialtySampleId);

        SpecialtySampleResultTemplateDto target = null;
        if (Objects.equals(templateTypeEnum, SpecialtySampleResultTemplateTypeEnum.SPECIALTY_STANDARD)) {
            target = new SpecialtySampleResultStandardDto(specialtySampleResults);
        } else if (Objects.equals(templateTypeEnum, SpecialtySampleResultTemplateTypeEnum.SPECIALTY_TYPE_ONE)) {
            target = new SpecialtySampleResultTypeOneDto(specialtySampleResults);
        } else if (Objects.equals(templateTypeEnum, SpecialtySampleResultTemplateTypeEnum.SPECIALTY_TYPE_THREE)) {
            target = new SpecialtySampleResultTypeThreeDto(specialtySampleResults);
        }

        return target;
    }

    @Override
    public SpecialtySampleResultTemplateTypeEnum selectResultTemplate(SpecialtySampleDto dto) {
        return selectResultTemplateByTestItemId(dto.getTestItemId());
    }

    @Override
    public SpecialtySampleResultTemplateTypeEnum selectResultTemplateByTestItemId(long testItemId) {
        final TestItemDto testItem = testItemService.selectByTestItemId(testItemId);
        if (Objects.isNull(testItem)) {
            throw new IllegalStateException("检验项目不存在");
        }

        // 使用结果模板 默认标准版本
        SpecialtySampleResultTemplateTypeEnum resultTemplateType = SpecialtySampleResultTemplateTypeEnum.SPECIALTY_STANDARD;

        final String property = environment.getProperty("specialty.template." + testItem.getTestItemCode());

        if (StringUtils.isNotBlank(property)) {
            resultTemplateType = EnumUtils.getEnum(SpecialtySampleResultTemplateTypeEnum.class, property);
            if (Objects.isNull(resultTemplateType)) {
                throw new IllegalStateException(String.format("结果模版配置错误，无法识别 [%s]", property));
            }
            return resultTemplateType;
        }

        // 特检样本 检验项目 绑定的报告单模板
        ReportTemplateBindDto reportTemplateBindDto = reportTemplateBindService.selectByBizId(testItem.getTestItemId());
        if (Objects.nonNull(reportTemplateBindDto)) {
            resultTemplateType = SpecialtySampleResultTemplateTypeEnum
                    .getStatusByTemplateCode(reportTemplateBindDto.getReportTemplateCode());
        }

        return resultTemplateType;
    }

}
