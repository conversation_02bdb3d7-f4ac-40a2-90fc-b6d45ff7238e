package com.labway.lims.specialty.service.chain.check.one;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;

/**
 * 一审 流水
 * 
 * <AUTHOR>
 * @since 2023/5/4 11:18
 */
@Slf4j
@Component
public class SpecialtyOneCheckFlowCommand implements Command {

    @DubboReference
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public boolean execute(Context context) throws Exception {
        final SpecialtyOneCheckContext from = SpecialtyOneCheckContext.from(context);
        final List<SpecialtySampleDto> specialtySampleList = from.getSpecialtySampleList();
        final LoginUserHandler.User user = from.getUser();

        LinkedList<Long> genIds = snowflakeService.genIds(specialtySampleList.size());
        for (SpecialtySampleDto sampleDto : specialtySampleList) {

            SampleFlowDto sampleFlow = new SampleFlowDto();
            sampleFlow.setSampleFlowId(genIds.pop());
            sampleFlow.setApplyId(sampleDto.getApplyId());
            sampleFlow.setApplySampleId(sampleDto.getApplySampleId());
            sampleFlow.setBarcode(sampleDto.getBarcode());
            sampleFlow.setOperateCode(BarcodeFlowEnum.ONE_CHECK.name());
            sampleFlow.setOperateName(BarcodeFlowEnum.ONE_CHECK.getDesc());
            sampleFlow.setOperator(user.getNickname());
            sampleFlow.setOperatorId(user.getUserId());
            sampleFlow.setContent("一审");

            sampleFlowService.addSampleFlow(sampleFlow);
        }

        return CONTINUE_PROCESSING;
    }
}
