package com.labway.lims.specialty.service.chain.check.one;

import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.api.service.SpecialtySampleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 一审 参数检验
 * 
 * <AUTHOR>
 * @since 2023/5/4 10:09
 */
@Slf4j
@Component
public class SpecialtyOneCheckParamCheckCommand implements Command {
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;
    @Resource
    private SpecialtySampleService specialtySampleService;

    @Override
    public boolean execute(Context context) throws Exception {
        final SpecialtyOneCheckContext from = SpecialtyOneCheckContext.from(context);
        final Set<Long> specialtySampleIds = from.getSpecialtySampleIds();

        // 对应选中 特检样本
        final List<SpecialtySampleDto> specialtySampleDtos =
            specialtySampleService.selectBySpecialtySampleIds(specialtySampleIds);

        final Set<Long> selectSpecialtySampleIds =
            specialtySampleDtos.stream().map(SpecialtySampleDto::getSpecialtySampleId).collect(Collectors.toSet());

        if (specialtySampleIds.stream().anyMatch(x -> !selectSpecialtySampleIds.contains(x))) {
            throw new LimsException("存在无效特检样本");
        }
        // 对应申请单样本ids
        final Set<Long> applyIds =
            specialtySampleDtos.stream().map(SpecialtySampleDto::getApplyId).collect(Collectors.toSet());
        final List<ApplyDto> applyDtos = applyService.selectByApplyIds(applyIds);
        Map<Long, ApplyDto> applyDtoByApplyId =
            applyDtos.stream().collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity()));

        if (applyIds.stream().anyMatch(x -> !applyDtoByApplyId.containsKey(x))) {
            throw new LimsException("已选数据存在对应申请单不存在");
        }

        // 对应申请单样本ids
        final Set<Long> applySampleIds =
            specialtySampleDtos.stream().map(SpecialtySampleDto::getApplySampleId).collect(Collectors.toSet());

        final List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleIds);

        if (applySampleDtos.stream()
            .anyMatch(obj -> !Objects.equals(obj.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode()))) {
            throw new LimsException("已选数据存在一审或已审样本，不可一审");
        }

        for (Long applySampleId : applySampleIds) {

            if (applySampleService.isDisabled(applySampleId)) {
                throw new LimsException(String.format(" [%s] 对应样本已禁用", applySampleId));
            }

            if (applySampleService.isTerminate(applySampleId)) {
                throw new LimsException(String.format(" [%s] 对应样本已经终止检验", applySampleId));
            }
        }

        from.put(SpecialtyOneCheckContext.SPECIALTY_SAMPLE, specialtySampleDtos);
        from.put(SpecialtyOneCheckContext.APPLY, applyDtos);
        return CONTINUE_PROCESSING;
    }
}
