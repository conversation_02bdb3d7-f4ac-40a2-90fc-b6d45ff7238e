package com.labway.lims.specialty.service.chain.check.one;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleAuditDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.service.chain.StopWatchContext;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.List;
import java.util.Set;

/**
 * 一审信息内容
 *
 * <AUTHOR>
 * @since 2023/5/4 9:55
 */
@Getter
@Setter
public class SpecialtyOneCheckContext extends StopWatchContext {

    /**
     * 特检 样本ids
     */
    private Set<Long> specialtySampleIds;
    /**
     * 操作用户
     */
    private LoginUserHandler.User user;

    /**
     * 获取 信息参数 从上下文中
     */
    public static SpecialtyOneCheckContext from(Context context) {
        return (SpecialtyOneCheckContext)context;
    }

    // 特检样本
    public static final String SPECIALTY_SAMPLE = "SPECIALTY_SAMPLE_" + IdUtil.objectId();

    public static final String SAMPLE_AUDTI_DTO = "SAMPLE_AUDTI_DTO" + IdUtil.objectId();

    public SpecialtySampleAuditDto getAuditDto() {
        return (SpecialtySampleAuditDto) get(SAMPLE_AUDTI_DTO);
    }

    public List<SpecialtySampleDto> getSpecialtySampleList() {
        return (List<SpecialtySampleDto>)get(SPECIALTY_SAMPLE);
    }

    // 申请单
    public static final String APPLY = "APPLY_" + IdUtil.objectId();

    public List<ApplyDto> getApplyDtoList() {
        return (List<ApplyDto>)get(APPLY);
    }

    @Override
    protected String getWatcherName() {
        return "特检一审";
    }
}
