package com.labway.lims.specialty.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ReportTemplateBindTypeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleEventDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.base.api.dto.ReportTemplateBindDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.FinanceSampleLockService;
import com.labway.lims.base.api.service.ReportTemplateBindService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.specialty.api.dto.SelectSpecialtySampleDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleAuditDto;
import com.labway.lims.specialty.api.dto.SpecialtySampleDto;
import com.labway.lims.specialty.api.service.SpecialtySampleResultService;
import com.labway.lims.specialty.api.service.SpecialtySampleService;
import com.labway.lims.specialty.mapstruct.SpecialtySampleConverter;
import com.labway.lims.specialty.vo.CancelCheckRequestVo;
import com.labway.lims.specialty.vo.SpecialtySampleAuditVo;
import com.labway.lims.specialty.vo.SpecialtySampleListRequestVo;
import com.labway.lims.specialty.vo.SpecialtySampleListResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.labway.lims.apply.api.dto.ApplySampleEventDto.SAMPLE_CHANGE_EXCHANGE;
import static com.labway.lims.apply.api.dto.ApplySampleEventDto.SAMPLE_CHANGE_KEY;

/**
 * 特检样本 API
 */
@Slf4j
@RestController
@RequestMapping("/specialty-sample")
public class SpecialtySampleController extends BaseController {
    @Resource
    private SpecialtySampleService specialtySampleService;
    @Resource
    private SpecialtySampleResultService specialtySampleResultService;
    @Resource
    private SpecialtySampleConverter specialtySampleConverter;

    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private SampleReportService sampleReportService;

    @DubboReference
    private ReportTemplateBindService reportTemplateBindService;

    @DubboReference
    private UserService userService;
    @DubboReference
    private FinanceSampleLockService financeSampleLockService;
    @DubboReference
    private RabbitMQService rabbitMQService;
    @Resource
    private EnvDetector envDetector;

    /**
     * 列表查询
     */
    @PostMapping("/list")
    public Object specialtySampleList(@RequestBody SpecialtySampleListRequestVo vo) {
        SelectSpecialtySampleDto dto = specialtySampleConverter.selectSpecialtySampleDtoFromRequestVo(vo);
        String sampleStatus = vo.getSampleStatus();
        final LoginUserHandler.User user = LoginUserHandler.get();
        dto.setOrgId(user.getOrgId());
        dto.setGroupId(user.getGroupId());
        // 对应 特检 样本
        final List<SpecialtySampleDto> specialtySampleDtos =
            specialtySampleService.selectBySelectSpecialtySampleDto(dto);
        if (CollectionUtils.isEmpty(specialtySampleDtos)) {
            return Collections.emptyList();
        }

        // 对应申请单ids
        final Set<Long> applyIds =
            specialtySampleDtos.stream().map(SpecialtySampleDto::getApplyId).collect(Collectors.toSet());

        // 申请单样本ids
        final Set<Long> applySampleIds =
            specialtySampleDtos.stream().map(SpecialtySampleDto::getApplySampleId).collect(Collectors.toSet());

        final List<ApplyDto> applyDtos = applyService.selectByApplyIds(applyIds);
        Map<Long, ApplyDto> applyByApplyId =
            applyDtos.stream().collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity()));

        // 对应 申请单样本信息
        final List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleIds);
        Map<Long, ApplySampleDto> applySampleByApplySampleId =
            applySampleDtos.stream().collect(Collectors.toMap(ApplySampleDto::getApplySampleId, Function.identity()));

        // 对应已审申请单样本
        List<Long> auditApplySampleId =
            applySampleDtos.stream().filter(obj -> Objects.equals(obj.getStatus(), SampleStatusEnum.AUDIT.getCode()))
                .map(ApplySampleDto::getApplySampleId).collect(Collectors.toList());

        // 对应报告项目
        final List<SampleReportDto> sampleReportDtoList =
            sampleReportService.selectByApplySampleIds(auditApplySampleId);
        Map<Long, SampleReportDto> reportDtoByApplySampleId = sampleReportDtoList.stream()
            .collect(Collectors.toMap(SampleReportDto::getApplySampleId, Function.identity()));

        // 申请单样本ids
        Set<Long> testItemIds =
            specialtySampleDtos.stream().map(SpecialtySampleDto::getTestItemId).collect(Collectors.toSet());

        // 检验项目 绑定的报告单模板
        List<ReportTemplateBindDto> reportTemplateBindDtos =
            reportTemplateBindService.selectByBizIdsAndBindType(testItemIds, ReportTemplateBindTypeEnum.TEST_ITEM);

        // key: 检验项目id value:绑定模板
        Map<Long, ReportTemplateBindDto> reportTemplateByTestItemId = reportTemplateBindDtos.stream()
            .collect(Collectors.toMap(ReportTemplateBindDto::getBizId, v -> v, (a, b) -> a));

        List<SpecialtySampleListResponseVo> targetList = Lists.newArrayListWithCapacity(specialtySampleDtos.size());

        specialtySampleDtos.forEach(item -> {
            SpecialtySampleListResponseVo temp =
                JSON.parseObject(JSON.toJSONString(item), SpecialtySampleListResponseVo.class);

            // 对应申请单信息
            ApplyDto applyDto = applyByApplyId.get(item.getApplyId());
            if (Objects.nonNull(applyDto)) {
                temp.setPatientName(applyDto.getPatientName());
                temp.setApplyType(applyDto.getApplyTypeName());
            }

            // 对应申请单样本信息
            ApplySampleDto applySampleDto = applySampleByApplySampleId.get(item.getApplySampleId());
            if (Objects.nonNull(applySampleDto)) {
                temp.setIsPrint(applySampleDto.getIsPrint());
                temp.setStatus(applySampleDto.getStatus());
                temp.setRecordDate(applySampleDto.getCreateDate());
                temp.setUrgent(applySampleDto.getUrgent());
                temp.setTwoPickDate(applySampleDto.getTwoPickDate());
            }

            // 对应报告
            SampleReportDto sampleReportDto = reportDtoByApplySampleId.get(item.getApplySampleId());
            if (Objects.nonNull(sampleReportDto)) {
                temp.setFileType(sampleReportDto.getFileType());
                temp.setUrl(sampleReportDto.getUrl());
            }

            // 查找模版
            temp.setResultTemplateType(specialtySampleResultService.selectResultTemplate(item).getCode());

            if (filterByStatus(sampleStatus, temp)) {
                return;
            }

            targetList.add(temp);
        });

        // 二次分拣时间 升序
        return targetList.stream()
            .sorted(Comparator.comparing(o -> ObjectUtils.defaultIfNull(o.getTwoPickDate(), new Date())))
            .collect(Collectors.toList());
    }

    /**
     * 一审
     */
    @PostMapping("/one-check")
    public Object oneCheck(@RequestBody SpecialtySampleAuditVo vo) {

        final SpecialtySampleAuditDto dto = JSON.parseObject(JSON.toJSONString(vo), SpecialtySampleAuditDto.class);
        specialtySampleService.oneCheck(dto);

        return Collections.emptyMap();
    }

    /**
     * 二审
     */
    @PostMapping("/two-check")
    public Object twoCheck(@RequestBody Set<Long> specialtySampleIds) {

        specialtySampleService.twoCheck(specialtySampleIds);

        return Collections.emptyMap();
    }

    /**
     * 取消一审
     */
    @PostMapping("/cancel-one-check")
    public Object cancelOneCheck(@RequestBody CancelCheckRequestVo vo) {
        if (Objects.isNull(vo.getSpecialtySampleId()) || StringUtils.isAnyBlank(vo.getUsername(), vo.getPassword())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        final UserDto user = userService.selectByUsername(vo.getUsername());
        if (Objects.isNull(user)) {
            throw new IllegalArgumentException("工号或密码错误");
        }
        // 校验密码
        if (!userService.validPassword(user.getUsername(), user.getPassword(), vo.getPassword())) {
            throw new IllegalArgumentException("工号或密码错误");
        }
        // 对应 特检样本
        final SpecialtySampleDto specialtySampleDto =
            specialtySampleService.selectBySpecialtySampleId(vo.getSpecialtySampleId());
        if (Objects.isNull(specialtySampleDto)) {
            throw new LimsException("特检样本不存在");
        }

        if (!Objects.equals(specialtySampleDto.getOneCheckerId(), user.getUserId())) {
            throw new LimsException("一审人错误");
        }
        ApplyDto applyDto = applyService.selectByApplyId(specialtySampleDto.getApplyId());
        if (Objects.isNull(applyDto)) {
            throw new LimsException("对应申请单不存在");
        }
        final ApplySampleDto applySampleDto =
            applySampleService.selectByApplySampleId(specialtySampleDto.getApplySampleId());
        if (Objects.isNull(applySampleDto)) {
            throw new LimsException("特检样本对应申请单样本不存在");
        }

        if (applySampleService.isDisabled(applySampleDto.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已禁用", applySampleDto.getApplySampleId()));
        }

        if (applySampleService.isTerminate(applySampleDto.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已经终止检验", applySampleDto.getApplySampleId()));
        }

        if (BooleanUtils.isTrue(financeSampleLockService.isSampleLock(applySampleDto.getApplySampleId()))) {
            throw new IllegalStateException("该样本已经加锁，不能取消审核");
        }

        if (!Objects.equals(applySampleDto.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode())) {
            throw new LimsException("只有一审状态样本可取消一审");
        }
        // 取消一审 修改数据
        specialtySampleService.cancelOneCheck(specialtySampleDto);

        final ApplySampleEventDto event = new ApplySampleEventDto();
        event.setEvent(ApplySampleEventDto.EventType.CancelOneCheck);
        event.setOrgId(user.getOrgId());
        event.setHspOrgId(applyDto.getHspOrgId());
        event.setHspOrgCode(applyDto.getHspOrgCode());
        event.setHspOrgName(applyDto.getHspOrgName());
        event.setApplyId(applyDto.getApplyId());
        event.setApplySampleId(specialtySampleDto.getApplySampleId());
        event.setBarcode(specialtySampleDto.getBarcode());
        event.setExtras(Map.of("sampleId", String.valueOf(specialtySampleDto.getSpecialtySampleId()), "sampleNo",
            specialtySampleDto.getSampleNo(), "outBarcode", String.valueOf(applySampleDto.getOutBarcode())));

        final String json = JSON.toJSONString(event);
        rabbitMQService.convertAndSend(SAMPLE_CHANGE_EXCHANGE, SAMPLE_CHANGE_KEY, json);

        log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功", specialtySampleDto.getApplySampleId(),
            specialtySampleDto.getBarcode(), json, SAMPLE_CHANGE_EXCHANGE, SAMPLE_CHANGE_KEY);

        return Collections.emptyMap();
    }

    /**
     * 取消二审
     */
    @PostMapping("/cancel-two-check")
    public Object cancelTwoCheck(@RequestBody CancelCheckRequestVo vo) {
        if (Objects.isNull(vo.getSpecialtySampleId()) || StringUtils.isAnyBlank(vo.getUsername(), vo.getPassword())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        final UserDto user = userService.selectByUsername(vo.getUsername());
        if (Objects.isNull(user)) {
            throw new IllegalArgumentException("工号或密码错误");
        }

        // 校验密码
        if (!userService.validPassword(user.getUsername(), user.getPassword(), vo.getPassword())) {
            throw new IllegalArgumentException("工号或密码错误");
        }

        // 对应 特检样本
        final SpecialtySampleDto specialtySampleDto =
            specialtySampleService.selectBySpecialtySampleId(vo.getSpecialtySampleId());
        if (Objects.isNull(specialtySampleDto)) {
            throw new LimsException("特检样本不存在");
        }

        if (!Objects.equals(specialtySampleDto.getTwoCheckerId(), user.getUserId())) {
            throw new LimsException("二审人错误");
        }
        ApplyDto applyDto = applyService.selectByApplyId(specialtySampleDto.getApplyId());
        if (Objects.isNull(applyDto)) {
            throw new LimsException("对应申请单不存在");
        }

        final ApplySampleDto applySampleDto =
            applySampleService.selectByApplySampleId(specialtySampleDto.getApplySampleId());
        if (Objects.isNull(applySampleDto)) {
            throw new LimsException("特检样本对应申请单样本不存在");
        }

        if (applySampleService.isDisabled(applySampleDto.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已禁用", applySampleDto.getApplySampleId()));
        }

        if (applySampleService.isTerminate(applySampleDto.getApplySampleId())) {
            throw new IllegalStateException(String.format(" [%s] 对应样本已经终止检验", applySampleDto.getApplySampleId()));
        }

        if (BooleanUtils.isTrue(financeSampleLockService.isSampleLock(applySampleDto.getApplySampleId()))) {
            throw new IllegalStateException("该样本已经加锁，不能取消审核");
        }

        if (!Objects.equals(applySampleDto.getStatus(), SampleStatusEnum.AUDIT.getCode())) {
            throw new LimsException("只有已审状态样本可取消二审");
        }

        // 取消一审 修改数据
        specialtySampleService.cancelTwoCheck(specialtySampleDto);

        final ApplySampleEventDto event = new ApplySampleEventDto();
        event.setEvent(ApplySampleEventDto.EventType.CancelTwoCheck);
        event.setOrgId(user.getOrgId());
        event.setHspOrgId(applyDto.getHspOrgId());
        event.setHspOrgCode(applyDto.getHspOrgCode());
        event.setHspOrgName(applyDto.getHspOrgName());
        event.setApplyId(applyDto.getApplyId());
        event.setApplySampleId(specialtySampleDto.getApplySampleId());
        event.setBarcode(specialtySampleDto.getBarcode());
        event.setExtras(Map.of("sampleId", String.valueOf(specialtySampleDto.getSpecialtySampleId()), "sampleNo",
            specialtySampleDto.getSampleNo(), "outBarcode", String.valueOf(applySampleDto.getOutBarcode()),
            "businessCenterOrgCode", envDetector.getBusinessCenterOrgCode()));
        final String json = JSON.toJSONString(event);
        rabbitMQService.convertAndSend(SAMPLE_CHANGE_EXCHANGE, SAMPLE_CHANGE_KEY, json);

        log.info("样本 [{}] 条码 [{}] 发送消息 [{}] 到 [{}#{}] 成功", specialtySampleDto.getApplySampleId(),
            specialtySampleDto.getBarcode(), json, SAMPLE_CHANGE_EXCHANGE, SAMPLE_CHANGE_KEY);

        return Collections.emptyMap();
    }

    /**
     * 根据 样本状态 过滤数据
     */
    private boolean filterByStatus(String sampleStatus, SpecialtySampleListResponseVo temp) {
        // 终止检验数据不展示
        if (Objects.equals(temp.getStatus(), SampleStatusEnum.STOP_TEST.getCode())) {
            return true;
        }

        if (StringUtils.isBlank(sampleStatus)) {
            // 未传入 过滤数据状态
            return false;
        }
        if (Objects.equals(sampleStatus, SampleStatusEnum.NOT_AUDIT.name())
            && !Objects.equals(temp.getStatus(), SampleStatusEnum.NOT_AUDIT.getCode())) {
            // 样本状态 未审 数据 不为未审过滤掉
            return true;
        }
        if (Objects.equals(sampleStatus, SampleStatusEnum.ONE_AUDIT.name())
            && !Objects.equals(temp.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode())) {
            // 样本状态 一审 数据 不为一审过滤掉
            return true;
        }
        // 样本状态 已审 数据 不为已审过滤掉
        return Objects.equals(sampleStatus, SampleStatusEnum.AUDIT.name())
            && !Objects.equals(temp.getStatus(), SampleStatusEnum.AUDIT.getCode());
    }

}
