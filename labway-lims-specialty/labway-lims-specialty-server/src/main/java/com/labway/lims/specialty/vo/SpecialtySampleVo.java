package com.labway.lims.specialty.vo;


import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 特检样本
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class SpecialtySampleVo implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组
     */
    private String groupName;

    /**
     * 专业小组ID
     */
    private Long instrumentGroupId;

    /**
     * 专业小组名称
     */
    private String instrumentGroupName;

    /**
     * 检验日期，暂定二次分拣日期
     */
    private Date testDate;

    /**
     * 一次审核人ID
     */
    private Long oneCheckerId;

    /**
     * 一次审核
     */
    private String oneCheckerName;

    /**
     * 二次审核人名称
     */
    private String twoCheckerName;

    /**
     * 二次审核
     */
    private Long twoCheckerId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 样本备注
     */
    private String sampleRemark;

    /**
     * 结果备注
     */
    private String resultRemark;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 检验机构ID
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

}
