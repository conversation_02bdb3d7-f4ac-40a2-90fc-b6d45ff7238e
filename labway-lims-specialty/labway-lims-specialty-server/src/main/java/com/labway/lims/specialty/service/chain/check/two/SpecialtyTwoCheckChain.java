package com.labway.lims.specialty.service.chain.check.two;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 特检 一审
 *
 * <AUTHOR>
 * @since 2023/4/28 17:58
 */
@Component
public class SpecialtyTwoCheckChain extends ChainBase implements InitializingBean {

    @Resource
    private SpecialtyTwoCheckParamCheckCommand specialtyTwoCheckParamCheckCommand;
    @Resource
    private SpecialtyTwoCheckLimitCommand specialtyTwoCheckLimitCommand;
    @Resource
    private SpecialtyTwoCheckBuildReportCommand specialtyTwoCheckBuildReportCommand;

    @Resource
    private SpecialtyTwoCheckUpdateStatusDataCommand specialtyTwoCheckUpdateStatusDataCommand;
    @Resource
    private SpecialtyTwoCheckFlowCommand specialtyTwoCheckFlowCommand;
    @Resource
    private SpecialtyTwoCheckRabbitMQCommand specialtyTwoCheckRabbitMQCommand;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 检查参数
        addCommand(specialtyTwoCheckParamCheckCommand);
        // 上锁
        addCommand(specialtyTwoCheckLimitCommand);

        // 创建报告
        addCommand(specialtyTwoCheckBuildReportCommand);

        // 特检样本 二审 修改样本状态、二审人
        addCommand(specialtyTwoCheckUpdateStatusDataCommand);

        // 保存流水
        addCommand(specialtyTwoCheckFlowCommand);

        // 发送到 mq
        addCommand(specialtyTwoCheckRabbitMQCommand);

        // 完成
        addCommand(context -> PROCESSING_COMPLETE);
    }
}
