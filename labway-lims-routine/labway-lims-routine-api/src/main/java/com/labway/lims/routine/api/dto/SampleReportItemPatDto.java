package com.labway.lims.routine.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class SampleReportItemPatDto extends SampleReportItemDto{

    private String uuid;

    /**
     * 病人姓名
     */
    private String patientName;
    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 检验日期，暂定二次分拣日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date testDate;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 这个结果可能是经过格式化或计算过的
     */
    private String result;

    /**
     * 单位
     */
    private String unit;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 合并配置
     */
    private CombinedBillDTO combinedBillConfig;
}
