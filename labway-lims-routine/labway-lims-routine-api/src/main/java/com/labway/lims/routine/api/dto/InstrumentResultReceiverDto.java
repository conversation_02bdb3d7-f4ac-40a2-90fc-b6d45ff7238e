package com.labway.lims.routine.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class InstrumentResultReceiverDto implements Serializable {

    /**
     * 样本号/条码号
     */
    private String barcode;

    /**
     * 检验时间 yyyy-MM-dd HH:mm:ss
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date date;

    /**
     * 检测的仪器编码
     */
    private String instrumentCode;

    /**
     * 专业组code
     */
    private String groupCode;

    /**
     * orgId
     */
    private Long orgId;

    /**
     * 结果
     */
    private List<ResultInfo> results;


    @Getter
    @Setter
    public static class ResultInfo {
        /**
         * 名称 仪器通道编码
         */
        private String name;

        /**
         * 结果
         */
        private String result;

        /**
         * 本地图片地址 , 一般指向文件地址
         */
        private String localImage;

        /**
         * 远程图片地址 , 一般指向远程地址
         */
        private String remoteImage;
    }


}
