package com.labway.lims.routine.api.dto;

import java.io.Serializable;
import java.util.Set;

import lombok.Getter;
import lombok.Setter;

/**
 * 常规检验 取消一审、二审
 * 
 * <AUTHOR>
 * @since 2023/4/3 15:24
 */
@Getter
@Setter
public class SampleCancelAuditDto implements Serializable {
    /**
     * 样本 ID
     */
    private Set<Long> sampleIds;

    /**
     * 操作 审核状态
     *
     * @see com.labway.lims.api.enums.routine.SampleAuditStatusEnum
     */
    private String auditStatus;

}
