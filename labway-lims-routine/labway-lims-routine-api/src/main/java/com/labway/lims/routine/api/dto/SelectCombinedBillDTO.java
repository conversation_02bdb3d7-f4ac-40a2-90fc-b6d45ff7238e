package com.labway.lims.routine.api.dto;

import cn.hutool.core.lang.Assert;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@Data
public class SelectCombinedBillDTO {

    /**
     * 检验日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private LocalDate testDate;

    public Date getTestDateStart(){
        verification();
        return localDateTime2Date(testDate.atStartOfDay());
    }

    public Date getTestDateEnd(){
        verification();
        return localDateTime2Date(testDate.atStartOfDay().plusDays(1).minusSeconds(1));
    }

    private Date localDateTime2Date(LocalDateTime localDateTime){
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    private void verification(){
        Assert.notNull(testDate, "检验时间不能为空");
    }
}
