package com.labway.lims.routine.api.service;

import java.util.Collection;
import java.util.List;

import javax.annotation.Nullable;

import com.labway.lims.apply.api.dto.SampleAbnormalDto;
import com.labway.lims.routine.api.dto.SampleCriticalResultDto;
import com.labway.lims.routine.api.dto.SelectSampleCriticalDto;

/**
 * 样本危机值 Service
 *
 * <AUTHOR>
 * @since 2023/4/10 9:38
 */
public interface SampleCriticalResultService {

    /**
     * 查询 危机值 信息 根据 查询参数 SampleCriticalResultDto
     */
    List<SampleCriticalResultDto> selectBySelectSampleCriticalDto(SelectSampleCriticalDto dto);

    /**
     * 查询 危机值信息
     */
    List<SampleCriticalResultDto> selectByCriticalValueIds(Collection<Long> criticalValueIds);

    /**
     * 根据 危机值id 获取 对应危机值信息
     */
    @Nullable
    SampleCriticalResultDto selectByCriticalValueId(long criticalValueId);

    /**
     * 修改 危机值信息
     */
    void updateByCriticalValueId(SampleCriticalResultDto sampleCriticalResultDto);

    /**
     * 根据专业组查询
     */
    List<SampleCriticalResultDto> selectByGroupId(long groupId);

    /**
     * 根据样本ID和报告项目ID查询
     */
    @Nullable
    SampleCriticalResultDto selectBySampleIdAndReportItemCode(long sampleId, String reportItemCode);

    /**
     * 根据样本ID
     */
    List<SampleCriticalResultDto> selectBySampleId(long sampleId);

    /**
     * 根据样本IDs
     */
    List<SampleCriticalResultDto> selectBySampleIds(Collection<Long> sampleIds);

    /**
     * 添加危机值
     */
    long addSampleCriticalResult(SampleCriticalResultDto dto);

    /**
     * 根据ID删除危机值
     */
    void deleteByCriticalValueId(long criticalValueId);

    /**
     * 根据条件删除
     */
    void deleteBySampleIdAndReportItemId(long sampleId, long reportItemId);

    /**
     * 根据样本id 删除
     */
    void deleteBySampleIds(Collection<Long> sampleIds);

    void deleteBySampleIdAndReportItemCode(long sampleId, String reportItemCode);

    void updateByCriticalValueIds(SampleCriticalResultDto criticalResultDto, Collection<Long> updateCriIds);

    /**
     *  根据applyId去修改对应的送检机构
     * @param sampleCriticalResultDto
     */
    void updateByApplyId(SampleCriticalResultDto sampleCriticalResultDto);

    void updateByApplyIds(SampleCriticalResultDto sampleCriticalResultDto, Collection<Long> applyIds);

    void deleteByCriticalValueIds(List<Long> criticalValueIds);

    /**
     * 获取危急值超时的配置(单位：分钟)
     * @return
     */
    int getTimeoutConfig();
}
