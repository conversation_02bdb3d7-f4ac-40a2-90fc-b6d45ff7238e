package com.labway.lims.routine.api.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Collection;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class StartReTestResultDto implements Serializable {

    /**
     * 样本id
     */
    private Long sampleId;

    /**
     * 样本报告项目id
     */
    private Collection<SampleReportItemDto> reportItems;

}