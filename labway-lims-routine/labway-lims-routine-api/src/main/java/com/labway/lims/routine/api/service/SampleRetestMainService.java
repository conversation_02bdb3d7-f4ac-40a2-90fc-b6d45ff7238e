package com.labway.lims.routine.api.service;

import com.labway.lims.routine.api.dto.CountRetestResultDto;
import com.labway.lims.routine.api.dto.RetestRecordDto;
import com.labway.lims.routine.api.dto.SampleRetestMainDto;
import com.labway.lims.routine.api.dto.StartReTestDto;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/17 13:03
 */
public interface SampleRetestMainService {

    void startRetest(StartReTestDto dto);

    /**
     * 取消复查
     */
    void cancelRetest(long sampleId);

    /**
     * 取消复查
     */
    void cancelRetest(long sampleId, String reportItemCode);



    /**
     * 根据 sampleMainId 查询
     */
    List<SampleRetestMainDto> selectBySampleId(long sampleId);

    /**
     * 统计复查信息
     */
    CountRetestResultDto countRetestResultInfo(long sampleId);
    /**
     * 根据 ID 修改
     */
    boolean updateBySampleRetestMainId(SampleRetestMainDto dto);

    /**
     * 查询历史复查列表
     */
    List<RetestRecordDto> selectRetestRecord(long sampleId);

    /**
     * 根据样本ID集合查询
     */
    List<SampleRetestMainDto> selectBySampleIds(Collection<Long> sampleIds);

    /**
     * 添加主复查表
     */
    long addSampleRetestMain(SampleRetestMainDto main);

    /**
     * 根据ID删除
     */
    void deleteBySampleRetestMainId(long sampleRetestMainId);
}
