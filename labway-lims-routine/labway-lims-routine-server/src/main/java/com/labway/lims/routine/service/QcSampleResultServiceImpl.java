package com.labway.lims.routine.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.routine.api.dto.QcSampleResultDto;
import com.labway.lims.routine.api.dto.SyncQcSampleResultDto;
import com.labway.lims.routine.api.service.QcSampleResultService;
import com.labway.lims.routine.mapper.TbQcSampleResultMapper;
import com.labway.lims.routine.mapstruct.QcSampleResultConverter;
import com.labway.lims.routine.model.TbQcSampleResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 质控样本结果 service impl
 *
 * <AUTHOR>
 * @since 2023/7/5 11:22
 */
@Slf4j
@DubboService
public class QcSampleResultServiceImpl implements QcSampleResultService {

    @Resource
    private TbQcSampleResultMapper tbQcSampleResultMapper;
    @Resource
    private QcSampleResultConverter qcSampleResultConverter;

    @Override
    public List<QcSampleResultDto> selectBySampleId(long sampleId) {

        LambdaQueryWrapper<TbQcSampleResult> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbQcSampleResult::getSampleId, sampleId);
        queryWrapper.eq(TbQcSampleResult::getIsDelete, YesOrNoEnum.NO.getCode());

        return qcSampleResultConverter.qcSampleResultDtoListFromTbObj(tbQcSampleResultMapper.selectList(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncQcSampleResult(SyncQcSampleResultDto sampleResultDto) {
        // 新增
        Collection<QcSampleResultDto> needAddQcSampleResultDtos = sampleResultDto.getNeedAddQcSampleResultDtos();
        if (CollectionUtils.isNotEmpty(needAddQcSampleResultDtos)) {

            // 要添加的 物料库存
            List<TbQcSampleResult> targetList =
                qcSampleResultConverter.tbQcSampleResultListFromTbObjDto(needAddQcSampleResultDtos);

            // 数量 分区批次插入
            List<List<TbQcSampleResult>> partitionList = ListUtils.partition(targetList, 500);

            partitionList.forEach(item -> tbQcSampleResultMapper.batchAddQcQcSampleResult(item));
        }
        // 删除
        Collection<QcSampleResultDto> needDeleteSampleResultDtos = sampleResultDto.getNeedDeleteSampleResultDtos();
        if (CollectionUtils.isNotEmpty(needDeleteSampleResultDtos)) {
            tbQcSampleResultMapper.deleteBatchIds(needDeleteSampleResultDtos.stream()
                .map(QcSampleResultDto::getSampleResultId).collect(Collectors.toSet()));
        }

        // 更新
        for (QcSampleResultDto updateQcSampleResultDto : sampleResultDto.getNeedUpdateQcSampleResultDtos()) {
            tbQcSampleResultMapper
                .updateById(qcSampleResultConverter.tbQcSampleResultFromTbObjDto(updateQcSampleResultDto));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBySampleId(long sampleId) {
        LambdaUpdateWrapper<TbQcSampleResult> updateWrapper = Wrappers.lambdaUpdate();

        updateWrapper.set(TbQcSampleResult::getIsDelete, YesOrNoEnum.NO.getCode());
        updateWrapper.eq(TbQcSampleResult::getSampleId, sampleId);
        updateWrapper.eq(TbQcSampleResult::getIsDelete, YesOrNoEnum.YES.getCode());

        tbQcSampleResultMapper.update(null, updateWrapper);
    }

}
