package com.labway.lims.routine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.routine.model.TbQcSampleResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@Mapper
public interface TbQcSampleResultMapper extends BaseMapper<TbQcSampleResult> {

    /**
     * 批量 插入
     */
    void batchAddQcQcSampleResult(@Param("conditions") List<TbQcSampleResult> conditions);

}
