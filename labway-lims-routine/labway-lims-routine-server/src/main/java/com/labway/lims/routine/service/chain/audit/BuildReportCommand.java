package com.labway.lims.routine.service.chain.audit;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Dict;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.CAPdf;
import com.labway.lims.api.config.HspOrgConfig;
import com.labway.lims.api.enums.SampleReportFileTypeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.enums.routine.SampleAuditStatusEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.service.SampleImageService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.MatchBindReportDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.ReportTemplateBindDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.ReportTemplateBindService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.dto.ca.CAPdfSignDto;
import com.labway.lims.pdfreport.api.enums.SealTypeEnum;
import com.labway.lims.pdfreport.api.service.CaPdfService;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.pdfreport.api.vo.ca.CAPdfSignVo;
import com.labway.lims.pdfreport.api.vo.ca.CASealVo;
import com.labway.lims.routine.api.dto.CombinedBillDTO;
import com.labway.lims.routine.api.dto.SampleAuditDto;
import com.labway.lims.routine.api.dto.SampleDto;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.config.CombinedBillConfig;
import com.labway.lims.routine.config.SimSunFontConfig;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocumentInformation;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartPanel;
import org.jfree.chart.ChartUtils;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.axis.NumberAxis;
import org.jfree.chart.axis.NumberTickUnit;
import org.jfree.chart.labels.StandardXYItemLabelGenerator;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.chart.plot.XYPlot;
import org.jfree.chart.renderer.xy.XYLineAndShapeRenderer;
import org.jfree.data.xy.XYSeries;
import org.jfree.data.xy.XYSeriesCollection;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.awt.*;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class BuildReportCommand implements Command {

    @Value("${print-merge-report.code:DANYANG_ROUTINE}")
    private String printMergeReportCode;

    private static final List<Color> COLORS = new ArrayList<>();

    static {
        COLORS.add(Color.RED);
        COLORS.add(Color.BLUE);
        COLORS.add(Color.ORANGE);
        COLORS.add(Color.CYAN);
        COLORS.add(Color.GREEN);
        COLORS.add(Color.BLACK);
        COLORS.add(Color.MAGENTA);
    }

    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private PdfReportService pdfReportService;
    @DubboReference
    private ReportTemplateBindService reportTemplateBindService;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;
    @DubboReference
    private SystemParamService systemParamService;
    @DubboReference
    private GroupService groupService;
    @DubboReference
    private SampleImageService sampleImageService;
    @DubboReference
    private UserService userService;

    @DubboReference
    private CaPdfService caPdfService;

    @Resource
    private HspOrgConfig hspOrgConfig;

    @Resource
    private CombinedBillConfig combinedBillConfig;

    @Resource
    private SimSunFontConfig simSunFontConfig;

    @Override
    public boolean execute(Context c) throws Exception {

        AuditSampleContext context = AuditSampleContext.from(c);

        final SampleAuditDto auditVo = context.getParam();

        if (Objects.equals(auditVo.getAuditStatus(), SampleAuditStatusEnum.ONE_CHECK.name())) {
            // 一审不生成报告
            return CONTINUE_PROCESSING;
        }


        final List<SampleReportDto> sampleReports = context.getSampleReports();
        // 判断如果是上传的pdf文件 则不进行pdf构建
        if (CollectionUtils.isNotEmpty(sampleReports)
                && Objects.equals(sampleReports.iterator().next().getIsUploadPdf(), YesOrNoEnum.YES.getCode())) {
            return CONTINUE_PROCESSING;
        }


        final SampleDto sample = context.getSample();
        final LinkedList<SampleReportDto> sampleReportDtos = new LinkedList<>();

        final List<ApplySampleItemDto> sampleItems = context.getApplySampleItems();
        if (CollectionUtils.isEmpty(sampleItems)) {
            throw new IllegalStateException(String.format("条码号 [%s] 没有检验项目", sample.getBarcode()));
        }

        // 样本 仪器专业小组 对应 报告项目
        final List<InstrumentReportItemDto> instrumentReportItems =
                instrumentReportItemService.selectByInstrumentGroupId(sample.getInstrumentGroupId());
        // 对应报告项目
        List<SampleReportItemDto> sampleReportItems = context.getSampleReportItems();

        if (Objects.equals(SampleAuditStatusEnum.ONE_CHECK.name(), context.getParam().getAuditStatus())) {
            sample.setOneCheckerId(LoginUserHandler.get().getUserId());
            sample.setOneCheckerName(LoginUserHandler.get().getNickname());
            sample.setOneCheckDate(new Date());
        } else if (Objects.equals(SampleAuditStatusEnum.TWO_CHECK.name(), context.getParam().getAuditStatus())) {
            sample.setTwoCheckerId(LoginUserHandler.get().getUserId());
            sample.setTwoCheckerName(LoginUserHandler.get().getNickname());
            sample.setTwoCheckDate(new Date());
        }

        sampleReportDtos.add(buildPDF(sample, context.getApply(), context.getApplySample(), sampleReportItems,
                context.getSampleReportItemResults(), instrumentReportItems, sampleItems));

        context.put(AuditSampleContext.SAMPLE_REPORT_LIST, sampleReportDtos);

        return CONTINUE_PROCESSING;
    }

    /**
     * 常规pdf走到了这里
     *
     * @see com.labway.lims.apply.service.impl.SynthesisRoutinePdfServiceImpl#buildPDF
     */
    public SampleReportDto buildPDF(SampleDto sample, ApplyDto apply, ApplySampleDto applySample,
                                    List<SampleReportItemDto> reportItems, List<SampleResultDto> results,
                                    List<InstrumentReportItemDto> instrumentReportItems, List<ApplySampleItemDto> sampleItems) throws Exception {

        Map<String, CombinedBillDTO> reportCodeMap = combinedBillConfig.getReportCodeMap();

        final ProfessionalGroupDto group = groupService.selectByGroupId(sample.getGroupId());
        if (Objects.isNull(group)) {
            throw new IllegalStateException(String.format("专业组 [%s] 不存在", sample.getGroupName()));
        }

        final UserDto twoChecker = userService.selectByUserId(sample.getTwoCheckerId());
        if (Objects.isNull(twoChecker)) {
            throw new IllegalStateException(String.format("二次审核人 [%s] 不存在", sample.getTwoCheckerName()));
        }

        final UserDto oneChecker = userService.selectByUserId(sample.getOneCheckerId());
        if (Objects.isNull(oneChecker)) {
            throw new IllegalStateException(String.format("一次审核人 [%s] 不存在", sample.getOneCheckerName()));
        }

        final UserDto tester = userService.selectByUserId(applySample.getTesterId());
        if (Objects.isNull(tester)) {
            throw new IllegalStateException(String.format("检验者 [%s] 不存在", applySample.getTesterName()));
        }


        final PdfReportParamDto param = new PdfReportParamDto();

        param.put("sample",
                Dict.of("sampleNo", sample.getSampleNo(), "instrumentGroupName", sample.getInstrumentGroupName(),
                        "instrumentName", sample.getInstrumentName(), "testDate", sample.getTestDate(), "testerName",
                        applySample.getTesterName(), "oneCheckerName", sample.getOneCheckerName(), "twoCheckerName",
                        sample.getTwoCheckerName(), "checkerName", sample.getTwoCheckerName(), "sampleRemark",
                        applySample.getSampleRemark(), "resultRemark", applySample.getResultRemark(), "_sample",
                        Dict.parse(sample)));

        param.put("apply",
                Dict.of("masterBarcode", apply.getMasterBarcode(), "patientName", apply.getPatientName(), "patientAge",
                        apply.getPatientAge(), "samplingDate", apply.getSamplingDate(), "patientMobile",
                        apply.getPatientMobile(), "createDate", apply.getCreateDate(), "sendDoctorName",
                        apply.getSendDoctorName(), "patientCard", apply.getPatientCard(), "dept", apply.getDept(), "hspOrgName",
                        apply.getHspOrgName(), "_apply", Dict.parse(apply)));
        param.put("applySample",
                Dict.of("barcode", applySample.getBarcode(), "tubeName", applySample.getTubeName(), "sampleTypeName",
                        applySample.getSampleTypeName(), "groupName", applySample.getGroupName(), "onePickerName",
                        applySample.getOnePickerName(), "onePickDate", applySample.getOnePickDate(), "twoPickerName",
                        applySample.getTwoPickerName(), "twoPickDate", applySample.getTwoPickDate(), "_applySample",
                        Dict.parse(applySample)));
        param.put("testItems", Dict.of());
        param.put("applySampleItems", sampleItems.stream().map(Dict::parse)
                .collect(Collectors.toList()));

        // 过滤不打印的
        List<SampleReportItemDto> printItems = Lists.newArrayList();
        for (SampleReportItemDto dto : reportItems) {
            InstrumentReportItemDto itemDto = instrumentReportItems.stream()
                    .filter(e -> Objects.equals(e.getInstrumentId(), sample.getInstrumentId())
                            && Objects.equals(e.getReportItemCode(), dto.getReportItemCode()))
                    .findFirst().orElse(null);
            //查不到去专业小组去查
            if (Objects.isNull(itemDto)) {
                itemDto = instrumentReportItems.stream()
                        .filter(e -> Objects.equals(e.getReportItemCode(), dto.getReportItemCode()))
                        .findFirst().orElse(null);
            }
            if (Objects.nonNull(itemDto) && Objects.equals(itemDto.getIsPrint(), YesOrNoEnum.YES.getCode())) {
                printItems.add(dto);
            }
        }

        List<SampleReportItemDto> sortedReportItems = printItems.stream().sorted(
                        Comparator.comparing(SampleReportItemDto::getPrintSort).thenComparing(SampleReportItemDto::getReportItemId))
                .collect(Collectors.toList());

        final Map<String, List<InstrumentReportItemDto>> instrumentReportItemsMap = instrumentReportItems.stream()
                .collect(Collectors.groupingBy(InstrumentReportItemDto::getReportItemCode));

        final List<ReportItemResult> reportItemsParam = new ArrayList<>();

        //这里需要新增互认项目的处理  报告名称前缀+"*"号
        final SystemParamDto reportParams = systemParamService.selectByParamName(SystemParamNameEnum.TOGETHER_ACCEPT_REPORT_CODE.getCode(),apply.getOrgId());
        Set<String> codesMap = null;
        if (reportParams != null && StringUtils.isNotBlank(reportParams.getParamValue())) {
            //转json格式
            JSONObject paramJson = JSON.parseObject(reportParams.getParamValue());
            if (paramJson != null) {
                codesMap = paramJson.keySet();
            }
        }

        // 是否带*标记
        boolean flag = false;
        for (SampleReportItemDto reportItem : sortedReportItems) {
            final ReportItemResult dict = new ReportItemResult();
            dict.set_sampleReportItem(reportItem);
            dict.setReportItemCode(reportItem.getReportItemCode());
            dict.setTestItemCode(reportItem.getTestItemCode());
            dict.setTestItemName(reportItem.getTestItemName());
            //对报告项目做特殊处理
            if (CollectionUtils.isNotEmpty(codesMap) && codesMap.contains(reportItem.getReportItemCode())) {
                flag = true;
                dict.setReportItemName("*"+reportItem.getReportItemName());
            }else {
                dict.setReportItemName(reportItem.getReportItemName());
            }

            final SampleResultDto sampleResult =
                    results.stream().filter(e -> Objects.equals(e.getReportItemCode(), reportItem.getReportItemCode()))
                            .findFirst().orElse(null);
            if (Objects.isNull(sampleResult)) {
                continue;
            }

            dict.set_sampleResult(sampleResult);
            dict.setResult(sampleResult.getResult());
            dict.setUnit(sampleResult.getUnit());
            dict.setRange(sampleResult.getRange());
            dict.setStatus(sampleResult.getStatus());
            dict.setJudge(sampleResult.getJudge());

            if (Objects.equals(sampleResult.getJudge(), TestJudgeEnum.UP.name())) {
                dict.setUpOrDown("↑");
            } else if (Objects.equals(sampleResult.getJudge(), TestJudgeEnum.DOWN.name())) {
                dict.setUpOrDown("↓");
            } else {
                dict.setUpOrDown(StringUtils.EMPTY);
            }

            // 优先取仪器上
            final InstrumentReportItemDto instrumentReportItemDto = ObjectUtils.defaultIfNull(InstrumentReportItemDto.getInstrumentReportItemDto(instrumentReportItemsMap, sampleResult.getReportItemCode(), sampleResult.getInstrumentId(), sample.getInstrumentId()), new InstrumentReportItemDto());
            dict.set_instrumentReportItem(instrumentReportItemDto);
            dict.setMerge(reportCodeMap.get(sampleResult.getReportItemCode()));

            reportItemsParam.add(dict);
        }

        param.put("flag", flag);
        param.put("reportItems", reportItemsParam);
        param.put("instrumentNames", reportItemsParam.stream()
                .map(e -> e.get_instrumentReportItem().getInstrumentName())
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.joining(",")));

        List<String> reportItemCodes = reportItemsParam.stream().map(ReportItemResult::getReportItemCode).collect(Collectors.toList());
        boolean isMerge = StringUtils.isNotBlank(applySample.getMergeExtraInfo()) && combinedBillConfig.getReportCodes().containsAll(reportItemCodes);
        if (isMerge) {
            Map<String, List<ReportItemResult>> reportItemMap = reportItemsParam.stream().collect(Collectors.groupingBy(e -> e.getMerge().getReportGroupName()));
            // 样本图片
            param.put("sampleImages", base64(reportItemMap));

            // 提示
            Set<String> groupNames = reportItemMap.keySet();
            List<CombinedBillConfig.ReportItemTips> reportItemTips = combinedBillConfig.getReportItemTips().stream().filter(e -> groupNames.contains(e.getReportGroupName())).collect(Collectors.toList());
            param.put("tips", reportItemTips);
        }else {
            // 样本图片
            param.put("sampleImages", sampleImageService.selectSampleImageBySampleId(sample.getSampleId()).stream().map(Dict::parse)
                    .collect(Collectors.toList()));
        }
        // 签名
        param.put("signature", Dict.of(
                // 检验者
                "tester", Dict.of("name", tester.getNickname(), "cnSign", tester.getCnSign(), "enSign", tester.getEnSign(), "sign",
                        StringUtils.defaultString(tester.getCnSign(), tester.getEnSign())),
                // 一次审核人
                "oneChecker",
                Dict.of("name", oneChecker.getNickname(), "cnSign", oneChecker.getCnSign(), "enSign",
                        oneChecker.getEnSign(), "sign",
                        StringUtils.defaultString(oneChecker.getCnSign(), oneChecker.getEnSign())),
                // 二次审核人
                "twoChecker",
                Dict.of("name", twoChecker.getNickname(), "cnSign", twoChecker.getCnSign(), "enSign",
                        twoChecker.getEnSign(), "sign",
                        StringUtils.defaultString(twoChecker.getCnSign(), twoChecker.getEnSign())),
                // 批准者
                "approver", Dict.of("name", group.getApproverName(), "sign", group.getApproverSign())));

        // 会获取到多个模板，当获取到多个时，生成多个PDF，然后拼接起来
        final Map<String, List<ItemPdfMapping>> templates = getReportTemplates(sample, apply, sampleItems).stream()
                .collect(Collectors.groupingBy(ItemPdfMapping::getReportTemplateCode));

        log.info("条码 [{}] 最终获取到模板 {}", sample.getBarcode(), templates.keySet());

        final List<File> files = new ArrayList<>();
        for (var e : templates.entrySet()) {
            final List<ApplySampleItemDto> applySampleItems = e.getValue().stream().map(ItemPdfMapping::getApplySampleItem)
                    .collect(Collectors.toList());

            final PdfReportParamDto k = new PdfReportParamDto();
            k.putAll(param);

            // 当有多个模版的时候再隔离，只有一个的时候就取全部
            if (templates.size() != NumberUtils.INTEGER_ONE) {
                // 检验项目隔离
                k.put("applySampleItems", applySampleItems.stream().map(Dict::parse)
                        .collect(Collectors.toList()));
                // 结果隔离
                k.put("reportItems", reportItemsParam.stream().filter(t -> applySampleItems.stream()
                                .anyMatch(o -> Objects.equals(o.getTestItemCode(), t.getTestItemCode())))
                        .collect(Collectors.toList()));
            }

            log.info("开始使用报告单模板 [{}] 生成条码 [{}] 的报告单。 参数 [{}]", e.getKey(), sample.getBarcode(),
                    JSON.toJSONString(k));

            final File tempFile = FileUtil.createTempFile();
            try (final FileOutputStream fos = new FileOutputStream(tempFile)) {
                // 是指定的几家医院需要ca认证
                String reportCode = e.getKey();
                if (isMerge) {
                    reportCode = printMergeReportCode;
                }
                String pdfCodeBak = reportCode;
                String pdfCode = reportCode;
                boolean contains = hspOrgConfig.getHspOrgCodes().contains(apply.getHspOrgCode());
                if(contains){
                    pdfCode = "CA_" + pdfCode;
                }
                byte[] bytes;
                try {
                    bytes = pdfReportService.build(pdfCode, k);
                }catch (Exception exception){
                    if(!contains){
                        throw exception;
                    }
                    log.error("ca模板生成失败， 生成原始模板");
                    contains = false;
                    bytes = pdfReportService.build(pdfCodeBak, k);
                }
                if(contains){
                    try {
                        byte[] CABytes = createCAPDF(bytes, applySample.getTesterName(), twoChecker.getNickname());
                        if (CABytes.length > 0) {
                            bytes = CABytes;
                        }
                    } catch (Exception exception) {
                        bytes = pdfReportService.build(pdfCodeBak, k);
                        log.error("ca模板生成失败， 生成原始模板" + exception);
                    }
                }

                fos.write(bytes);
                log.info("使用报告单模板 [{}] 生成条码 [{}] 的报告单。 文件地址: {}", reportCode, sample.getBarcode(),
                        tempFile);
            }
            files.add(tempFile);
        }

        final File pdf = files.size() == NumberUtils.INTEGER_ONE ? files.iterator().next() : FileUtil.createTempFile();
        // 如果数量不是1，那么需要合并
        if (files.size() != NumberUtils.INTEGER_ONE) {
            final PDFMergerUtility merger = new PDFMergerUtility();
            for (File file : files) {
                merger.addSource(file);
            }

            final PDDocumentInformation information = new PDDocumentInformation();
            information.setKeywords(String.join(",", templates.keySet()));
            merger.setDestinationFileName(pdf.getAbsolutePath());
            merger.setDestinationDocumentInformation(information);

            try {
                merger.mergeDocuments(MemoryUsageSetting.setupTempFileOnly());
            } catch (Exception e) {
                log.error("条码 [{}] 审核时合并PDF失败", sample.getBarcode(), e);
                throw new IllegalStateException("审核时合并PDF失败", e);
            } finally {
                files.forEach(FileUtils::deleteQuietly);
            }
        }

        try (final FileInputStream fis = new FileInputStream(pdf)) {
            final SampleReportDto sr = new SampleReportDto();
            sr.setApplySampleId(applySample.getApplySampleId());
            sr.setApplyId(apply.getApplyId());
            sr.setSampleId(sample.getSampleId());
            sr.setBarcode(sample.getBarcode());
            sr.setFileType(SampleReportFileTypeEnum.PDF.name());
            sr.setUrl(huaweiObsUtils.upload(fis, MediaType.APPLICATION_PDF_VALUE));
            sr.setGroupName(applySample.getGroupName());
            sr.setGroupId(applySample.getGroupId());
            sr.setHspOrgId(apply.getHspOrgId());
            sr.setHspOrgName(apply.getHspOrgName());
            return sr;
        } finally {
            FileUtils.deleteQuietly(pdf);
        }
    }


    private ReportTemplateBindDto getReportTemplate(SampleDto sample, ApplyDto apply, List<ApplySampleItemDto> sampleItems) {
        MatchBindReportDto  matchBindReportDto = new MatchBindReportDto();
        if (CollectionUtils.isNotEmpty(sampleItems)){
            matchBindReportDto.setTestItemIds(sampleItems.stream().map(ApplySampleItemDto::getTestItemId)
                    .collect(Collectors.toList()));

        }
        matchBindReportDto.setBarcode(sample.getBarcode());
        matchBindReportDto.setInstrumentGroupId(sample.getInstrumentGroupId());
        matchBindReportDto.setInstrumentGroupName(sample.getInstrumentGroupName());
        matchBindReportDto.setHspOrgId(apply.getHspOrgId());
        matchBindReportDto.setHspOrgName(apply.getHspOrgName());
        matchBindReportDto.setGroupId(sample.getGroupId());
        return reportTemplateBindService.findMatchReportTemplate(matchBindReportDto);

    }

    /**
     * {key: 检验项目编码,value: 模板编码}
     */
    private List<ItemPdfMapping> getReportTemplates(SampleDto sample, ApplyDto apply, List<ApplySampleItemDto> sampleItems) {
        final List<ItemPdfMapping> list = new ArrayList<>();

        // 是否允许多PDF
        final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.ALLOW_SAMPLE_MULTI_PDF.getCode(), apply.getOrgId());
        // 如果不允许，那么使用一个模板
        if (Objects.isNull(param) || BooleanUtils.isNotTrue(BooleanUtils.toBoolean(StringUtils.lowerCase(param.getParamValue())))) {
            final ReportTemplateBindDto reportTemplate = getReportTemplate(sample, apply, sampleItems);
            for (ApplySampleItemDto e : sampleItems) {
                list.add(new ItemPdfMapping()
                        .setApplySampleItem(e)
                        .setReportTemplateCode(reportTemplate.getReportTemplateCode()));
            }
            return list;
        }

        // 一个项目取一个模板
        for (ApplySampleItemDto e : sampleItems) {
            list.add(new ItemPdfMapping()
                    .setApplySampleItem(e)
                    .setReportTemplateCode(getReportTemplate(sample, apply, List.of(e)).getReportTemplateCode()));
        }

        return list;
    }

    /**
     * 打印capdf
     * @param bytes  文件
     * @param testName 检验者
     * @param auditName 审核者
     * @return
     * @throws IOException
     */
    private byte[] createCAPDF(byte[] bytes, String testName, String auditName) throws IOException {
        List<CAPdfSignDto.Strategy> strategies = new ArrayList<>();

        //  所有章
        List<CASealVo> caSealVos = caPdfService.selectSeal(null);
        if (CollectionUtils.isEmpty(caSealVos)) {
            return new byte[0];
        }

        // 检验者章
        CAPdfSignDto.Strategy strategy = this.getStrategy(caSealVos, hspOrgConfig.getTest(), testName);
        if(Objects.nonNull(strategy)) {
            strategies.add(strategy);
        }

        //  审核者章
        CAPdfSignDto.Strategy strategy1 = this.getStrategy(caSealVos, hspOrgConfig.getAudit(), auditName);
        if(Objects.nonNull(strategy1)) {
            strategies.add(strategy1);
        }

        // 公章
        CAPdfSignDto.Strategy strategy2 = this.getStrategy(caSealVos, hspOrgConfig.getOffice(), "-1");
        if(Objects.nonNull(strategy2)) {
            strategies.add(strategy2);
        }

        CAPdfSignDto caPdfSignDto = new CAPdfSignDto();
        caPdfSignDto.setFile(bytes);
        caPdfSignDto.setSignedStrategy(strategies);

        CAPdfSignVo caPdfSignVo = caPdfService.pdfQuiesceSign(caPdfSignDto);

        return caPdfService.download(caPdfSignVo.getEnvelopeId(), null);
    }

    /**
     * 获取签章配置
     * @param caSealVos 签章列表
     * @param caPdf 签署位置配置
     * @param sealName 签署人
     * @return
     */
    private CAPdfSignDto.Strategy getStrategy(List<CASealVo> caSealVos, CAPdf caPdf, String sealName) {
        if (Objects.equals(sealName, "-1")) {
            sealName = caPdf.getSealName();
        }
        String finalSealName = sealName;
        CASealVo caSealVo = caSealVos.stream().filter(e -> e.getSealName().startsWith(finalSealName)).findFirst().orElse(null);
        if (Objects.nonNull(caSealVo)) {
            CAPdfSignDto.Strategy strategy = new CAPdfSignDto.Strategy();
            strategy.setSealId(caSealVo.getId());
            strategy.setStragegyType(1);
            strategy.setKeywords(caPdf.getKeyword());
            strategy.setPages("all");
            if (Objects.equals(caSealVo.getSealType(), String.valueOf(SealTypeEnum.PERSION_SEAL.getCode()))) {
                strategy.setReduction(caPdf.getReduction());
            }
            strategy.setOffsetDirectionX(caPdf.getX());
            strategy.setOffsetDirectionY(caPdf.getY());
            strategy.setIndex(0);
            return strategy;
        }
        return null;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    private static class ItemPdfMapping {
        /**
         * 项目
         */
        private ApplySampleItemDto applySampleItem;

        /**
         * 模板
         */
        private String reportTemplateCode;
    }



    public String base64(Map<String, List<ReportItemResult>> reportItemMap){
        String[] x = new String[]{"0", "30", "60", "90", "120", "160", "180"};

        // 创建数据
        final XYSeriesCollection dataset = new XYSeriesCollection( );
        dataset.getIntervalWidth();

        reportItemMap.forEach((k, v) -> {
            final XYSeries xySeries = new XYSeries(k);

            for (String columnKey : x) {
                ReportItemResult reportItemResult = v.stream().filter(e -> e.getMerge().getTestTime().equals(columnKey)).findFirst().orElse(null);
                Double y;
                if(Objects.nonNull(reportItemResult)){
                    try {
                        y = Double.parseDouble(reportItemResult.getResult());
                        xySeries.add(Double.parseDouble(columnKey), y);
                    }catch (Exception e){
                        log.error("打印值类型转换异常，已过滤{}", reportItemResult.getResult());
                    }
                }
            }
            dataset.addSeries(xySeries);
        });

        // 创建JFreeChart对象
        JFreeChart chart = ChartFactory.createXYAreaChart(
                "", // 图标题
                "", // x轴标题
                "", // y轴标题
                dataset, //数据集
                PlotOrientation.VERTICAL, true, true, false);
        chart.getLegend().setItemFont(getFont(18));
        ChartPanel chartPanel = new ChartPanel(chart);
        chart.setBackgroundPaint(Color.WHITE);
        chartPanel.setPreferredSize(new java.awt.Dimension(560, 367));

        NumberAxis xAxis = new NumberAxis();
        xAxis.setTickUnit(new NumberTickUnit(30));
        xAxis.setAutoRangeIncludesZero(false);
        xAxis.setAutoRangeStickyZero(false);
        xAxis.setAxisLinePaint(Color.BLACK);
        xAxis.setAxisLineStroke(new BasicStroke(3.0f));

        NumberAxis yAxis = new NumberAxis();
        yAxis.setAutoRangeIncludesZero(false);
        yAxis.setAutoRangeStickyZero(false);
        yAxis.setAxisLinePaint(Color.BLACK);
        yAxis.setAxisLineStroke(new BasicStroke(3.0f));


        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        final XYPlot plot = chart.getXYPlot();
        plot.setOutlinePaint(new Color(0, 0, 0, 0));
        plot.setBackgroundPaint(new Color(0, 0, 0, 0));
        plot.setDomainAxis(xAxis);
        plot.setRangeAxis(yAxis);

        float[] dashes = { 6F, 6F };
        BasicStroke brokenLine = new BasicStroke(1.0F, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND, 1.0F, dashes, 0.0F);
        plot.setRangeGridlinePaint(Color.BLACK);
        plot.setRangeGridlineStroke(brokenLine);
        plot.setDomainGridlinesVisible(true);

        XYLineAndShapeRenderer renderer = new XYLineAndShapeRenderer();
        renderer.setDefaultItemLabelGenerator( new StandardXYItemLabelGenerator( " {2} " ));
        renderer.setDefaultItemLabelsVisible(true);
        for (int i = 0; i < COLORS.size(); i++) {
            renderer.setSeriesPaint(i, COLORS.get(i));
            renderer.setSeriesStroke(i, new BasicStroke(3.0f));
            renderer.setSeriesItemLabelsVisible(i, true);
            renderer.setSeriesShapesVisible(i, true);
        }
        plot.setRenderer(renderer);
        try {
            ChartUtils.writeChartAsPNG(bos, chart, 700, 300);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        byte[] bytes = bos.toByteArray();
        return Base64.getEncoder().encodeToString(bytes);
    }

    @Data
    public static class ReportItemResult implements Serializable {
        private SampleReportItemDto _sampleReportItem;
        private String reportItemCode;
        private String reportItemName;
        private String testItemCode;
        private String testItemName;
        private SampleResultDto _sampleResult;
        private String result;
        private String unit;
        private String range;
        private Integer status;
        private String judge;
        private CombinedBillDTO merge;
        private String upOrDown ;
        private InstrumentReportItemDto _instrumentReportItem;
    }

    private Font getFont(int fontSize) {
        File fontFile = simSunFontConfig.getFontFile();
        try {
            Font nf = Font.createFont(Font.TRUETYPE_FONT, fontFile);
            return nf.deriveFont(Font.PLAIN, 18f);
        }catch (Exception e){
            log.error("字体获取失败， 使用默认字体");
        }
        return new Font("宋体", Font.PLAIN, fontSize);
    }
}
