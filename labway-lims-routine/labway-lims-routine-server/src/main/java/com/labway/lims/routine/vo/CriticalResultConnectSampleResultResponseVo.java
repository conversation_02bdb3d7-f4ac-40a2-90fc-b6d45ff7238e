package com.labway.lims.routine.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/4/11 17:37
 */
@Getter
@Setter
public class CriticalResultConnectSampleResultResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 结果
     */
    private String resultValue;
    /**
     * 结果时间
     */
    private Date resultDate;
    /**
     * 结果值类型
     */
    private String resultTypeName;
    /**
     * 结果来源
     */
    private String resultFromName;
}
