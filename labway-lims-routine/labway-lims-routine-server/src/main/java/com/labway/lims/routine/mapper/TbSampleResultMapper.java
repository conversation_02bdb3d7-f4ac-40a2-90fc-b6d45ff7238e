package com.labway.lims.routine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.routine.api.dto.ReceiveMeiBiaoResultDTO;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.model.TbSampleResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Mapper
public interface TbSampleResultMapper extends BaseMapper<TbSampleResult> {

    int updateBySampleResultIds(@Param("sampleResult") SampleResultDto sampleResult,
                                @Param("sampleResultIds") Collection<Long> sampleResultIds);

    void addBatch(@Param("sampleResults") Collection<SampleResultDto> sampleResults);

    /**
     * 根据ID查询 忽略 is_delete 字段
     */
    TbSampleResult selectBySampleResultIdIgnoreDelete(@Param("sampleResultId") long sampleResultId,
                                                      @Param("sampleId") long sampleId);


    List<TbSampleResult> selectBySampleIdsAndReportItemIds(@Param("results") List<ReceiveMeiBiaoResultDTO> results);

    /**
     * 根据ID查询 忽略 is_delete 字段
     */
    List<TbSampleResult> selectIgnoreDelete(@Param("sampleIds") Collection<Long> sampleIds);
}
