package com.labway.lims.routine.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <pre>
 * MissReportItemNumTips
 * 缺项的数量提示(全、-1、-2、-3、-4、-5、缺)
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/10/18 9:50
 */
@Getter
@AllArgsConstructor
public enum MissReportItemNumTips {

    ALL("全"),
    MISS("缺"),
    MISS_COUNT("-"),

    ;

    private final String tips;

    public static final Integer THRESHOLD = 5;

    public static String getTips(long missCount) {
        if (missCount > THRESHOLD) {
            return MISS.getTips(); // 缺
        } else if (missCount == 0) {
            return ALL.getTips(); // 全
        } else {
            return MISS_COUNT.getTips() + missCount; // -1、-2、-3、-4、-5
        }
    }

}
