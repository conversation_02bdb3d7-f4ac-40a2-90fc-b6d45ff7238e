package com.labway.lims.routine.service.chain.pick.two;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 二次分拣
 */
@Slf4j
@Component
public class TwoPickChain extends ChainBase implements InitializingBean {
    @Resource
    private TwoPickCheckParamCommand twoPickCheckParamCommand;
    @Resource
    private TwoPickCommonPhraseCommand twoPickCommonPhraseCommand;
    @Resource
    private TwoPickOriginalResultCommand twoPickOriginalResultCommand;
    @Resource
    private TwoPickFillInfoCommand twoPickFillInfoCommand;
    @Resource
    private TwoPickSaveInfoCommand twoPickSaveInfoCommand;
    @Resource
    private TwoPickResultMarkCommand twoPickResultMarkCommand;
    @Resource
    private TwoPickRecalculateCommand twoPickRecalculateCommand;

    @Override
    public void afterPropertiesSet() throws Exception {

        // 校验参数
        addCommand(twoPickCheckParamCommand);

        // 填充信息
        addCommand(twoPickFillInfoCommand);

        //添加带出项目
        addCommand(twoPickRecalculateCommand);

        // 保存
        addCommand(twoPickSaveInfoCommand);

        // 结果标记 , 之所以在常用短语前面，是因为常用短语会修改结果会重新覆盖掉
        addCommand(twoPickResultMarkCommand);

        // 原始结果
        addCommand(twoPickOriginalResultCommand);

        // 常用短语
        addCommand(twoPickCommonPhraseCommand);

        // 完成
        addCommand(c -> PROCESSING_COMPLETE);
    }
}
