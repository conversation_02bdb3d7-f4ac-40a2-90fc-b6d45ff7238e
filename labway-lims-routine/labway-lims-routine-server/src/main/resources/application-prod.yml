mysql:
  master:
    host: db21094d9c2847c692172e06e3f0864cin03.internal.cn-east-3.postgresql.rds.myhuaweicloud.com
    port: 5432
  username: labway_lims
  password: <PERSON><PERSON>(fSBeXj9bMY6rGRNtTO2vv08Met80qiB1)

# HikariCP连接池配置 - 用于ShardingSphere数据源
spring:
  shardingsphere:
    datasource:
      master:
        # HikariCP连接池配置
        minimum-idle: 15                    # 最小空闲连接数(routine服务查询较多)
        maximum-pool-size: 60               # 最大连接池大小
        connection-timeout: 20000           # 连接超时时间(毫秒)
        idle-timeout: 300000                # 空闲连接超时时间(5分钟)
        max-lifetime: 1200000               # 连接最大生命周期(20分钟)
        leak-detection-threshold: 60000     # 连接泄漏检测阈值(1分钟)
        # 连接测试配置
        connection-test-query: SELECT 1
        validation-timeout: 3000            # 连接验证超时时间
        # 性能优化配置
        auto-commit: true                   # 自动提交
        read-only: false                    # 非只读
        # 连接池名称
        pool-name: <PERSON><PERSON>Pool-LIMS-Routine
