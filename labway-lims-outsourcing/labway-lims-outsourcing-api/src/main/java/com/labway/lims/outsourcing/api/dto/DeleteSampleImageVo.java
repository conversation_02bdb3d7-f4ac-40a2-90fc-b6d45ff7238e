package com.labway.lims.outsourcing.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * DeleteSampleImageVo
 * 删除样本图片
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/28 9:57
 */
@Getter
@Setter
public class DeleteSampleImageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 图片id
     */
    private Long sampleImageId;

}
