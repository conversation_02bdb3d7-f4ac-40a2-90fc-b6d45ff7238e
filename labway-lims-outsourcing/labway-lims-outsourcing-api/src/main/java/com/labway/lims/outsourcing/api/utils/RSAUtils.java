package com.labway.lims.outsourcing.api.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class RSAUtils {
    /**
     * 生成的公钥(给前端)
     */
    public static final String PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCLZWe6VDMsfzBEVh8+A/5SCIDmJ1uJkQ+pm3tcHWDFMRpsoX37y5XfuaEFTo8ZkSnGix0vkWMFA3tQYd2q0n7K5lHHxJcfpMFyoIvtLytuIitpEIi8iiyr+h3PelGYHAVc/kbpYML34CgpBN/siz7ktNApRXTJpDJu8V6bURX/oQIDAQAB";
    /**
     * 生成的私钥
     */
    public static final String PRIVATE_KEY = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAItlZ7pUMyx/MERWHz4D/lIIgOYnW4mRD6mbe1wdYMUxGmyhffvLld+5oQVOjxmRKcaLHS+RYwUDe1Bh3arSfsrmUcfElx+kwXKgi+0vK24iK2kQiLyKLKv6Hc96UZgcBVz+RulgwvfgKCkE3+yLPuS00ClFdMmkMm7xXptRFf+hAgMBAAECgYA8ZmMMtKE73Zyfb6W37GkNSIv5rgIW2Jktx6YrIruklgM6ILuDGBLHzxo7P2/4BxNP6UVrMBb0vC55hP5Un0K6ozAgXE1UnjRcpJUCqi2HasHIlR/lp++gxt04kLwq5xvQd8aMg0UihMJDrvsFKIa0V3/BxQrQeeSpXREPGJGpcQJBAP3CWp+2zFE28Jf0xiKd2iU4Qqw9kqY3j5cFPELKG9Sobj+VTFcMvsCRoDtp9dnm5uYNM2MZxSW1yWqHfRuIclUCQQCMoIXeUYBAoK+x+iKqgPXvEugpa8zPzBqki5EnIh+n4Wvglxat8SEZaiiH/Ly4nG9OHbixIx6hngSXvVxO4NwdAkBT9OCs5Pr2h9nUPLztKRLKq7GLcRe6/Lj6O1YzttC8bDmFuey3BFV8OvIrliQsQvPz+zwcNqN+lYWdRFSFLoC9AkAHlC8iyCmtUfumD+EGZ2TZuZ8hfFnRaSKWc58jCMfD+HYj6ICkZzZtdDcy/P/+ww+MmjV19nqUIzwEIYR0C9JRAkBCLc9fu7vl8rmU9vy3dvmWb81PmT5XxxZggsZ+8g18e4hLtevStc+LY5djdt/TIgCGzdzoBwSdEzylTme4DbPh";

    /**
     * 秘钥
     */
    public static final String SECRET_KEY = "L6/AnaMNBGdWLAyfE6qAIQ==";

    /**
     * 生成公私钥
     *
     * @param keySize 密钥长度
     */
    public static Map<String, String> genKeyPair(int keySize) {
        Map<String, String> keyMap = new HashMap<>();
        try {
            //创建密钥对生成器
            KeyPairGenerator kpg = KeyPairGenerator.getInstance("RSA");
            kpg.initialize(keySize);
            //生成密钥对
            KeyPair keyPair = kpg.generateKeyPair();
            //公钥
            PublicKey publicKey = keyPair.getPublic();
            //私钥
            PrivateKey privateKey = keyPair.getPrivate();
            keyMap.put("publicKey", Base64.getEncoder().encodeToString(publicKey.getEncoded()));
            keyMap.put("privateKey", Base64.getEncoder().encodeToString(privateKey.getEncoded()));
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            throw new RuntimeException();
        }
        return keyMap;
    }

    /**
     * RSA 加密
     *
     * @param content 待加密字符串
     * @param key     Base64编码的公钥字符串
     */
    public static String rsaEncrypt(String content, String key) {
        byte[] keyBytes = Base64.getDecoder().decode(key);
        String encryptString = null;
        try {
            X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(keyBytes);
            PublicKey pubKey = KeyFactory.getInstance("RSA").generatePublic(x509EncodedKeySpec);
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.ENCRYPT_MODE, pubKey);
            encryptString = Base64.getEncoder().encodeToString(cipher.doFinal(content.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return encryptString;
    }

    /**
     * RSA 解密
     *
     * @param content 待解密Base64字符串
     * @param key     Base64编码的私钥字符串
     */
    public static String rsaDecrypt(String content, String key) throws Exception {
        byte[] contentBytes = Base64.getDecoder().decode(content);
        byte[] keyBytes = Base64.getDecoder().decode(key);
        String decryptString = null;

        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(keyBytes);
        PrivateKey privateKey = KeyFactory.getInstance("RSA").generatePrivate(pkcs8EncodedKeySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        decryptString = new String(cipher.doFinal(contentBytes));

        return decryptString;
    }


    /**
     * AES加密
     */
    public static String aesEncrypt(String secretKey,String content) {
        try {
            IvParameterSpec iv = new IvParameterSpec("FNQpwniuEaEriDDP".getBytes(StandardCharsets.UTF_8));
            SecretKeySpec keySpec = new SecretKeySpec(
                    secretKey.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, iv);
            byte[] bytes = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * AES解密
     */
    public static String aesDecrypt(String secretKey,String content) {
        try {
            IvParameterSpec iv = new IvParameterSpec("FNQpwniuEaEriDDP".getBytes(StandardCharsets.UTF_8));
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
            cipher.init(Cipher.DECRYPT_MODE, keySpec, iv);
            byte[] decode = Base64.getDecoder().decode(content);
            byte[] original = cipher.doFinal(decode);
            return new String(original);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) throws Exception {
//        System.out.println(genKeyPair(1024));
        //必须24位
//        String secretKey = "L6/AnaMNBGdWLAyfE6qAIQ==";
        String jsonStr="{\"code\":0,\"success\":true,\"msg\":\"\",\"data\":[{\"barcode\":\"\",\"hspOrgCode\":\"\",\"tubeType\":\"\",\"sourceType\":\"\",\"isUrgent\":\"\",\"sampleType\":\"\",\"sampleStatus\":\"\",\"appDept\":\"\",\"inpatientArea\":\"\",\"patientNo\":\"\",\"patientName\":\"\",\"sex\":\"\",\"age\":\"\",\"birthday\":\"\",\"bed\":\"\",\"diag\":\"\",\"patientCard\":\"\",\"medNumber\":\"\",\"idNumber\":\"\",\"address\":\"\",\"tel\":\"\",\"memo\":\"\",\"isForbidden\":\"\",\"sendStatus\":\"\",\"appUserName\":\"\",\"extractUserName\":\"\",\"extractDate\":\"\",\"submitUserCode\":\"\",\"submitUserName\":\"\",\"submitDate\":\"\",\"testitemSum\":\"\",\"combBarCode\":\"\",\"isReceive\":\"\",\"groupMemo\":\"\",\"receiveDate\":\"\",\"receiveUserCode\":\"\",\"receiveUserName\":\"\",\"sampleNum\":\"\",\"sendDate\":\"\",\"operDate\":\"\",\"lwHspOrgCode\":\"\",\"lwBarcode\":\"\",\"lwMainBarcode\":\"\",\"lwOperDate\":\"\",\"summaryHandoverCode\":\"\",\"origoutOrgcode\":\"\",\"origoutOrgname\":\"\",\"testApplyItem\":[{\"barcode\":\"\",\"outTestitemCode\":\"\",\"outTestitemName\":\"\",\"testitemCode\":\"\",\"testitemName\":\"\",\"hspOrgCode\":\"\",\"isFee\":\"\",\"isForbidden\":\"\",\"feeNum\":\"\",\"property1\":\"\",\"property2\":\"\",\"property3\":\"\",\"property4\":\"\",\"property5\":\"\",\"feeType\":\"\",\"showNo\":\"\",\"lwHspOrgCode\":\"\",\"lwBarcode\":\"\",\"lwMainBarcode\":\"\",\"lwOperDate\":\"\",\"lwTubeType\":\"\",\"lwSampleType\":\"\",\"lwCombBarCode\":\"\",\"labPackageId\":\"\",\"labPackageName\":\"\",\"origoutTestitemCode\":\"\",\"origoutTestitemName\":\"\",}]}]}";
        //发送方
        //  1. AES加密参数字符串
            String aesEncryptStr =  aesEncrypt(SECRET_KEY,jsonStr);
        //  2. RSA用公钥加密AES密钥
            String rsaEncryptStr = rsaEncrypt(SECRET_KEY, PUBLIC_KEY);
        System.out.println(aesEncryptStr);
        System.out.println(rsaEncryptStr);
        //  3.发送方加密后的参数字符串及aes密钥

        //接收方
        //  1。用RSA私钥解密AES密钥
        String aesKey = rsaDecrypt(rsaEncryptStr, PRIVATE_KEY);
        //  2. 解密参数字符串
        String result = aesDecrypt(aesKey,aesEncryptStr);
        System.out.println(result);



//        List<String> list = new ArrayList<>();
//        list.add("21");
//        list.add("212");
//        list.add("211");
//        String encryptString = rsaEncrypt(list.toString(), PUBLICKEY);
//        System.out.println("密文======" + encryptString);
//        String decryptString = rsaDecrypt(encryptString, PRIVATEKEY);
//        System.out.println("明文======" + decryptString);
//
//        String encrypt = encrypt("马云");
//        System.out.println("加密后:" + encrypt);
//        String decrypt = decrypt(encrypt);
//        System.out.println("解密后:" + decrypt);
    }
}
