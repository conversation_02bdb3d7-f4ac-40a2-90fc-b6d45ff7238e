package com.labway.lims.outsourcing.api.dto.his;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.PatientCardTypeEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class HisSample {

    /**
     * 外部条码
     */
    private String outBarcode;

    /**
     * 就诊类型
     */
    private String applyTypeName;

    /**
     * 就诊类型编码
     */
    private String applyTypeCode;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 性别 1男 2女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date patientBirthday;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;

    /**
     * 急诊类型
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 样本个数
     */
    private Integer sampleCount;

    /**
     * 样本性状
     */
    private String sampleProperty;

    /**
     * 样本性状code
     */
    private String samplePropertyCode;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date applyDate;

    /**
     * 采样时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date samplingDate;

    /**
     * 门诊/住院号
     */
    private String patientVisitCard;

    /**
     * 科室
     */
    private String dept;

    /***
     * 床号
     */
    private String patientBed;

    /**
     * 临床诊断
     */
    private String clinicalDiagnosis;

    /**
     * 备注
     */
    private String remark;

    /**
     * 手机号
     */
    private String patientMobile;

    /**
     * 身份证号
     */
    private String patientCard;

    /**
     * 证件类型
     *
     * @see PatientCardTypeEnum
     */
    private String patientCardType;

    /**
     * 送检医生
     */
    private String sendDoctor;

    /**
     * 地址
     */
    private String patientAddress;

    /**
     * 送检机构
     */
    private Long hspOrgId;

    /**
     * 送检机构
     */
    private String hspOrgName;
    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 检验项目
     */
    private List<HisSampleItem> hisSampleItems;
    /**
     * 原始机构编码
     */
    private String originalOrgCode;
    /**
     * 原始机构名称
     */
    private String originalOrgName;
    /**
     * 检验机构编码
     */
    private String orgCode;
    /**
     * 检验机构名称
     */
    private String orgName;
    /**
     *送检医生的身份证
     */
    private String sendDoctorCard;
}
