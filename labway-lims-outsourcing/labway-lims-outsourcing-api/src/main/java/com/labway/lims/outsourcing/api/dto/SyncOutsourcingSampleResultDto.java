package com.labway.lims.outsourcing.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Set;

/**
 * <pre>
 * SyncOutsourcingSampleResultDto
 * 同步结果
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/3/21 14:54
 */
@Getter
@Setter
public class SyncOutsourcingSampleResultDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 样本ID
     */
    private Set<Long> outsourcingSampleIds;

    /**
     * 是否忽略多出的报告项目
     */
    private boolean ignoreMissingReportItem;

    // 幂等标识
    private String idempotentCode;

    /**
     * 机构ID
     */
    private long orgId;

}
