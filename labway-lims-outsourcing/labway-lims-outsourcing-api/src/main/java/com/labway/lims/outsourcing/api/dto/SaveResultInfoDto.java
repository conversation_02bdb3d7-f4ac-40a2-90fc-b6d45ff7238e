package com.labway.lims.outsourcing.api.dto;

import com.labway.lims.api.enums.outsourcing.OutsourcingModifyTypeEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import com.labway.lims.base.api.dto.InstrumentReportItemReferenceDto;
import lombok.Getter;
import lombok.Setter;

import javax.annotation.Nullable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/4/12 14:59
 */
@Getter
@Setter
public class SaveResultInfoDto implements Serializable {
    /**
     * 结果
     */
    private String result;

    /**
     * 旧结果，null则没有
     */
    @Nullable
    private String beforeResult;

    /**
     * 检验判断
     *
     * @see TestJudgeEnum
     */
    private String judge;

    /**
     * 是否是异常值
     */
    private Boolean exception;

    /**
     * 是否是危机值
     */
    private Boolean critical;


    /**
     * 是否是复查中，如果时 false 表示复查已经结束
     */
    private Boolean retesting;

    /**
     * 当前是结果修改还是复查结果普通修改结果
     * @see OutsourcingModifyTypeEnum
     */
    private Integer modifyType;

    /**
     * 命中的参考范围，null则没有
     */
    @Nullable
    private InstrumentReportItemReferenceDto instrumentReportItemReferenceDto;
}
