package com.labway.lims.outsourcing.api.service;

import com.labway.lims.api.enums.routine.SaveResultSourceEnum;
import com.labway.lims.outsourcing.api.dto.OutsourcingSaveResultDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingStartReTestDto;
import com.labway.lims.outsourcing.api.dto.SaveResultInfoDto;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2023/5/24 14:46
 */
public interface OutsourcingSampleResultService {

    /**
     * 批量添加结果
     */
    SaveResultInfoDto saveResult(OutsourcingSaveResultDto result, SaveResultSourceEnum source);

    /**
     * 批量添加结果
     *
     */
    List<SaveResultInfoDto> saveResults(List<OutsourcingSaveResultDto> results, SaveResultSourceEnum source);

    /**
     * 外送检验开始复查
     */
    void startRetest(OutsourcingStartReTestDto dto);
}
