package com.labway.lims.outsourcing.api.dto.his;

import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class HisSampleItem {

    /**
     * outTestItemCode + '-' + testItemCode 组装的一个值
     */
    private String hisSampleItemId;

    /**
     * 外部检验项目名称
     */
    private String outTestItemName;

    /**
     * 外部检验项目编码
     */
    private String outTestItemCode;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;
    /**
     * 检验项目id
     */
    private Long testItemId;

    /**
     * 收费数量
     */
    private Integer count;

    /**
     * 是否加急 1是 0否
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 管型 code
     */
    private String tubeCode;
    /**
     * 管型
     */
    private String tubeName;

    /**
     * 样本类型
     */
    private String sampleTypeCode;

    /**
     * 样本类型
     */
    private String sampleType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 项目类型编码
     *
     * @see ItemTypeEnum
     */
    private String itemType;

    /**
     * 自定义码
     */
    private String customCode;
}
