package com.labway.lims.outsourcing.api.dto.his;

import lombok.Getter;
import lombok.Setter;

import java.util.Set;

@Getter
@Setter
public class HisParam {

    /**
     * 外部条码
     */
    protected String outBarcode;
    /**
     * 送检机构id
     */
    protected Long hspOrgId;

    /**
     * 忽略重复签收项目
     */
    private Boolean ignoreDuplicateSignItem;

    /**
     * 是否忽略检验项目限制性别校验 true:忽略 false:校验
     */
    private Boolean ignoreItemLimitSex;

    /**
     * 用来忽略重复项目的
     *
     * @see HisSampleItem#getHisSampleItemId()
     */
    private Set<String> hisSampleItemIds;
    /**
     * 签收项目类型
     */
    private String noType;

}
