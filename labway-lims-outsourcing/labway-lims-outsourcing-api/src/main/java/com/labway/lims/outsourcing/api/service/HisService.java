package com.labway.lims.outsourcing.api.service;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyInfo;
import com.labway.lims.outsourcing.api.dto.his.HisCancelSignParam;
import com.labway.lims.outsourcing.api.dto.his.HisGetParam;
import com.labway.lims.outsourcing.api.dto.his.HisSample;
import com.labway.lims.outsourcing.api.dto.his.HisSignParam;

import java.util.List;

public interface HisService {
    /**
     * 获取条码信息
     */
    HisSample get(HisGetParam get);

    /**
     * 签收
     */
    ApplyInfo sign(HisSignParam sign);

    /**
     * 签收 - 责任链
     */
    ApplyInfo signChain(HisSignParam sign);

    /**
     * 样本交接
     */
    void handoverSample(HisSignParam sign, HisSample hisSample, String orgCode, List<ApplyInfo.Sample> samples, ApplyInfo applyInfo, LoginUserHandler.User user);

    /**
     * 取消签收
     */
    void cancelSign(HisCancelSignParam cancelSignParam);

}
