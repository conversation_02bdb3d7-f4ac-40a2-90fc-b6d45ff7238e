package com.labway.lims.outsourcing.api.dto.his;

import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;

import javax.annotation.Nullable;
import java.util.Set;

@Getter
@Setter
public class HisCancelSignParam {
    /**
     * 申请单id
     */
    @NonNull
    private Set<Long> applyIds;

    /**
     * 原因名称
     */
    @Nullable
    private String reasonName;

    /**
     * 原因编码
     */
    @Nullable
    private String reasonCode;

    /**
     * 送检机构编码
     */
    @NonNull
    private String hspOrgCode;

    /**
     * 签收项目类型
     */
    private String noType;
}
