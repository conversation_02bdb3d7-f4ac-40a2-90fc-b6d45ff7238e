package com.labway.lims.outsourcing.service;

import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.outsourcing.api.dto.OutsourcingStartReTestDto;
import com.labway.lims.outsourcing.api.service.OutsourcingSampleRetestService;
import com.labway.lims.outsourcing.service.chain.retest.OutsourcingStartRetestChain;
import com.labway.lims.outsourcing.service.chain.retest.OutsourcingStartRetestContext;
import com.labway.lims.outsourcing.service.chain.retest.cancel.OutsourcingCancelRetestChain;
import com.labway.lims.outsourcing.service.chain.retest.cancel.OutsourcingCancelRetestContext;
import com.labway.lims.routine.api.service.SampleCriticalResultService;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleRetestItemService;
import com.labway.lims.routine.api.service.SampleRetestMainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2023/5/29 14:12
 */
@Slf4j
@DubboService
public class OutsourcingSampleRetestServiceImpl implements OutsourcingSampleRetestService {
    @Resource
    private OutsourcingStartRetestChain outsourcingStartRetestChain;
    @DubboReference
    private SampleRetestMainService sampleRetestMainService;
    @DubboReference
    private SampleCriticalResultService sampleCriticalResultService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private SampleRetestItemService sampleRetestItemService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private OutsourcingCancelRetestChain outsourcingCancelRetestChain;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startOutsourcingReTestResult(OutsourcingStartReTestDto dto) {

        if (Objects.isNull(dto.getOutsourcingSampleId())) {
            return;
        }

        final OutsourcingStartRetestContext context = new OutsourcingStartRetestContext();
        context.put(OutsourcingStartRetestContext.RETESET_INFO,dto);

        try {
            if (!outsourcingStartRetestChain.execute(context)) {
                throw new IllegalStateException("开始复查失败");
            }

        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new LimsException(e.getMessage(), e);
        } finally {
            log.info("开始复查 [{}] 时\n{}", dto.getOutsourcingSampleId(), context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }

    }

    @Override
    public void cancelRetest(long outsourcingSampleId) {
        final OutsourcingCancelRetestContext context = new OutsourcingCancelRetestContext();
        context.setOutsourcingSampleId(outsourcingSampleId);

        try {
            if (!outsourcingCancelRetestChain.execute(context)) {
                throw new IllegalStateException("取消复查失败");
            }
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        }

    }

    @Override
    public void cancelRetest(long outsourcingSampleId, long reportItemId) {
        final OutsourcingCancelRetestContext context = new OutsourcingCancelRetestContext();
        context.setOutsourcingSampleId(outsourcingSampleId);
        context.setReportItemId(reportItemId);

        try {
            if (!outsourcingCancelRetestChain.execute(context)) {
                throw new IllegalStateException("取消复查失败");
            }
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        }
    }


}
