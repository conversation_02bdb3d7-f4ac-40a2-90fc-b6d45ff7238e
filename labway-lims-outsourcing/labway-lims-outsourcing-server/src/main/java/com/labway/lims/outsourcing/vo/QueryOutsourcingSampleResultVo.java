package com.labway.lims.outsourcing.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class QueryOutsourcingSampleResultVo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 送检机构Id
     */
    private Long hspOrgId;

    /**
     * 外送机构id
     */
    private Long exportOrgId;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 用户姓名
     */
    private String patientName;


    /**
     * 外送日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date sendDateStart;

    /**
     * 外送日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date sendDateEnd;

    /**
     * 样本状态 待审核 待复查 待二审 已审核 终止（99）
     *
     * @see SampleStatusEnum
     */
    private Integer status;

}
