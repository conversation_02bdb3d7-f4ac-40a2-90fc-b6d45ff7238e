<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <link rel="icon" href="https://lims.labway.cn/LimsFront/favicon.ico">
    <link rel="stylesheet" href="./static/css/style.css">
    <script src="./static/js/jquery-1.11.0.min.js"></script>
    <title>报告模板修改历史</title>

</head>
<body>
<div class="app">
    <a href="./index.html">返回</a>
    <table border="1" align="">
        <thead>
        <tr>
            <td>序号</td>
            <td>ID</td>
            <td width="250">更新时间</td>
            <td>操作</td>
        </tr>
        </thead>
        <tbody>

        </tbody>
    </table>
</div>

<script>
    const history = {}

    function copy(id) {
        $.getJSON('/template/history/get?reportTemplateHistoryId=' + id, function (res) {
            const input = document.createElement('textarea');
            input.value = res.data.content
            document.body.appendChild(input);
            input.select();
            document.execCommand('copy');
            document.body.removeChild(input);

            alert('复制成功')
        })

    }


    $(function () {


        const params = Object.fromEntries(new URLSearchParams(location.search)) || {}
        const $table = $('table')
        $.getJSON('/template/history?reportTemplateId=' + (params.reportTemplateId || 0), function (res) {
            const $tbody = $table.find('tbody').empty()
            let count = 0;
            for (const item of res.data) {
                history[item.reportTemplateHistoryId] = item.content
                $tbody.append(`
                    <tr>
                        <td>${++count}</td>
                        <td>${item.reportTemplateHistoryId}</td>
                        <td width="150">${item.createDate}</td>
                        <td>
                            <a href="javascript:copy('${item.reportTemplateHistoryId}')">复制</a>
                        </td>
                    </tr>
                `)
            }
        })


    });
</script>
</body>
</html>