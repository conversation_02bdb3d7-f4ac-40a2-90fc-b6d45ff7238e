package com.labway.lims.pdfreport.service;

import cn.hutool.core.io.FileUtil;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfDocumentInfo;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.styledxmlparser.css.media.MediaDeviceDescription;
import com.itextpdf.styledxmlparser.css.media.MediaType;
import com.labway.lims.pdfreport.tags.TagCssApplierFactory;
import com.labway.lims.pdfreport.tags.TagWorkerFactory;
import org.junit.Test;

import java.io.File;

public class BarcodeTest {
    @Test
    public void test() throws Exception {
        final String html = "<html><head></head><body><h1>test</h1><barcode style='border:1px red solid' hide-text=true>123456789012</barcode></body></html>";

        final ConverterProperties properties = new ConverterProperties();
        properties.setMediaDeviceDescription(new MediaDeviceDescription(MediaType.PRINT));
        properties.setTagWorkerFactory(new TagWorkerFactory());
        properties.setCssApplierFactory(new TagCssApplierFactory());

        final File file = FileUtil.createTempFile(".pdf",true);
        try (final PdfWriter pdfWriter = new PdfWriter(file);
             final PdfDocument pdfDocument = new PdfDocument(pdfWriter)) {
            pdfWriter.setSmartMode(true);

            final PdfDocumentInfo documentInfo = pdfDocument.getDocumentInfo();
            documentInfo.setAuthor("Labway Inc.");

            try (final Document doc = HtmlConverter.convertToDocument(html, pdfDocument, properties)) {
                doc.add(new AreaBreak());
            }

        }

        System.out.println(file);
    }
}
