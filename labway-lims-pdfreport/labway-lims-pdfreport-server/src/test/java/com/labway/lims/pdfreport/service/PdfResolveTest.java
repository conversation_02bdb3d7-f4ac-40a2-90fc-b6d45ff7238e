package com.labway.lims.pdfreport.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.labway.lims.pdfreport.widgets.PageWidget;
import com.labway.lims.pdfreport.widgets.RowWidget;
import com.labway.lims.pdfreport.widgets.TableWidget;
import com.labway.lims.pdfreport.widgets.Widget;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.junit.Test;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

public class PdfResolveTest {
    @Test
    public void test() throws Exception {
        final String struct = IOUtils.toString(new FileInputStream("/Users/<USER>/Downloads/vform85257.json"),
                StandardCharsets.UTF_8);

        IOUtils.write(PageWidget.from(struct).toHtml(), new FileOutputStream("/Users/<USER>/Software/projects/labway/lims-examine/labway-lims-pdfreport/labway-lims-pdfreport-server/src/main/resources/template/test.html"),
                StandardCharsets.UTF_8);

    }


    public void parseWidget(JSONArray json, Widget parent) {
        for (int i = 0; i < json.size(); i++) {
            final JSONObject j = json.getJSONObject(i);
            final Widget widget = getWidget(j);

            JSONArray children = j.getJSONArray("widgetList");

            if (CollectionUtils.isEmpty(children)) {
                if (widget instanceof RowWidget) {
                    children = j.getJSONArray("cols");
                } else if (widget instanceof TableWidget) {
                    children = j.getJSONArray("rows");
                    if (CollectionUtils.isEmpty(children)) {
                        throw new IllegalStateException("表格不能为空");
                    }
                    children = children.getJSONObject(0).getJSONArray("cols");
                }
            }

            if (CollectionUtils.isNotEmpty(children)) {
                parseWidget(children, widget);
            }

            parent.getChildren().add(widget);
        }
    }

    public Widget getWidget(JSONObject json) {
        final String type = json.getString("type");


        final Class<? extends Widget> clazz = Widget.DEFINE_CHILDREN.get(type);
        if (Objects.isNull(clazz)) {
            throw new IllegalStateException(String.format("无法识别 %s", type));
        }

        final Widget widget = json.toJavaObject(clazz);
        widget.setOriginal(json);
        widget.setType(type);


        return widget;
    }

}
