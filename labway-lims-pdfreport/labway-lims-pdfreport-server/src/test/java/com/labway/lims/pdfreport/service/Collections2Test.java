package com.labway.lims.pdfreport.service;

import cn.hutool.core.lang.Dict;
import com.google.common.collect.Collections2;
import com.labway.lims.pdfreport.utils.Filters;
import lombok.Getter;
import lombok.Setter;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class Collections2Test {
    @Test
    public void test() {
        final List<Dict> test = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            test.add(Dict.of("position", "TYPE_ONE_FOURTH_ROW_CENTER",
                    "result", "https://obs.labway.cn/labway-lims/test/2023/06/19/13/46/5c9428f5494f4a19a497aa7a6ef4734e"));
        }

        test.add(Dict.of("position", "TYPE_ONE_FOURTH_ROW_CENTER",
                "result", "https://obs.labway.cn/labway-lims/test/2023/06/19/13/46/5c9428f5494f4a19a497aa7a6ef4734e"));

        System.out.println(Collections2.filter(test, Filters.propertyFilter("position")));
    }

    @Test
    public void test2() {
        List<User> users = new ArrayList<>();
        {
            final User user = new User();
            user.setUsername("test");
            users.add(user);
        }
        {
            final User user = new User();
            user.setUsername("test2");
            users.add(user);
        }

        final Collection<Object> username = Collections2.transform(users, Filters.propertyMap("username"));

        System.out.println(username);
    }

    @Getter
    @Setter
    static class User {
        private String username;

    }
}
