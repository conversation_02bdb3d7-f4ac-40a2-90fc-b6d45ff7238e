package com.labway.lims.pdfreport.api.dto.ca;


import lombok.*;

import javax.annotation.Nullable;
import java.io.File;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * CA pdf 静默签署
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class CAPdfSignDto extends CAKeyDto implements Serializable {

    /**
     * file 列表 < 100M  静默签署一次只能签署一份文件
     * @NotNull
     */
    private byte[] file;

    /**
     * 签署策略
     */
    private List<Strategy> signedStrategy;



    @Data
    public static class Strategy implements Serializable{
        /** 1
         * 签署策略类型
         * 默认值为 0位置签署  （注释 3-5 必填）
         * 传值    1关键字签署 （注释 6-10 必填）   会根据pdf中的文字进行解析， 签署中心在关键字中心
         * 传值    2加盖骑缝章 （注释 11-13必填）
         * 不能为空
         */
        private Integer stragegyType;

        /**  2
         * 印章id
         */
        private String sealId;

        /**
         * 个人签字比例取值范围【10-150中间的整数】，默认值为100 ，代表可以把签字框调整为目前默认签字框的10%-150%
         */
        private Integer reduction;

        /**
         * 个人签字旋转角度取值范围【0/90/180/270】，默认值为0
         */
        private Integer rotate;

        // ------------------------------------位置签署 start-----------------------------------
        /**  3
         * 位置签署字段， 相对位置，  范围 【0-1】
         */
        private BigDecimal upLeftX;

        /** 4
         * 位置签署字段， 相对位置，  范围 【0-1】
         */
        private BigDecimal upLeftY;
        /** 5
         * 页码
         */
        private Integer page;
        // ------------------------------------位置签署 end-----------------------------------


        // ------------------------------------关键字签署 start-----------------------------------
        /** 6
         * 关键字签署
         */
        private String keywords;

        /** 7
         * 关键字签署x轴偏移量， 不传默认0
         */
        private Float offsetDirectionX;

        /** 8
         * 关键字签署y轴偏移量， 不传默认0
         */
        private Float offsetDirectionY;

        /** 9
         * 关键字的搜索页数
         * 支持格式形如：'first','last','all','odd','even','1','1-5','1,3,4,7,8'
         */
        private String pages;

        /** 10
         * 关键字的索引
         * 默认0签署全部关键字，1签第一个，2签第二个，以此类推
         */
        private Integer index;
        // ------------------------------------关键字签署 end-----------------------------------

        // ------------------------------------骑缝章签署 start-----------------------------------
        /** 11
         * 骑缝章位置
         * 默认 1 文档右边加盖骑缝章
         */
        private Integer location;

        /** 12
         * 骑缝章偏移量
         * 单位为相对页框或页高的百分比（以页面左上方为PDF原点，X轴向右为正，Y轴向下为正）。可选值范围[0, 1)（相对于PDF文档页面的高度或宽度的百分比）
         */
        private Float offset;

        /** 13
         * 骑缝章加盖页码
         * ALL - 加盖所有页，ALL，ODD, EVEN，m-n
         */
        private String crevicePages;
        // ------------------------------------骑缝章签署 end-----------------------------------

    }

    public CAPdfSignDto() {
        super(null, null);
    }
}
