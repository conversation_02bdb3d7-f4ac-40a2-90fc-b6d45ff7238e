package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 物料申请单状态
 */
@AllArgsConstructor
@Getter
public enum MaterialApplyStatusEnum {
    /**
     * 全部
     */
    ALL(-1, "全部"),
    /**
     * 已提交
     */
    SUBMIT(1, "已提交"),
    /**
     * 部分出库
     */
    PART_OUT(2, "部分出库"),
    /**
     * 全部出库
     */
    ALL_OUT(3, "全部出库"),
    /**
     * 已审核
     */
    AUDIT(4, "已审核"),

    /**
     * 已退回
     */
    ROLLBACK(0, "已退回"),

    /**
     * 驳回
     */
    REJECT(99, "已驳回")
    ;


    private final Integer code;
    private final String desc;


    public static MaterialApplyStatusEnum getByCode(Integer code) {
        for (MaterialApplyStatusEnum value : MaterialApplyStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalStateException("未知的申领单状态");
    }

}
