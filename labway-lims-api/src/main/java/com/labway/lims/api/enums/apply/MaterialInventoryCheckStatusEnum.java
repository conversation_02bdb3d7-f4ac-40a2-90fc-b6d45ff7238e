package com.labway.lims.api.enums.apply;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 体检样本状态
 * 
 * <AUTHOR>
 * @since 2023/5/8 14:59
 */
@Getter
@AllArgsConstructor
public enum MaterialInventoryCheckStatusEnum {

    IN_CHECK(0, "盘点中"),

    FINISH_CHECK(1, "已盘"),

    CANCEL_CHECK(2, "已取消"),

    UNKNOWN(-1, "未知"),

    ;

    private final Integer code;

    private final String desc;

    public static MaterialInventoryCheckStatusEnum getByCode(int code) {
        for (MaterialInventoryCheckStatusEnum value : MaterialInventoryCheckStatusEnum.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value;
            }
        }
        return UNKNOWN;
    }

}
