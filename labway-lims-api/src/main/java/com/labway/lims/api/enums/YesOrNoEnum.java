package com.labway.lims.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * 是否枚举
 */
@Getter
@AllArgsConstructor
public enum YesOrNoEnum {
    /**
     * 是
     */
    YES(1, "是"),

    /**
     * 否
     */
    NO(0, "否");

    private final int code;
    private final String desc;


    public static YesOrNoEnum selectByCode(Integer code) {
        return Arrays.stream(values())
                .filter(i -> Objects.equals(i.getCode(), code)).findFirst().orElse(NO);
    }

    public static YesOrNoEnum selectByDesc(String desc) {
        return Arrays.stream(values())
                .filter(i -> Objects.equals(i.getDesc(), desc)).findFirst().orElse(NO);
    }

    public static YesOrNoEnum selectByBool(Boolean bool) {
        return BooleanUtils.isTrue(bool) ? YES : NO;
    }

}
