package com.labway.lims.api.enums.base;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 数据字段 枚举
 */
@Getter
@AllArgsConstructor
public enum DictEnum {
    /**
     * 性别
     */
    SEX("性别"),
    /**
     * 就诊类型
     */
    VISIT_TYPE("就诊类型"),
    /**
     * 急诊类型
     */
    URGENT_TYPE("急诊类型"),
    /**
     * 样本性状
     */
    SAMPLE_PROPERTY("样本性状"),
    /**
     * 样本修改原因
     */
    SAMPLE_UPDATE_CAUSE("样本修改原因"),
    /**
     * 容器类型
     */
    CONTAINER_TYPE("容器类型维护"),
    /**
     * 样本异常原因
     */
    SAMPLE_EXCEPTION_CAUSE("样本异常原因"),
    /**
     * 异常处理方式
     */
    EXCEPTION_PROCESSING_METHOD("异常处理方式"),
    /**
     * 微生物敏感度
     */
    MICROBIOLOGY_SUSCEPTIBILITY("微生物敏感度"),
    /**
     * 微生物检验方法
     */
    MICROBIOLOGY_TEST_METHOD("微生物检验方法"),
    /**
     * 微生物结果类型
     */
    MICROBIOLOGY_RESULT_FORMULA("微生物结果类型"),
    /**
     * 微生物样本结果
     */
    MICROBIOLOGY_SAMPLE_RESULT("微生物样本结果"),
    /**
     * 微生物细菌数量
     */
    MICROBIOLOGY_GERM_NUMBER("微生物细菌数量"),
    /**
     * 细菌结果备注
     */
    MICROBIOLOGY_GERM_REMARK("细菌结果备注"),

    /**
     * 检验方法
     */
    TEST_METHOD("检验方法"),
    /**
     * 销售区域
     */
    SELL_AREA("销售区域"),
    /**
     * 销售部门
     */
    SELL_DEPT("销售部门"),
    /**
     * 销售类型
     */
    SELL_TYPE("销售类型"),
    /**
     * 仪器类型
     */
    INSTRUMENT_TYPE("仪器类型"),
    /**
     * 仪器项目类型
     */
    INSTRUMENT_PROJECT_TYPE("仪器项目类型"),
    /**
     * 项目单位
     */
    PROJECT_UNIT("项目单位"),
    /**
     * 院感检验方法
     */
    INFECTION_TEST_METHOD("院感检验方法"),
    /**
     * 试管架类型
     */
    RACK_TYPE("试管架类型"),
    /**
     * 试管架环节
     */
    RACK_LINK("试管架环节"),
    /**
     * 病理类型
     */
    PATHOLOGY_TYPE("病理类型"),
    /**
     * 双输对照内容
     */
    DOUBLE_INPUT_CONTENT_CONTRAST("双输对照内容"),
    /**
     * 结果内容维护
     */
    RESULT_REMARK_CONTENT("结果内容备注"),
    /**
     * 取消签收原因
     */
    CANCEL_SIGN_CAUSE("取消签收原因"),
    /**
     * 取消复核原因
     */
    CANCEL_CHECK_CAUSE("取消复核原因维护"),
    /**
     * 终止检验原因
     */
    STOP_TEST_CAUSE("终止检验原因"),
    /**
     * 禁用项目原因
     */
    FORBIDDEN_PROJECT_CAUSE("禁用项目原因"),
    /**
     * 结果类型
     */
    RESULT_TYPE("结果类型"),
    /**
     * 院感检测标准
     */
    INFECTION_TEST_STANDARD("院感检测标准"),
    /**
     * 专业组类型
     */
    GROUP_TYPE("专业组类型"),
    /**
     * 细菌类型
     */
    WHONET_GERM_TYPE("WHONET细菌类型维护"),
    /**
     * 样本类型
     */
    SAMPLE_TYPE("样本类型"),
    /**
     * 送检机构提示消息
     */
    HSP_ORG_TIP_MSG("机构提示消息"),

    /**
     * 细菌数量
     */
    GERM_COUNT("细菌数量"),
    /**
     * 细菌备注
     */
    GERM_REMARK("细菌备注"),
    /**
     * 财务专业组
     */
    FINANCE_GROUP("财务专业组"),

    MICROBIOLOGY_SAMPLE_REMARK("微生物样本备注"),

    /**
     * 报告迟发申请
     */
    REPORT_DELAY_TYPE("报告迟发申请"),

    ;

    private final String desc;

    /**
     * 获取字典 对应信息
     */
    public static String selectDescByName(String name) {
        return Arrays.stream(DictEnum.values()).filter(obj -> Objects.equals(obj.name(), name)).map(obj -> obj.desc)
            .findFirst().orElse(name);
    }

}
