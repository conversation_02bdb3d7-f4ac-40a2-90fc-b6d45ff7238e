package com.labway.lims.api.enums.specialty;

import com.labway.lims.api.enums.PdfTemplateTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 特检样本结果 模版 类型
 * 
 * <AUTHOR>
 * @since 2023/4/24 17:01
 */
@Getter
@AllArgsConstructor
public enum SpecialtySampleResultTemplateTypeEnum {

    SPECIALTY_STANDARD(1, PdfTemplateTypeEnum.SPECIALTY_STANDARD.getCode(),
        PdfTemplateTypeEnum.SPECIALTY_STANDARD.getDesc()),

    SPECIALTY_TYPE_ONE(2, PdfTemplateTypeEnum.SPECIALTY_TYPE_ONE.getCode(),
            PdfTemplateTypeEnum.SPECIALTY_TYPE_ONE.getDesc()),

    SPECIALTY_TYPE_THREE(3, PdfTemplateTypeEnum.SPECIALTY_TYPE_THREE.getCode(),
            PdfTemplateTypeEnum.SPECIALTY_TYPE_THREE.getDesc()),

    ;

    private final int code;

    private final String reportTemplateCode;

    private final String desc;

    public static SpecialtySampleResultTemplateTypeEnum getStatusByTemplateCode(String reportTemplateCode) {
        for (SpecialtySampleResultTemplateTypeEnum statusEnum : SpecialtySampleResultTemplateTypeEnum.values()) {
            if (Objects.equals(statusEnum.getReportTemplateCode(), reportTemplateCode)) {
                return statusEnum;
            }
        }
        return SPECIALTY_STANDARD;
    }

}
