package com.labway.lims.apply.util;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.apply.api.dto.PdaApplyAndItemDto;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Component
public class PdaApplyCacheUtil {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RedisPrefix redisPrefix;

    @Resource
    private ThreadPoolConfig threadPoolConfig;

    private static final String PDA_APPLY_CACHE = "%sPDA-APPLY-CACHE:%s";

    public void setPdaApplyCache(PdaApplyAndItemDto dto) {
        final String format = String.format(PDA_APPLY_CACHE, redisPrefix.getBasePrefix(), dto.getMasterBarcode());
        stringRedisTemplate.opsForValue().set(format, JSON.toJSONString(dto), 20, TimeUnit.MINUTES);
    }


    public PdaApplyAndItemDto getPdaApplyCache(String masterBarcode) {
        final String format = String.format(PDA_APPLY_CACHE, redisPrefix.getBasePrefix(), masterBarcode);
        return JSON.parseObject(stringRedisTemplate.opsForValue().get(format), PdaApplyAndItemDto.class);
    }

    public void deletePdaApplyCache(String masterBarcode){
        threadPoolConfig.getPool().submit(() -> {
            final String format = String.format(PDA_APPLY_CACHE, redisPrefix.getBasePrefix(), masterBarcode);
            stringRedisTemplate.delete(format);
        });
    }
}
