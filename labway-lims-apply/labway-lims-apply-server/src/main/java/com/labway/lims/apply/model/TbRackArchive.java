package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 试管架归档
 * 
 * <AUTHOR>
 * @since 2023/4/13 14:49
 */
@Setter
@Getter
@TableName("tb_rack_archive")
public class TbRackArchive implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId
    private Long rackArchiveId;
    /**
     * 试管架id
     */
    private Long rackId;
    /**
     * 逻辑试管架id
     */
    private Long rackLogicId;
    /**
     * 冰箱ID
     */
    private Long refrigeratorId;
    /**
     * 有效时间从
     */
    private Date startEffectiveDate;
    /**
     * 有效时间至
     */
    private Date endEffectiveDate;
    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 1 已删除 0 未删除
     * @see YesOrNoEnum
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 创建人
     */
    private Long creatorId;
    /**
     * 创建人
     */
    private String creatorName;
    /**
     * 更新人
     */
    private Long updaterId;
    /**
     * 更新人
     */
    private String updaterName;

}
