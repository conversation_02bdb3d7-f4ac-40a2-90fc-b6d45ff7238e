package com.labway.lims.apply.service.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.labway.lims.apply.vo.utils.ExcelFileHeadMappingVo;
import com.labway.lims.apply.vo.utils.ExcelFileHeadVo;
import com.labway.lims.apply.vo.ImportPhysicalRegisterVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 解析 体检人员导入文件
 * 
 * <AUTHOR>
 * @since 2023/7/27 15:55
 */
@Slf4j
public class AnalyzeExcelFileListener extends AnalysisEventListener<Map<Integer, String>> {

    private final Map<String, Integer> reverseHeadMap = new HashMap<>();

    private final List<ExcelFileHeadMappingVo> headMappingVoList = new ArrayList<>();
    private final List<ExcelFileHeadVo> headVoList = new ArrayList<>();
    private final List<Map<Integer, String>> dataList = new ArrayList<>();
    private List<String> mappingImportHeadList = new ArrayList<>();

    public AnalyzeExcelFileListener(List<String> mappingImportHeadList) {
        this.mappingImportHeadList = mappingImportHeadList;
    }

    public Object getTargetList() {
        return Map.of("dataList", dataList, "headMappingList", headMappingVoList, "headList", headVoList);
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        //没有默认表头 则退出
        if (CollectionUtils.isEmpty(mappingImportHeadList)){
            return;
        }

        // excel 表头
        headVoList.addAll(headMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).map(obj -> {
            ExcelFileHeadVo headVo = new ExcelFileHeadVo();
            headVo.setExcelIndex(obj.getKey());
            headVo.setExcelHead(obj.getValue());
            return headVo;
        }).collect(Collectors.toList()));

        // 名称 -》 列索引 重复 取第一个
        reverseHeadMap
            .putAll(headVoList.stream().collect(Collectors.toMap(ExcelFileHeadVo::getExcelHead,
                ExcelFileHeadVo::getExcelIndex, (key1, key2) -> key1)));
        // 设置默认 映射
        mappingImportHeadList.forEach(item -> {
            ExcelFileHeadMappingVo headVo = new ExcelFileHeadMappingVo();
            headVo.setTemplateHead(item);
            Integer index = reverseHeadMap.get(item);
            if (Objects.nonNull(index)) {
                headVo.setExcelHead(item);
                headVo.setExcelIndex(index);
            }
            headMappingVoList.add(headVo);
        });

    }

    @Override
    public void invoke(Map<Integer, String> dataMap, AnalysisContext analysisContext) {
        dataList.add(dataMap);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
    }

}
