
package com.labway.lims.apply.service.chain.pick.one;

import com.labway.lims.api.RedisPrefix;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.dto.RackLogicSpaceDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.base.api.service.TestItemService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.awt.*;
import java.time.Duration;
import java.util.List;

/**
 * 寻找坐标系
 */
@Getter
@Component
@Slf4j
class OnePickFindLocationCommand implements Command {
    @Resource
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private TestItemService testItemService;
    @Resource
    private RackLogicService rackLogicService;
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean execute(Context c) throws Exception {

        final OnePickContext context = OnePickContext.from(c);
        final RackLogicDto rackLogic = context.getRackLogic();
        final ApplySampleDto applySample = context.getApplySample();

        final String rackLogicLockKey = redisPrefix.getBasePrefix() + ":ONE_PICK:RACK:" + rackLogic.getRackLogicId();
        final String applySampleLockKey =
                redisPrefix.getBasePrefix() + ":ONE_PICK:APPLY_SAMPLE:" + rackLogic.getRackLogicId();

        try {

            if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(rackLogicLockKey,
                    StringUtils.EMPTY, Duration.ofMinutes(1)))) {
                throw new IllegalStateException("试管架正在分拣中");
            }

            if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue().setIfAbsent(applySampleLockKey,
                    StringUtils.EMPTY, Duration.ofMinutes(1)))) {
                throw new IllegalStateException("条码正在分拣中");
            }

            // 二维坐标系
            final int[][] xy = new int[rackLogic.getRow()][rackLogic.getColumn()];
            int x = -1;
            int y = -1;

            // 填充坐标系
            for (RackLogicSpaceDto e : rackLogicSpaceService.selectByRackLogicId(rackLogic.getRackLogicId())) {
                xy[e.getColumn()][e.getRow()] = 1;
            }

            // 找到空闲的位置
            for (int i = 0; i < xy.length; i++) {
                for (int j = 0; j < xy[i].length; j++) {
                    if (xy[i][j] == 0) {
                        x = j;
                        y = i;
                        break;
                    }
                }

                if (x != -1) {
                    break;
                }
            }

            if (x == -1) {
                throw new IllegalStateException(String.format("试管架 [%s] 已满", rackLogic.getRackCode()));
            }

            // 获取试管架剩余的位置
            int count = 0;
            for (int[] ss : xy) {
                for (int j = 0; j < ss.length; j++) {
                    if (ss[j] == 0) {
                        count++;
                    }
                }
            }

            final RackLogicSpaceDto space = new RackLogicSpaceDto();
            space.setRackLogicId(rackLogic.getRackLogicId());
            space.setRackId(rackLogic.getRackId());
            space.setRow(x);
            space.setColumn(y);
            space.setApplySampleId(applySample.getApplySampleId());
            space.setStatus(1);

            rackLogicSpaceService.addRackLogicSpace(space);

            context.put(OnePickContext.POINT, new Point(x, y));

            // 减 1 是因为，马上就要用掉一个
            context.put(OnePickContext.SPACES, Math.max(count - 1, 0));

        } finally {
            stringRedisTemplate.delete(List.of(rackLogicLockKey, applySampleLockKey));
        }


        return CONTINUE_PROCESSING;
    }


}
