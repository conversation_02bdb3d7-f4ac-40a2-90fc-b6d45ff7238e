package com.labway.lims.apply.service.chain.apply.update;

import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.service.chain.apply.add.CheckParamCommand;
import com.labway.lims.base.api.dto.TestItemDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <pre>
 * CheckTestItemLimitSexCommand
 * 检验项目限制性别
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/10/10 16:18
 */
@Slf4j
@Component
public class CheckTestItemLimitSexCommand implements Command {

    @Resource
    private CheckParamCommand checkParamCommand;

    @Override
    public boolean execute(Context c) throws Exception {
        final UpdateApplyContext from = UpdateApplyContext.from(c);
        final TestApplyDto testApply = from.getTestApply();
        List<TestItemDto> testItems = from.getTestItems();
        List<ApplySampleItemDto> addApplySampleItems = from.getAddApplySampleItems();

        // 校验检验项目 限制性别 跳过
        if (BooleanUtils.isTrue(testApply.getIgnoreItemLimitSex())) {
            log.info("检验项目限制性别标识：{}", testApply.getIgnoreItemLimitSex());
            return CONTINUE_PROCESSING;
        }

        if (CollectionUtils.isNotEmpty(addApplySampleItems)) {
            List<Long> testItemIds = addApplySampleItems.stream().map(ApplySampleItemDto::getTestItemId).collect(Collectors.toList());
            testItems = testItems.stream().filter(e -> testItemIds.contains(e.getTestItemId())).collect(Collectors.toList());

            checkParamCommand.checkTestItemLimitSex(testApply, testItems);
        }

        return CONTINUE_PROCESSING;
    }

}
