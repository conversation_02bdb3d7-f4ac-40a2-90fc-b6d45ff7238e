package com.labway.lims.apply.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 微生物二次分拣 附件 配置
 * 
 * <AUTHOR>
 * @since 2023/8/30 20:12
 */
@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "microbiology.two-picker")
public class MicrobiologyTwoPickerAttachConfig {

    /**
     *key: 检验项目 code  value : 对应模板code
     */
    private Map<String, String> attachMap;

}
