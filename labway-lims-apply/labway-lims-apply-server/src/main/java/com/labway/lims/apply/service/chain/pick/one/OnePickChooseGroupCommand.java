
package com.labway.lims.apply.service.chain.pick.one;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.RackLogicDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.RackLogicService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.TestItemService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 判断分拣到哪个专业组
 */
@Getter
@Component
@Slf4j
public class OnePickChooseGroupCommand implements Command {
    /**
     * 专业组下没有试管架
     */
    public static final int NO_RACK_LOGIC = 1001;

    @Resource
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private TestItemService testItemService;
    @Resource
    private RackLogicService rackLogicService;
    @Resource
    private RackLogicSpaceService rackLogicSpaceService;
    @Resource
    private ApplySampleService applySampleService;
    @DubboReference
    private GroupService groupService;

    @Override
    public boolean execute(Context c) throws Exception {

        final OnePickContext context = OnePickContext.from(c);
        final List<ApplySampleItemDto> applySampleItems = context.getApplySampleItems();
        final ApplySampleDto applySample = context.getApplySample();

        final ProfessionalGroupDto splitBloodGroup = groupService.selectSplitBloodGroup(LoginUserHandler.get().getOrgId());
        final Set<Long> groupIds =
                applySampleItems.stream().map(ApplySampleItemDto::getGroupId).collect(Collectors.toSet());

        if (groupIds.stream().anyMatch(e -> Objects.equals(e, NumberUtils.LONG_ZERO))) {
            throw new IllegalStateException("申请单项目的专业组错误");
        }

        final RackLogicDto rackLogic;
        long targetGroupId = groupIds.size() == NumberUtils.INTEGER_ONE ? groupIds.iterator().next() : splitBloodGroup.getGroupId();
        final String targetGroupName;

        // 要一次分拣到哪个专业组
        if (groupIds.size() == NumberUtils.INTEGER_ONE) {
            // 如果只有一个专业组且涉及到多个外送机构，那么去分血组
            if (context.getTestItems().values().stream()
                    .filter(e -> Objects.equals(e.getEnableExport(), YesOrNoEnum.YES.getCode()))
                    .map(TestItemDto::getExportOrgId).distinct().count() > NumberUtils.INTEGER_ONE) {
                rackLogic = rackLogicService.selectAvailableRackLogic(splitBloodGroup.getGroupId(), LoginUserHandler.get().getUserId());
                targetGroupName = splitBloodGroup.getGroupName();
                targetGroupId = splitBloodGroup.getGroupId();
            } else {
                rackLogic = rackLogicService.selectAvailableRackLogic(targetGroupId, LoginUserHandler.get().getUserId());
                final long tg = targetGroupId;
                targetGroupName = applySampleItems.stream().filter(e -> Objects.equals(e.getGroupId(), tg))
                        .map(ApplySampleItemDto::getGroupName).findFirst().orElse(StringUtils.EMPTY);

                // 如果这个专业组下面的项目全是外送 那么样本设置为外送
                if (applySampleItems.stream().allMatch(k -> Objects.equals(k.getIsOutsourcing(), YesOrNoEnum.YES.getCode()))) {
                    context.put(OnePickContext.IS_OUTSOURCING, Boolean.TRUE);
                }
            }
        } else { // 分血组
            rackLogic =
                    rackLogicService.selectAvailableRackLogic(splitBloodGroup.getGroupId(), LoginUserHandler.get().getUserId());
            targetGroupName = splitBloodGroup.getGroupName();
        }

        if (Objects.isNull(rackLogic)) {
            throw new LimsCodeException(NO_RACK_LOGIC, String.format("专业组 %s 下没有可用的试管架", targetGroupName))
                    .setData(Map.of("groupId", targetGroupId, "applySampleId", applySample.getApplySampleId(), "groupName",
                            targetGroupName));
        }

        if (Objects.equals(splitBloodGroup.getGroupId(), targetGroupId)) {
            context.put(OnePickContext.GROUP, splitBloodGroup);
        } else {
            context.put(OnePickContext.GROUP, Objects.requireNonNull(groupService.selectByGroupId(targetGroupId),
                    String.format("试管架 [%s] 不存在", targetGroupName)));
        }

        context.put(OnePickContext.RACK_LOGIC, rackLogic);

        return CONTINUE_PROCESSING;
    }


}
