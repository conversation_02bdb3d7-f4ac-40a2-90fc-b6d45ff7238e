package com.labway.lims.apply.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.lims.apply.api.dto.GroupMaterialApplyDetailDto;
import com.labway.lims.apply.model.TbGroupMaterialApplyDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TbGroupMaterialApplyDetailMapper extends BaseMapper<TbGroupMaterialApplyDetail> {

    /**
     * 批量新增
     */
    void addBatch(@Param("list") List<GroupMaterialApplyDetailDto> list);
}
