package com.labway.lims.apply.controller.it8000;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleFlowDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.vo.IT8000HandleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * IT8000 扫描到条码
 */
@Slf4j
@Component
class SeenAction implements ActionStrategy {

    @Resource
    private ApplySampleService applySampleService;
    @Resource
    private SampleFlowService sampleFlowService;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object action(IT8000HandleVo vo) throws Exception {

        final String barcode = vo.getExtras().getString("barcode");
        final String position = vo.getExtras().getString("position");

        // 查询申请单样本
        final List<ApplySampleDto> applySamples = applySampleService.selectByBarcode(barcode).stream()
                // 获取没有分拣的
                .filter(e -> Objects.equals(e.getIsOnePick(), YesOrNoEnum.NO.getCode()))
                .filter(e -> Objects.equals(e.getIsTwoPick(), YesOrNoEnum.NO.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applySamples)) {
            log.error("条码 [{}] 无法记录IT8000扫描到条码的条码环节，因为没有获取到尚未分拣的申请单样本", barcode);
            return Map.of();
        }

        sampleFlowService.addSampleFlows(applySamples.stream().map(e -> SampleFlowDto.builder()
                .applyId(e.getApplyId())
                .sampleFlowId(snowflakeService.genId())
                .applySampleId(e.getApplySampleId())
                .operateCode(BarcodeFlowEnum.IT8000SEEN.name())
                .operateName(BarcodeFlowEnum.IT8000SEEN.getDesc())
                .barcode(e.getBarcode())
                .content(String.format("扫描到条码 位置信息 [%s]", position))
                .build()).collect(Collectors.toList()));

        log.info("条码 [{}] Roche 扫描到条码，记录条码环节成功", barcode);

        return Map.of();
    }


    @Override
    public IT8000HandleVo.Action action() {
        return IT8000HandleVo.Action.SEEN;
    }


}
