package com.labway.lims.apply.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.MaterialApplyStatusEnum;
import com.labway.lims.apply.api.dto.GroupMaterialApplyDetailDto;
import com.labway.lims.apply.api.dto.MaterialRejectDto;
import com.labway.lims.apply.api.service.GroupMaterialApplyDetailService;
import com.labway.lims.apply.mapper.TbGroupMaterialApplyDetailMapper;
import com.labway.lims.apply.mapper.TbGroupMaterialApplyMapper;
import com.labway.lims.apply.model.TbGroupMaterialApply;
import com.labway.lims.apply.model.TbGroupMaterialApplyDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.aop.framework.AopContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class GroupMaterialApplyDetailServiceImpl extends ServiceImpl<TbGroupMaterialApplyDetailMapper, TbGroupMaterialApplyDetail> implements GroupMaterialApplyDetailService {

    @Resource
    private TbGroupMaterialApplyDetailMapper tbGroupMaterialApplyDetailMapper;

    @Resource
    @Lazy
    private TbGroupMaterialApplyMapper tbGroupMaterialApplyMapper;

    @Override
    public List<GroupMaterialApplyDetailDto> selectByApplyNo(long orgId, String applyNo) {

        final LambdaQueryWrapper<TbGroupMaterialApplyDetail> eq = Wrappers.lambdaQuery(TbGroupMaterialApplyDetail.class)
                .eq(TbGroupMaterialApplyDetail::getApplyNo, applyNo)
                .eq(TbGroupMaterialApplyDetail::getOrgId, orgId)
                .orderByAsc(TbGroupMaterialApplyDetail::getCreateDate);

        return JSON.parseArray(JSON.toJSONString(tbGroupMaterialApplyDetailMapper.selectList(eq)), GroupMaterialApplyDetailDto.class);
    }

    @Override
    public void addBatch(List<GroupMaterialApplyDetailDto> list) {
        tbGroupMaterialApplyDetailMapper.addBatch(list);
    }

    @Override
    public void deleteById(long id) {

        deleteByIds(Set.of(id));

    }

    @Override
    public void deleteByIds(Set<Long> ids) {
        tbGroupMaterialApplyDetailMapper.deleteBatchIds(ids);
        log.info("用户 [{}] 专业组 [{}] 删除物料明细 ids: {}", LoginUserHandler.get().getNickname(), LoginUserHandler.get().getGroupName(), ids);
    }

    @Override
    public List<GroupMaterialApplyDetailDto> selectByApplyNo(String applyNo, long orgId) {
        if (StringUtils.isBlank(applyNo)) {
            return Collections.emptyList();
        }

        final LambdaQueryWrapper<TbGroupMaterialApplyDetail> eq = Wrappers.lambdaQuery(TbGroupMaterialApplyDetail.class)
                .eq(TbGroupMaterialApplyDetail::getApplyNo, applyNo)
                .eq(TbGroupMaterialApplyDetail::getOrgId, orgId);

        return JSON.parseArray(JSON.toJSONString(tbGroupMaterialApplyDetailMapper.selectList(eq)), GroupMaterialApplyDetailDto.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean reject(MaterialRejectDto rejectDto) {
        if (CollectionUtils.isEmpty(rejectDto.getMaterialCodes())) {
            log.info("物料申请驳回，没有驳回的物料， 申请单：【{}】， ", rejectDto.getApplyNo());
            return true;
        }
        log.info("物料申请驳回，申请单：【{}】， 物料编码【{}】", rejectDto.getApplyNo(), rejectDto.getMaterialCodes());

        LambdaUpdateWrapper<TbGroupMaterialApplyDetail> wrapper = Wrappers.lambdaUpdate(TbGroupMaterialApplyDetail.class)
                .set(TbGroupMaterialApplyDetail::getStatus, MaterialApplyStatusEnum.REJECT.getCode())
                .eq(TbGroupMaterialApplyDetail::getApplyNo, rejectDto.getApplyNo())
                .in(TbGroupMaterialApplyDetail::getMaterialCode, rejectDto.getMaterialCodes());
        // 驳回物料
        tbGroupMaterialApplyDetailMapper.update(null, wrapper);

        // 申请单下未驳回的物料
        List<TbGroupMaterialApplyDetail> tbGroupMaterialApplyDetails = tbGroupMaterialApplyDetailMapper.selectList(Wrappers.lambdaQuery(TbGroupMaterialApplyDetail.class)
                        .eq(TbGroupMaterialApplyDetail::getApplyNo, rejectDto.getApplyNo()))
                .stream().filter(e -> !Objects.equals(e.getStatus(), MaterialApplyStatusEnum.REJECT.getCode()))
                .collect(Collectors.toList());
        log.info("物料申请驳回，申请单：【{}】， 物料编码【{}】， 未驳回物料【{}】", rejectDto.getApplyNo(), rejectDto.getMaterialCodes(), tbGroupMaterialApplyDetails.stream().map(TbGroupMaterialApplyDetail::getMaterialCode).collect(Collectors.toSet()));
        // 如果不存在未驳回的物料， 则申请单驳回
        if (CollectionUtils.isEmpty(tbGroupMaterialApplyDetails)) {
            tbGroupMaterialApplyMapper.update(null, Wrappers.lambdaUpdate(TbGroupMaterialApply.class)
                    .set(TbGroupMaterialApply::getStatus, MaterialApplyStatusEnum.REJECT.getCode())
                    .set(TbGroupMaterialApply::getUpdaterId, NumberUtils.LONG_ZERO)
                    .set(TbGroupMaterialApply::getUpdaterName, "业务中台")
                    .set(TbGroupMaterialApply::getUpdateDate, new Date())
                    .eq(TbGroupMaterialApply::getApplyNo, rejectDto.getApplyNo()));
        }

        return true;
    }

    @Override
    public void updateBatchByDetailId(List<GroupMaterialApplyDetailDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        final List<TbGroupMaterialApplyDetail> tbGroupMaterialApplyDetails =
                JSON.parseArray(JSON.toJSONString(list), TbGroupMaterialApplyDetail.class)
                        .stream().filter(e -> Objects.nonNull(e.getDetailId()))
                        .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(list)) {
            ((GroupMaterialApplyDetailServiceImpl) AopContext.currentProxy()).updateBatchById(tbGroupMaterialApplyDetails);
        }
    }

    @Override
    public List<GroupMaterialApplyDetailDto> selectByApplyNos(long orgId, List<String> applyNos) {
        final LambdaQueryWrapper<TbGroupMaterialApplyDetail> eq = Wrappers.lambdaQuery(TbGroupMaterialApplyDetail.class)
                .in(TbGroupMaterialApplyDetail::getApplyNo, applyNos)
                .eq(TbGroupMaterialApplyDetail::getOrgId, orgId)
                .orderByAsc(TbGroupMaterialApplyDetail::getCreateDate);

        return JSON.parseArray(JSON.toJSONString(tbGroupMaterialApplyDetailMapper.selectList(eq)), GroupMaterialApplyDetailDto.class);
    }
}
