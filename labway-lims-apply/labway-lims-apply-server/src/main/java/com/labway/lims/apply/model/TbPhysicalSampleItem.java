package com.labway.lims.apply.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 体检样本项目
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_physical_sample_item")
public class TbPhysicalSampleItem implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId
    private Long physicalSampleItemId;
    /**
     * 体验样本ID
     */
    private Long physicalSampleId;
    /**
     * 体检人
     */
    private Long physicalRegisterId;

    /**
     * 批次号
     */
    private Long physicalBatchId;

    /**
     * 检验项目
     */
    private Long testItemId;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 更新人
     */
    private String updaterName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 1:删除 0:未删
     * @see YesOrNoEnum
     */
    private Integer isDelete;

}
