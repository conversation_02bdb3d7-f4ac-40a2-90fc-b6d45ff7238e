package com.labway.lims.apply.service.chain.pick.two.multi;

import com.labway.lims.apply.api.dto.ApplySampleTwoPickDto;
import com.labway.lims.apply.api.dto.TwoPickDto;
import com.labway.lims.apply.service.chain.pick.two.TwoPickContext;
import com.labway.lims.apply.service.chain.pick.two.TwoPickRabbitMQCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 发送消息到mq
 */
@Slf4j
@Component
public class MultiTwoPickRabbitMQCommand implements Command {
    @Resource
    private TwoPickRabbitMQCommand twoPickRabbitMQCommand;


    @Override
    public boolean execute(Context c) throws Exception {

        final MultiTwoPickContext context = MultiTwoPickContext.from(c);

        for (ApplySampleTwoPickDto e : context.getApplySampleTwoPicks()) {
            final TwoPickContext ctx = new TwoPickContext(new TwoPickDto()
                    .setApplySampleId(e.getApplySampleId()));
            ctx.put(TwoPickContext.APPLY, context.getApplies().get(e.getApplyId()));
            ctx.getTwoPick().setSampleNo(e.getSampleNo());
            ctx.put(TwoPickContext.TWO_PICKED_SAMPLES, List.of(e));
            twoPickRabbitMQCommand.execute(ctx);
        }

        return CONTINUE_PROCESSING;
    }

}
