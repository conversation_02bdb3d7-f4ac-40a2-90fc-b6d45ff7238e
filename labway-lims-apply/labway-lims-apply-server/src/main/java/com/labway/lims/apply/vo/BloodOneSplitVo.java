package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * 单个分血
 */
@Getter
@Setter
public class BloodOneSplitVo {

    /**
     * 病人名称
     */
    private String patientName;

    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;


    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;

    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 管型
     */
    private String tubeName;

    /**
     * 管型CODE
     */
    private String tubeCode;

    /**
     * 样本类型
     */
    private String sampleTypeName;

    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;

    /**
     * 基本量、分血量
     */
    private BigDecimal basicQuantity;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 专业组，当不可分血时，这个是多个
     */
    private List<String> groupNames;

    /**
     * 检验项目
     */
    private List<String> testItemNames;

    /**
     * 是否分血分出来的条码
     */
    private Boolean isNewSplitSample;
}
