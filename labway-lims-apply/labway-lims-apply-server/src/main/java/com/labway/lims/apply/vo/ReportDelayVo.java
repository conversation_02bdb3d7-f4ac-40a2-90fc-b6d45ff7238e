package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * F
 * 报告延迟申请VO
 */
@Setter
@Getter
public class ReportDelayVo implements Serializable {

    /**
     * 签收日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date beginSignDate;

    /**
     * 签收日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endSignDate;


    /**
     * 申请日期开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date sendDateStart;

    /**
     * 申请日期结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date sendDateEnd;

    /**
     * 状态下拉框
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 检验项目
     */
    private Long testItemId;

    /**
     * 送检机构编码
     */
    private Set<String> hspOrgCodes;

    /**
     * 专业组编码
     */
    private String groupCode;
    /**
     * 专业组
     */
    private String groupName;

    /**
     * 门诊/住院号
     */
    private String patientVisitCard;

    /**
     * 病人姓名
     */
    private String patientName;

    /**
     * 条码号
     */
    private String barcode;


    /**
     * 签收日期
     */
    private Date signDate;

    /**
     * ---------------------------------列表数据-------------------------------
     */


    /**
     * @Fields delayId 迟发ID
     */
    private Long delayId;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;


    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 性别
     */
    private Integer patientSex;

    /**
     * 年龄
     */
    private Integer patientAge;
    /**
     * 子年龄
     */
    private Integer patientSubage;
    /**
     * 子年龄单位
     */
    private String patientSubageUnit;

    /**
     * 检验项目编码集合(空 为全部)
     */
    private String testItemCodes;

    /**
     * 检验项目编码List
     */
    private List<String> testItemCodesList;


    /**
     * 迟发原因
     */
    private String reason;

    /**
     * 迟发原因Name
     */
    private String reasonName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预计发布日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date sendReportDate;

    /**
     * 作废人ID
     */
    private Long cancelUserId;

    /**
     * 作废人姓名
     */
    private String cancelUserName;


    /**
     * 专业组id
     */
    private Long groupId;


    /**
     * 机构ID
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 迟发申请日期（创建时间）
     */
    private Date createDate;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;



    /**
     * 打印人ID
     */
    private Long printerId;

    /**
     * 打印人名称
     */
    private String printerName;

    /**
     * 打印时间
     */
    private Date printDate;


    /**
     * 检验项目
     */
    private List<BaseSampleEsModelDto.TestItem> testItems;

    /**
     * 项目类型 常规检验、微生物检验、院感检验 区分做什么检验的
     *
     * @see ItemTypeEnum
     */
    private String itemType;

    /**
     * 申请单样本状态
     */
    private Integer sampleStatus;

    /**
     * 样本id （可能是常规检验|微生物|院感...）
     */
    private Long sampleId;

    /**
     * 是否二次分拣 前段判断使用
     */
    private Integer isTwoPick;

    /**
     * 科室
     */

    private String dept;


    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    class TestItem {

        /**
         * 检验项目编码
         */

        private String testItemCode;

        /**
         * 检验项目名称
         */
        private String testItemName;


    }


}


