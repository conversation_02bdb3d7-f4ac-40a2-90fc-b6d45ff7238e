package com.labway.lims.apply.service.chain.pick.two;

import com.labway.lims.api.RedisPrefix;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * 限制
 */
@Slf4j
@Component
public class TwoPickLimitCommand implements Command, Filter {
    private static final String MARK = TwoPickLimitCommand.class.getName();

    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean execute(Context c) throws Exception {

        final TwoPickContext context = TwoPickContext.from(c);

        if (BooleanUtils.isNotTrue(stringRedisTemplate.opsForValue()
                .setIfAbsent(redisPrefix.getBasePrefix() + MARK + context.getTwoPick().getApplySampleId(),
                StringUtils.EMPTY, Duration.ofSeconds(10)))) {
            throw new IllegalStateException("正在二次分拣中");
        }

        context.put(MARK, StringUtils.EMPTY);

        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean postprocess(Context c, Exception exception) {
        final TwoPickContext context = TwoPickContext.from(c);
        if (context.containsKey(MARK)) {
            stringRedisTemplate.delete(redisPrefix.getBasePrefix() +
                    MARK + context.getTwoPick().getApplySampleId());
        }
        return CONTINUE_PROCESSING;
    }
}
