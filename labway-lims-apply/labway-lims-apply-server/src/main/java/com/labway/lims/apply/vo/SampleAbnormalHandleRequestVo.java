package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 异常 处理异常
 * 
 * <AUTHOR>
 * @since 2023/4/12 17:14
 */
@Getter
@Setter
public class SampleAbnormalHandleRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 异常值ID
     */
    private Long sampleAbnormalId;

    /**
     * 处理内容
     */
    private String handleContent;

    /**
     * 确认部门ID
     */
    private Long confirmGroupId;

}
