package com.labway.lims.apply.controller.it8000;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.vo.IT8000HandleVo;
import com.labway.lims.base.api.dto.InstrumentDto;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.InstrumentService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 获取仪器下的报告项目
 */
@Component
class MachineReportItemsAction implements ActionStrategy {
    @DubboReference
    private InstrumentService instrumentService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;

    @Override
    public Object action(IT8000HandleVo vo) throws Exception {
        final String machineCode = vo.getExtras().getString("lisMachineCode");

        final InstrumentDto instrument = instrumentService.selectByInstrumentCode(machineCode, LoginUserHandler.get().getOrgId());
        if (Objects.isNull(instrument)) {
            throw new IllegalArgumentException(String.format("仪器 [%s] 不存在", machineCode));
        }

        return instrumentReportItemService.selectByInstrumentId(instrument.getInstrumentId())
                .stream().map(e -> Map.of(
                        "reportItemCode", e.getReportItemCode(),
                        "instrumentChannel", e.getInstrumentChannel(),
                        "reportItemName", e.getReportItemName()
                )).collect(Collectors.toList());
    }

    @Override
    public IT8000HandleVo.Action action() {
        return IT8000HandleVo.Action.MACHINE_REPORT_ITEMS;
    }
}
