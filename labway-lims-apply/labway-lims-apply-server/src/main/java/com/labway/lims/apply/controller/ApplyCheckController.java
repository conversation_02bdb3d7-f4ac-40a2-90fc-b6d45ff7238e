package com.labway.lims.apply.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.*;
import com.labway.lims.apply.vo.*;
import com.labway.lims.base.api.dto.HspOrganizationFiledDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrganizationFiledService;
import com.labway.lims.base.api.service.TestItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.Diff;
import org.apache.commons.lang3.builder.DiffResult;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.labway.lims.api.enums.apply.DoubleCheckFiledEnum.OUT_BARCODE;

import static com.labway.lims.api.enums.apply.DoubleCheckFiledEnum.OUT_BARCODE;

@Slf4j
@RestController
@RequestMapping("/apply-check")
public class ApplyCheckController extends BaseController {

    @Resource
    private ApplyService applyService;
    @Resource
    private SampleFlowService sampleFlowService;

    @Resource
    private ApplySampleService applySampleService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private ApplyLogisticsService applyLogisticsService;

    @Resource
    private ApplyLogisticsSampleService applyLogisticsSampleService;
    @Resource
    private ApplyController applyController;

    @DubboReference
    private HspOrganizationFiledService hspOrganizationFiledService;

    @Resource
    private TestItemService testItemService;

    @Resource
    private ApplySampleItemService applySampleItemService;


    @Resource
    private ThreadPoolConfig threadPoolConfig;

    /**
     * 样本取消复核
     */
    @PostMapping("/cancel-sample-check")
    public Object cancelSampleCheck(@RequestBody CancelSampleCheckVo vo) {

        final String cause = vo.getCause();
        final String causeCode = vo.getCauseCode();
        final List<Long> applyIds = vo.getApplyIds();

        if (CollectionUtils.isEmpty(applyIds)) {
            throw new IllegalStateException("请选择样本信息");
        }

        if (StringUtils.isBlank(causeCode) || StringUtils.isBlank(cause)) {
            throw new IllegalStateException("请选择取消复核原因");
        }

        final List<ApplyDto> applys = applyService.selectByApplyIds(applyIds);
        if (CollectionUtils.isEmpty(applys)) {
            throw new IllegalStateException("申请单不存在");
        }

        for (final ApplyDto apply : applys) {
            if (BooleanUtils.isNotTrue(Objects.equals(apply.getStatus(), ApplyStatusEnum.CHECK.getCode()))) {
                throw new IllegalStateException(String.format("申请单 [%s] 复核状态异常", apply.getMasterBarcode()));
            }
        }

        // 校验申请单样本
        final List<ApplySampleDto> applySamples = applySampleService.selectByApplyIds(applyIds);
        cancelSampleCheckApplySample(applySamples);

        final LoginUserHandler.User user = LoginUserHandler.get();

        // 修改申请单状态
        applyService.updateCancelCheckStatus(applyIds);

        // 取消推送病理数据到业务中台
        applyService.cancelSendPathologyToBusinessCenter(applyIds);

        // 修改物流样本状态
        applyLogisticsSampleService.updateStatusByApplyIds(applyIds, ApplyStatusEnum.WAIT_CHECK.getCode());

        log.info("取消复核成功 用户 [{}] 专业组 [{}]  申请单id [{}] 原因 [{}]", user.getNickname(), user.getGroupName(), applyIds, cause);
        return Collections.emptyMap();
    }

    private void cancelSampleCheckApplySample(final List<ApplySampleDto> applySamples) {
        Set<String> disabled = new HashSet<>();
        Set<String> stoped = new HashSet<>();
        Set<String> onepick = new HashSet<>();

        for (final ApplySampleDto applySample : applySamples) {
            final String barcode = applySample.getBarcode();

            if (Objects.equals(applySample.getIsDisabled(), YesOrNoEnum.YES.getCode())) {
                disabled.add(barcode);
                // throw new IllegalStateException(String.format("样本 [%s] 已禁用,,不允许取消复核", barcode));
            }

            if (Objects.equals(applySample.getStatus(), SampleStatusEnum.STOP_TEST.getCode())) {
                stoped.add(barcode);
                // throw new IllegalStateException(String.format("样本 [%s] 已终止检验,不允许取消复核", barcode));
            }

            if (Objects.equals(applySample.getIsOnePick(), YesOrNoEnum.YES.getCode())) {
                onepick.add(barcode);
                // throw new IllegalStateException(String.format("样本 [%s] 已一次分拣不允许取消复核", barcode));
            }

        }

        if (Stream.of(disabled, stoped, onepick).anyMatch(CollectionUtils::isNotEmpty)) {
            throw new LimsCodeException(497,
                    String.format("样本 [%s] 已禁用", String.join(",", disabled))
                            .concat("<br />").concat(String.format("样本 [%s] 已终止检验", String.join(",", stoped)))
                            .concat("<br />").concat(String.format("样本 [%s] 已一次分拣", String.join(",", onepick)))
                            .concat("<br />").concat("不允许取消复核"));
        }
    }

    /**
     * 样本复核
     */
    @PostMapping("/sample-check")
    public Object sampleCheck(@RequestBody List<Long> applyIds) {

        if (CollectionUtils.isEmpty(applyIds)) {
            throw new IllegalStateException("请选择样本信息");
        }

        final List<ApplyDto> applys = applyService.selectByApplyIds(applyIds);
        if (CollectionUtils.isEmpty(applys)) {
            throw new IllegalStateException("复核申请单不存在");
        }

        for (final ApplyDto apply : applys) {

            if (BooleanUtils.isNotTrue(Objects.equals(apply.getStatus(), ApplyStatusEnum.WAIT_CHECK.getCode()))) {
                throw new IllegalStateException(String.format("申请单复核状态异常 主条码 [%s]", apply.getMasterBarcode()));
            }

        }


        final LoginUserHandler.User user = LoginUserHandler.get();
        final Date now = new Date();

        // 修改申请单状态
        ApplyDto apply = new ApplyDto();
        apply.setStatus(ApplyStatusEnum.CHECK.getCode());
        apply.setUpdaterName(user.getNickname());
        apply.setUpdaterId(user.getUserId());
        apply.setUpdateDate(now);

        apply.setCheckDate(now);
        apply.setCheckerId(user.getUserId());
        apply.setCheckerName(user.getNickname());

        apply.setSignDate(now);

        applyService.updateByApplyIds(apply, applyIds);

        // 修改物流样本状态
        applyLogisticsSampleService.updateStatusByApplyIds(applyIds, ApplyStatusEnum.CHECK.getCode());

        // 推送病理数据到业务中台
        applyService.sendPathologyToBusinessCenter(applyIds, Collections.emptyList());

        // 条码环节
        CompletableFuture.runAsync(() -> {
            LoginUserHandler.set(user);
            final List<ApplySampleDto> applySamples = applySampleService.selectByApplyIds(applyIds);
            if (CollectionUtils.isNotEmpty(applySamples)) {
                final LinkedList<Long> ids = snowflakeService.genIds(applySamples.size());
                sampleFlowService.addSampleFlows(applySamples.stream().map(e -> SampleFlowDto.builder()
                        .applyId(e.getApplyId())
                        .sampleFlowId(ids.pop())
                        .applySampleId(e.getApplySampleId())
                        .operateCode(BarcodeFlowEnum.DOUBLE_CHECK.name())
                        .operateName(BarcodeFlowEnum.DOUBLE_CHECK.getDesc())
                        .operatorId(LoginUserHandler.get().getUserId())
                        .operator(LoginUserHandler.get().getNickname())
                        .barcode(e.getBarcode())
                        .content("复核")
                        .build()).collect(Collectors.toList()));
            }
        }, threadPoolConfig.getPool());

        return Collections.emptyMap();
    }

    /**
     * 样本复核列表
     */
    @PostMapping(value = {"sample-not-reviewed-check-list", "sample-reviewed-check-list"})
    public Object sampleCheckList(@RequestBody HspOrgDateQueryVo vo, HttpServletRequest request) {
        // 默认当天
        final Date now = new Date();
        vo.setStartDate(ObjectUtils.defaultIfNull(vo.getStartDate(), DateUtil.beginOfDay(now)));
        vo.setEndDate(ObjectUtils.defaultIfNull(vo.getEndDate(), DateUtil.endOfDay(now)));

        final UnreviewedApplyQueryDto queryDto = new UnreviewedApplyQueryDto();

        List<Integer> statusList = new ArrayList<>();
        if (request.getRequestURI().contains("sample-reviewed-check-list")) {
            // 已复核
            queryDto.setQueryChecked(true);
            statusList.add(ApplyStatusEnum.CHECK.getCode());
        } else {
            statusList.add(ApplyStatusEnum.WAIT_CHECK.getCode());
        }

        queryDto.setStatusList(statusList);
        queryDto.setStartDate(vo.getStartDate());
        queryDto.setEndDate(vo.getEndDate());
        queryDto.setOrgId(LoginUserHandler.get().getOrgId());


        final List<SampleApplyDto> sampleApplys = applyService.selectUnreviewedApplySampleByQuery(queryDto);
        if (CollectionUtils.isEmpty(sampleApplys)) {
            return Collections.emptyList();
        }

        // 如果是复核的 那就按照复核时间排序
        if (Objects.equals(statusList.iterator().next(), ApplyStatusEnum.CHECK.getCode())) {
            sampleApplys.sort(Comparator.comparing(SampleApplyDto::getCheckDate));
        }

        // 查询物流申请单
        final Collection<Long> applyIds = sampleApplys.stream().filter(f -> Objects.equals(f.getSource(), ApplySourceEnum.SUPPLEMENTARY.name())).map(ApplyDto::getApplyId).collect(Collectors.toSet());
        final Map<Long, SimpleLogisticsSampleDto> simpleLogisticsSampleMap = applyLogisticsService.selectByApplyIds(applyIds).stream().collect(Collectors.toMap(SimpleLogisticsSampleDto::getApplyId, v -> v, (a, b) -> a));


        for (final SampleApplyDto sampleApply : sampleApplys) {
            if (MapUtils.isEmpty(simpleLogisticsSampleMap) || BooleanUtils.isNotTrue(Objects.equals(sampleApply.getSource(), ApplySourceEnum.SUPPLEMENTARY.name()))) {
                continue;
            }

            // 设置物流申请单图片
            final SimpleLogisticsSampleDto simpleLogisticsSample = simpleLogisticsSampleMap.get(sampleApply.getApplyId());
            if (Objects.nonNull(simpleLogisticsSample)) {
                sampleApply.setApplyImage(simpleLogisticsSample.getApplyImage());
            }
        }

        return JSON.parseArray(JSON.toJSONString(sampleApplys), SampleApplyVo.class);
    }

    /**
     * 双输复核
     */
    @PostMapping("/double-check")
    public Object doubleCheck(@RequestBody DoubleCheckTestApplyVo vo) {
        final Long applyId = vo.getApplyId();

        if (Objects.isNull(applyId)) {
            throw new IllegalStateException("请选择样本信息");
        }

        final ApplyDto apply = applyService.selectByApplyId(applyId);
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在");
        }

        if (!Objects.equals(apply.getStatus(), ApplyStatusEnum.WAIT_DOUBLE_CHECK.getCode())) {
            throw new IllegalStateException("申请单已双输复核");
        }


        final TestApplyVo testApplyVo = JSON.parseObject(JSON.toJSONString(apply), TestApplyVo.class);
        testApplyVo.setSendDoctor(apply.getSendDoctorName());
        testApplyVo.setClinicalDiagnosis(apply.getDiagnosis());

        // 将时间格式化成 yyyy-MM-dd HH:mm ，防止秒数不对比较失败
        final Date applyDate = testApplyVo.getApplyDate();
        final Date samplingDate = testApplyVo.getSamplingDate();
        final Date applyDateVo = vo.getApplyDate();
        final Date samplingDateVo = vo.getSamplingDate();
        String pattern = "yyyy-MM-dd HH:mm";
        if (Objects.nonNull(applyDate)) {
            testApplyVo.setApplyDate(DateUtil.parse(DateFormatUtils.format(testApplyVo.getApplyDate(), pattern)).toJdkDate());
        }

        if (Objects.nonNull(samplingDate)) {
            testApplyVo.setSamplingDate(DateUtil.parse(DateFormatUtils.format(testApplyVo.getSamplingDate(), pattern)).toJdkDate());
        }

        if (Objects.nonNull(applyDateVo)) {
            vo.setApplyDate(DateUtil.parse(DateFormatUtils.format(vo.getApplyDate(), pattern)).toJdkDate());
        }

        if (Objects.nonNull(samplingDateVo)) {
            vo.setSamplingDate(DateUtil.parse(DateFormatUtils.format(vo.getSamplingDate(), pattern)).toJdkDate());
        }


        // 错误提示消息
        List<String> errorMsgs = new ArrayList<>();

        // 对比差异
        final List<HspOrganizationFiledDto> hspOrganizationFileds = hspOrganizationFiledService.selectByHspOrgId(apply.getHspOrgId());
        final Map<String, String> hspOrganizationMap = hspOrganizationFileds.stream().collect(Collectors.toMap(HspOrganizationFiledDto::getCode, HspOrganizationFiledDto::getFiled, (a, b) -> a));
        vo.setFileds(hspOrganizationMap);

        //这里判断是否需要外部条码检验，因为外部条码信息在具体样本中
        if (hspOrganizationMap.containsKey(OUT_BARCODE.getCode())) {
            //这里直接找到具体样本 并赋值给testApplyVo
            List<ApplySampleDto> sampleDtos = applySampleService.selectByApplyId(applyId);
            if (CollectionUtils.isNotEmpty(sampleDtos)){
                testApplyVo.setOutBarcode(sampleDtos.get(0).getOutBarcode());
            }
        }

        final DiffResult<TestApplyVo> diffs = vo.diff(testApplyVo);

        final String filedName = diffs.getDiffs().stream().map(Diff::getFieldName).distinct().collect(Collectors.joining("、"));
        if (StringUtils.isNotBlank(filedName)) {
            errorMsgs.add(String.format("[%s] 不一致", filedName));
        }



        List<ApplyLogisticsSampleDto> applyLogisticsSamples = applyLogisticsSampleService.selectByApplyIds(List.of(applyId));
        if (CollectionUtils.isEmpty(applyLogisticsSamples)) {

            final List<TestApplyVo.Item> items = vo.getItems();
            if (CollectionUtils.isEmpty(items)) {
                throw new IllegalStateException("请选择检验项目");
            }

            final int toSetCount = items.stream().map(TestApplyVo.Item::getTestItemId).collect(Collectors.toSet()).size();
            if (!Objects.equals(toSetCount, items.size())) {
                throw new IllegalStateException("检验项目存在重复");
            }


            final Map<Long, TestApplyVo.Item> itemMap = items.stream().collect(Collectors.toMap(TestApplyVo.Item::getTestItemId, v -> v, (a, b) -> a));

            final List<ApplySampleItemDto> applySampleItems = applySampleItemService.selectByApplyIds(List.of(applyId));
            if (CollectionUtils.isEmpty(applySampleItems)) {
                throw new IllegalStateException("申请单检验项目不存在");
            }

            final List<TestItemDto> testItems = testItemService.selectByTestItemIds(items.stream().map(TestApplyVo.Item::getTestItemId).collect(Collectors.toSet()));
            if (CollectionUtils.isEmpty(testItems)) {
                throw new IllegalStateException("选择的检验项目均不存在");
            }
            final Map<Long, TestItemDto> testItemMap = testItems.stream().collect(Collectors.toMap(TestItemDto::getTestItemId, v -> v, (a, b) -> a));
            testItems.forEach(t -> {
                t.setSampleTypeCode(itemMap.get(t.getTestItemId()).getSampleTypeCode());
                t.setSampleTypeName(itemMap.get(t.getTestItemId()).getSampleTypeName());
            });

            //这里判断外部条码号
            if (StringUtils.isNotBlank(vo.getOutBarcode()) && vo.equals(applySampleItems.get(0))){

            }


            // 检验项目信息
            for (final ApplySampleItemDto applySampleItem : applySampleItems) {
                final Long testItemId = applySampleItem.getTestItemId();
                final String testItemName = applySampleItem.getTestItemName();
                final String testItemCode = applySampleItem.getTestItemCode();
                final String tube = applySampleItem.getTubeName();
                final String sampleType = applySampleItem.getSampleTypeName();
                final Integer count = applySampleItem.getCount();
                final Integer urgent = applySampleItem.getUrgent();
                final Long groupId = applySampleItem.getGroupId();

                final TestItemDto testItem = testItemMap.get(testItemId);
                if (Objects.isNull(testItem)) {
                    errorMsgs.add(String.format("缺少 [%s] 项目", testItemName));
                    continue;
                }

                List<String> errorsMsgItems = new ArrayList<>();

                if (BooleanUtils.isNotTrue(Objects.equals(testItemName, testItem.getTestItemName()))) {
                    errorsMsgItems.add("名称");
                }
                // 检验项目编码
                if (BooleanUtils.isNotTrue(Objects.equals(testItem.getTestItemCode(), testItemCode))) {
                    errorsMsgItems.add("编码");
                }
                // 样本类型
                if (BooleanUtils.isNotTrue(Objects.equals(testItem.getSampleTypeName(), sampleType))) {
                    errorsMsgItems.add("样本类型");
                }

                // 管型
                if (BooleanUtils.isNotTrue(Objects.equals(testItem.getTubeName(), tube))) {
                    errorsMsgItems.add("管型");
                }

                final TestApplyVo.Item item = ObjectUtils.defaultIfNull(itemMap.get(testItemId), new TestApplyVo.Item());
                // 收费数量
                if (BooleanUtils.isNotTrue(Objects.equals(item.getCount(), count))) {
                    errorsMsgItems.add("收费数量");
                }

                // 急诊状态
                if (BooleanUtils.isNotTrue(Objects.equals(item.getUrgent(), urgent))) {
                    errorsMsgItems.add("急诊状态");
                }
                // 专业组
                if (BooleanUtils.isNotTrue(Objects.equals(testItem.getGroupId(), groupId))) {
                    errorsMsgItems.add("专业组");
                }

                itemMap.remove(testItemId);

                if (CollectionUtils.isEmpty(errorsMsgItems)) {
                    continue;
                }
                errorMsgs.add(String.format("检验项目 %s [%s] 不一致", testItemName, String.join("、", errorsMsgItems)));
            }

            if (MapUtils.isNotEmpty(itemMap)) {

                final String extraItemName = itemMap.values().stream().map(TestApplyVo.Item::getTestItemName).collect(Collectors.joining(","));
                errorMsgs.add(String.format("%s 不存在", extraItemName));

            }

        }

        if (CollectionUtils.isNotEmpty(errorMsgs)) {
            throw new IllegalStateException(JSON.toJSONString(errorMsgs));
        }

        final Date now = new Date();

        // 更新申请单
        final LoginUserHandler.User user = LoginUserHandler.get();
        ApplyDto applyDto = new ApplyDto();
        applyDto.setApplyId(applyId);
        applyDto.setUpdateDate(now);
        applyDto.setUpdaterId(user.getUserId());
        applyDto.setUpdaterName(user.getNickname());

        applyDto.setCheckDate(now);
        applyDto.setCheckerId(user.getUserId());
        applyDto.setCheckerName(user.getNickname());

        applyDto.setSignDate(now);

        applyDto.setStatus(ApplyStatusEnum.DOUBLE_CHECK.getCode());
        applyService.updateByApplyId(applyDto);

        // 推送病理数据到业务中台
        applyService.sendPathologyToBusinessCenter(List.of(applyId), Collections.emptyList());

        //  更新物流申请单样本
        applyLogisticsSampleService.updateStatusByApplyId(applyId, ApplyStatusEnum.DOUBLE_CHECK.getCode());
        log.info("双输复核成功, 用户 [{}] 复核内容 [{}] applyId: {} ", user.getNickname(), hspOrganizationMap.values(), applyId);

        return Collections.emptyMap();
    }

    /**
     * 查询样本信息录入待双输复核列表
     */
    @PostMapping("/query-apply-double-check-list")
    public Object queryApplyDoubleCheckList(@RequestBody HspOrgDateQueryVo queryVo) {

        if (Objects.isNull(queryVo.getHspOrgId())) {
            return Collections.emptyList();
        }

        // 默认今天
        final Date now = new Date();
        queryVo.setStartDate(ObjectUtils.defaultIfNull(queryVo.getStartDate(), DateUtil.beginOfDay(now)));
        queryVo.setEndDate(ObjectUtils.defaultIfNull(queryVo.getEndDate(), DateUtil.endOfDay(now)));

        final UnreviewedApplyQueryDto query = new UnreviewedApplyQueryDto();
        query.setStatusList(List.of(ApplyStatusEnum.WAIT_DOUBLE_CHECK.getCode()));
        query.setSource(ApplySourceEnum.MANUAL.name());
        query.setEndDate(queryVo.getEndDate());
        query.setStartDate(queryVo.getStartDate());
        query.setHspOrgId(queryVo.getHspOrgId());
        query.setOrgId(LoginUserHandler.get().getOrgId());

        final List<SampleApplyDto> sampleApplys = applyService.selectUnreviewedApplySampleByQuery(query);

        return sampleApplys.stream().map(m -> {
            final SampleApplyVo vo = JSON.parseObject(JSON.toJSONString(m), SampleApplyVo.class);
            vo.setSampleStatusDesc(SampleStatusEnum.getStatusByCode(m.getSampleStatusCode()).getDesc());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 查询物流样本信息录入待双输复核列表
     */
    @PostMapping("/query-apply-logistics-double-check-list")
    public Object queryApplyLogisticsDoubleCheckList(@RequestBody HspOrgDateQueryVo queryVo) {

        // 默认今天
        final Date now = new Date();
        queryVo.setStartDate(ObjectUtils.defaultIfNull(queryVo.getStartDate(), DateUtil.beginOfDay(now)));
        queryVo.setEndDate(ObjectUtils.defaultIfNull(queryVo.getEndDate(), DateUtil.endOfDay(now)));

        final HspOrgDateQueryDto dto = JSON.parseObject(JSON.toJSONString(queryVo), HspOrgDateQueryDto.class);
        dto.setStatusList(List.of(ApplyStatusEnum.WAIT_DOUBLE_CHECK.getCode()));

        return JSON.parseArray(JSON.toJSONString(applyLogisticsSampleService.selectApplyLogisticsDetail(dto)), SimpleLogisticsSampleVo.class);
    }

    @PostMapping("send-pathology-to-business-center")
    public Object sendPathologyToBusinessCenter(@RequestBody Map<String, List<Long>> params) {
        applyService.sendPathologyToBusinessCenter(params.get("applyIds"), params.get("applySampleIds"));
        return Collections.emptyMap();
    }

}
