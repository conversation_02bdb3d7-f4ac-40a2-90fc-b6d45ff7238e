package com.labway.lims.apply.vo;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class PrintPdfOnePickSamplesVo extends OnePickRacksSamplesVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private String testName;

    public void setTestName(String testName) {
        this.testName = testName;
    }

    public void setTestName(List<String> testItemNames) {
        super.setTestItemNames(testItemNames);
        testName = CollUtil.join(getTestItemNames(), ",");
    }
}
