package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * 结果备注
 */
@Getter
@Setter
public class ApplySampleSampleRemarkVo {
    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 样本备注
     */
    private String sampleRemark;

    /**
     * 样本类型编码
     */
    private String sampleTypeCode;

    /**
     * 样本类型名称
     */
    private String sampleTypeName;

    /**
     * 样本性状
     */
    private String sampleProperty;

    /**
     * 样本性状编码
     */
    private String samplePropertyCode;

}
