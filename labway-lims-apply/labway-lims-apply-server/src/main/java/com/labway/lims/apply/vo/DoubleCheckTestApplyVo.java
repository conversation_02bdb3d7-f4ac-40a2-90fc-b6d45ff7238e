package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.builder.DiffBuilder;
import org.apache.commons.lang3.builder.DiffResult;
import org.apache.commons.lang3.builder.Diffable;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Map;
import java.util.Objects;

import static com.labway.lims.api.enums.apply.DoubleCheckFiledEnum.*;

@Getter
@Setter
public class DoubleCheckTestApplyVo extends TestApplyVo implements Diffable<TestApplyVo> {

    private static final long serialVersionUID = 1L;

    /**
     * 申请单id
     */
    private Long applyId;

    /**
     * 1 样本双输复核
     * 2 补录双输复核 （不需要校验项目信息）
     */
    private Integer type;

    @Override
    public DiffResult<TestApplyVo> diff(TestApplyVo old) {
        final DiffBuilder<TestApplyVo> builder = new DiffBuilder<>(this, old, ToStringStyle.JSON_STYLE);
        if (MapUtils.isEmpty(fileds)) {
            return builder.build();
        }

        // 就诊类型
        if (fileds.containsKey(APPLY_TYPE.getCode())) {
            builder.append("就诊类型", old.getApplyTypeCode(), this.getApplyTypeCode());
        }
        // 姓名
        if (fileds.containsKey(PATIENT_NAME.getCode())) {
            builder.append("姓名", old.getPatientName(), this.getPatientName());
        }
        // 性别
        if (fileds.containsKey(PATIENT_SEX.getCode())) {
            builder.append("性别", old.getPatientSex(), this.getPatientSex());
        }
        // 出生日期
        if (fileds.containsKey(PATIENT_BIRTHDAY.getCode())) {
            builder.append("出生日期", old.getPatientBirthday(), this.getPatientBirthday());
        }
        // 年龄
        if (fileds.containsKey(PATIENT_AGE.getCode())) {
            builder.append("年龄", old.getPatientAge(), this.getPatientAge());
        }
        // 子年龄
        if (fileds.containsKey(PATIENT_AGE.getCode())) {
            builder.append("年龄", old.getPatientSubage(), this.getPatientSubage());
        }
        // 子年龄单位
        if (fileds.containsKey(PATIENT_AGE.getCode())) {
            builder.append("子年龄单位", old.getPatientSubageUnit(), this.getPatientSubageUnit());
        }
        // 急诊状态
        if (fileds.containsKey(URGENT.getCode())) {
            builder.append("急诊状态", old.getUrgent(), this.getUrgent());
        }
        // 样本个数
        if (fileds.containsKey(SAMPLE_COUNT.getCode())) {
            builder.append("样本个数", old.getSampleCount(), this.getSampleCount());
        }
        // 样本性状
        if (fileds.containsKey(SAMPLE_PROPERTY_CODE.getCode())) {
            builder.append("样本性状", old.getSamplePropertyCode(), this.getSamplePropertyCode());
        }
        // 申请时间
        if (fileds.containsKey(APPLY_DATE.getCode())) {
            builder.append("申请时间", old.getApplyDate(), this.getApplyDate());
        }
        // 采样时间
        if (fileds.containsKey(SAMPLING_DATE.getCode())) {
            builder.append("采样时间", old.getSamplingDate(), this.getSamplingDate());
        }
        // 门诊/住院号
        if (fileds.containsKey(PATIENT_VISIT_CARD.getCode())) {
            builder.append("门诊/住院号", old.getPatientVisitCard(), this.getPatientVisitCard());
        }
        // 科室
        if (fileds.containsKey(DEPT.getCode())) {
            builder.append("科室", old.getDept(), this.getDept());
        }
        // 床号
        if (fileds.containsKey(PATIENT_BED.getCode())) {
            builder.append("床号", old.getPatientBed(), this.getPatientBed());
        }
        // 送检医生
        if (fileds.containsKey(SEND_DOCTOR.getCode())) {
            builder.append("送检医生", old.getSendDoctor(), this.getSendDoctor());
        }
        // 临床诊断
        if (fileds.containsKey(CLINICAL_DIAGNOSIS.getCode())) {
            builder.append("临床诊断", old.getClinicalDiagnosis(), this.getClinicalDiagnosis());
        }
        // 备注
        if (fileds.containsKey(REMARK.getCode())) {
            builder.append("备注", old.getRemark(), this.getRemark());
        }
        // 电话
        if (fileds.containsKey(PATIENT_MOBILE.getCode())) {
            builder.append("电话", old.getPatientMobile(), this.getPatientMobile());
        }
        // 身份证
        if (fileds.containsKey(PATIENT_CARD.getCode())) {
            builder.append("身份证", old.getPatientCard(), this.getPatientCard());
        }
        // 外部条码号
        if (fileds.containsKey(OUT_BARCODE.getCode())) {
            builder.append("外部条码号", old.getOutBarcode(), this.getOutBarcode());
        }
        return builder.build();
    }

    /**
     * 需要校验的字段
     */
    private Map<String, String> fileds;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        final DoubleCheckTestApplyVo that = (DoubleCheckTestApplyVo) o;
        return Objects.equals(type, that.type) && Objects.equals(fileds, that.fileds);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), type, fileds);
    }
}
