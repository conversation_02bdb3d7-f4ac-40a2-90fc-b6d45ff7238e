package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

@Getter
@Setter
public class SendSpecimenVo {
    /**
     * 签收个数
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 签收个数
     */
    private AtomicInteger signCount;

    /**
     * 审核个数
     */
    private AtomicInteger auditCount;

    /**
     * 样本列表
     */
    private List<Sample> items;


    @Getter
    @Setter
    public static final class Sample {

        /**
         * 样本条码
         */
        private String barcode;

        /**
         * 患者姓名
         */
        private String patientName;

        /**
         * 患者性别
         *
         * @see SexEnum
         */
        private Integer patientSex;

        /**
         * 患者年龄
         */
        private Integer patientAge;

        /**
         * 患者子年龄
         */
        private Integer patientSubage;

        /**
         * 患者子年龄单位
         *
         * @see PatientSubAgeUnitEnum
         */
        private String patientSubageUnit;

        /**
         * 签收时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
        private Date signDate;

        /**
         * 检验项目
         */
        private String testItem;

        /**
         * 审核时间
         */
        private Date auditDate;

        /**
         * 样本状态
         *
         * @see SampleStatusEnum
         */
        private Integer sampleStatus;


        public Date getAuditDate() {
            if (Objects.isNull(auditDate)) {
                return null;
            }

            if (DefaultDateEnum.DEFAULT_DATE.getDate().equals(auditDate)) {
                return null;
            }

            return auditDate;
        }
    }
}
