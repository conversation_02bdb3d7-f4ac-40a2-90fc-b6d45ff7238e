package com.labway.lims.apply.model.es;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;

/**
 * Date
 */
@Getter
@Setter
public class GeneticsInspection extends BaseSampleEsModel {
    /**
     * 一次审核
     */
    @Field(type = FieldType.Long)
    private Long oneCheckerId;
    /**
     * 一次审核人名称
     */
    @Field(type = FieldType.Keyword)
    private String oneCheckerName;
    /**
     * 二次审核人
     */
    @Field(type = FieldType.Long)
    private Long twoCheckerId;
    /**
     * 二次审核人名称
     */
    @Field(type = FieldType.Keyword)
    private String twoCheckerName;
    /**
     * 二审时间
     */
    @Field(type = FieldType.Date)
    private Date twoCheckDate;

    /**
     * 细胞数
     */
    @Field(type = FieldType.Integer)
    private Integer cellCount;
    /**
     * 分析细胞数
     */
    @Field(type = FieldType.Integer)
    private Integer analyseCellCount;
    /**
     * 标本情况
     */
    @Field(type = FieldType.Keyword)
    private String sampleSituation;
    /**
     * 核型
     */
    @Field(type = FieldType.Keyword)
    private String karyotype;
    /**
     * 显带方法
     */
    @Field(type = FieldType.Keyword)
    private String bandingMethod;
    /**
     * 显带水平
     */
    @Field(type = FieldType.Keyword)
    private String bandingLevel;
    /**
     * 分析意见
     */
    @Field(type = FieldType.Text)
    private String analyticalOpinion;
    /**
     * 原始图像1
     */
    @Field(type = FieldType.Keyword)
    private String karyotypeOriginalImg1;
    /**
     * 原始图像2
     */
    @Field(type = FieldType.Keyword)
    private String karyotypeOriginalImg2;
    /**
     * 原始图像3
     */
    @Field(type = FieldType.Keyword)
    private String karyotypeOriginalImg3;
    /**
     * 分析图像1
     */
    @Field(type = FieldType.Keyword)
    private String karyotypeImg1;
    /**
     * 分析图像2
     */
    @Field(type = FieldType.Keyword)
    private String karyotypeImg2;
    /**
     * 分析图像3
     */
    @Field(type = FieldType.Keyword)
    private String karyotypeImg3;

}
