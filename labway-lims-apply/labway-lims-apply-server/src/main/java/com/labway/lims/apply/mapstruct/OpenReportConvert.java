package com.labway.lims.apply.mapstruct;

import com.labway.lims.apply.api.dto.es.MicrobiologyInspectionDto;
import com.labway.lims.apply.api.dto.es.OutsourcingInspectionDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import com.labway.lims.apply.vo.openreport.OpenReportMicrobiologyInspectionVoOpenReport;
import com.labway.lims.apply.vo.openreport.OpenReportOutsourcingInspectionVoOpenReport;
import com.labway.lims.apply.vo.openreport.OpenReportRoutineInspectionVoOpenReport;
import org.mapstruct.Mapper;

/**
 * <AUTHOR> on 2024/12/23.
 */
@Mapper(componentModel = "spring")
public interface OpenReportConvert {

	OpenReportRoutineInspectionVoOpenReport convertEsRoutineResult2OpenReportResult(RoutineInspectionDto routineInspectionDto);

	OpenReportMicrobiologyInspectionVoOpenReport convertMicrobiologyEsResult2OpenReportResult(MicrobiologyInspectionDto microbiologyInspectionDto);

	OpenReportOutsourcingInspectionVoOpenReport convertOutSourcingEsResult2OpenReportResult(OutsourcingInspectionDto outsourcingInspectionDto);
}
