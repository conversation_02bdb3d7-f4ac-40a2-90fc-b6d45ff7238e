package com.labway.lims.apply.vo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Objects;

@Getter
@Setter
public class GroupMaterialApplyDetailAddVo {

    /**
     * 物料id
     */
    private Long materialId;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 申领主单位数量
     */
    private BigDecimal applyMainNumber;

    /**
     * 申领辅单位数量
     */
    private BigDecimal applyAssistNumber;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        final GroupMaterialApplyDetailAddVo that = (GroupMaterialApplyDetailAddVo) o;
        return Objects.equals(materialId, that.materialId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(materialId);
    }
}
