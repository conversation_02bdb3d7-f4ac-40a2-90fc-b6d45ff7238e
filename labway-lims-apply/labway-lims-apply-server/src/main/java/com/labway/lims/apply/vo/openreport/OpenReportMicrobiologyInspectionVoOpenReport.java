package com.labway.lims.apply.vo.openreport;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 微生物
 */
@Getter
@Setter
public class OpenReportMicrobiologyInspectionVoOpenReport extends OpenReportBaseSampleEsModelVo {
    /**
     * 二审人id
     */
    private Long twoCheckerId;
    /**
     * 二审人
     */
    private String twoCheckerName;
    /**
     * 二审时间
     */
    private Date twoCheckDate;
    /**
     * 微生物结果
     */
    private List<OpenReportMicrobiologyResultDto> results;

    /**
     * 微生物细菌
     */
    private List<OpenReportMicrobiologyGermDto> germs;
    /**
     * 一次审核id
     */
    private Long oneCheckerId;

    /**
     * 一次审核人
     */
    private String oneCheckerName;

    /**
     * 一次审核时间
     */
    private Date oneCheckerDate;



}
