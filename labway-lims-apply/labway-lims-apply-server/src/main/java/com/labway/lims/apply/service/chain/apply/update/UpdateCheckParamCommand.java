package com.labway.lims.apply.service.chain.apply.update;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.TestApplyDto;
import com.labway.lims.apply.api.dto.UpdateTestApplyDto;
import com.labway.lims.apply.api.dto.UpdateTestApplySampleDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.TestItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
@Component
public class UpdateCheckParamCommand implements Command {

    @DubboReference
    private TestItemService testItemService;

    @Resource
    private ApplyService applyService;
    @Resource
    private ApplySampleService applySampleService;

    @DubboReference
    private HspOrganizationService hspOrganizationService;

    /**
     * 检查申请单状态 复核了的不能修改（指针对申请单信息录入）
     */
    private static void checkApplyStatus(TestApplyDto testApply, ApplyDto apply) {
        if ((testApply instanceof UpdateTestApplyDto) &&
                List.of(ApplyStatusEnum.CHECK.getCode(), ApplyStatusEnum.DOUBLE_CHECK.getCode()).contains(apply.getStatus())) {
            throw new IllegalStateException("申请单已复核，不能修改");
        }
    }

    @Override
    public boolean execute(Context c) throws Exception {
        final UpdateApplyContext from = UpdateApplyContext.from(c);
        final TestApplyDto testApply = from.getTestApply();

        final LoginUserHandler.User user = LoginUserHandler.get();

        final ApplyDto apply = applyService.selectByApplyId(from.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在");
        }
        from.put(UpdateApplyContext.APPLY, apply);

        // 申请单录入修改 已经复核的申请单不能修改(在申请单录入页面)
        if (!from.isBatchUpdateItem()) {
            checkApplyStatus(testApply, apply);
        }

        // 校验申请单检验项目信息
        checkApplyTestItemInfo(from, testApply);

        // 样本信息修改，禁用或者终止的样本不能修改 （仅针对样本信息修改页面）
        sampleUpdateCheckApplyInfoAndSampleInfo(from, testApply, apply);

        // 是样本信息录入的时候
        //        sampleUpdateCheckApplyinfo(from, testApply, apply);

        final Long hspOrgId = testApply.getHspOrgId();
        if (Objects.isNull(hspOrgId)) {
            throw new IllegalArgumentException("请选择送检机构");
        }

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(hspOrgId);
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalStateException("选择的送检机构为空");
        }
        if (!Objects.equals(hspOrganization.getEnable(), YesOrNoEnum.YES.getCode())) {
            throw new IllegalStateException(String.format("送检机构 [%s] 已停用", hspOrganization.getHspOrgName()));
        }

        from.put(UpdateApplyContext.HSP_ORG, hspOrganization);
        from.setUser(user);

        return CONTINUE_PROCESSING;
    }


    private void checkApplyTestItemInfo(UpdateApplyContext from, TestApplyDto testApply) {

        final List<TestApplyDto.Item> items = testApply.getItems();

        if (CollectionUtils.isEmpty(items)) {
            throw new IllegalArgumentException("检验项目信息不能为空");
        }

        final int toSetCount = items.stream().map(TestApplyDto.Item::getTestItemId).collect(Collectors.toSet()).size();

        if (!Objects.equals(toSetCount, items.size())) {
            throw new IllegalArgumentException("检验项目出现重复");
        }

        final Map<Long, TestApplyDto.Item> updateTestApplyItemMap = items.stream().collect(Collectors.toMap(TestApplyDto.Item::getTestItemId, v -> v, (a, b) -> a));

        // 获取相关的检验项目信息
        final List<TestItemDto> testItems = testItemService.selectByTestItemIds(items.stream().map(TestApplyDto.Item::getTestItemId).collect(Collectors.toSet()));
        if (CollectionUtils.isEmpty(testItems)) {
            throw new IllegalStateException("检验项目不存在");
        }

        final Collection<Long> testItemIds = testItems.stream().map(TestItemDto::getTestItemId).collect(Collectors.toSet());
        updateTestApplyItemMap.forEach((k, v) -> {
            if (!testItemIds.contains(k)) {
                throw new IllegalStateException(String.format("检验项目 [%s] 不存在", v.getTestItemName()));
            }
        });

        from.put(UpdateApplyContext.TEST_ITEMS, testItems);
    }

    // 是检验加减项目的时候
    private void sampleUpdateCheckApplyInfoAndSampleInfo(UpdateApplyContext from, TestApplyDto testApply, ApplyDto apply) {
        if (!(testApply instanceof UpdateTestApplySampleDto)) {
            return;
        }

        if (BooleanUtils.isTrue(ApplyStatusEnum.isUnCheck(apply.getStatus()))) {
            throw new IllegalStateException("当前条码未完成复核，请至对应页面进行信息修改");
        }

        final List<Long> applySampleIds = ((UpdateTestApplySampleDto) testApply).getApplySampleIds();
        if (CollectionUtils.isEmpty(applySampleIds)) {
            throw new IllegalArgumentException("申请单样本id不能为空");
        }

        String barcode = testApply.getBarcode();
        if (StringUtils.isEmpty(barcode)) {
            throw new IllegalArgumentException("条码不能为空");
        }

        final List<ApplySampleDto> applySamples = applySampleService.selectByBarcode(barcode);
        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("申请单样本不存在");
        }
        final boolean isUpdateSexOrAge = isUpdateSexOrAge(testApply, apply);

        for (final ApplySampleDto applySample : applySamples) {

            if (applySampleIds.contains(applySample.getApplySampleId())
                    && Objects.equals(applySample.getStatus(), SampleStatusEnum.STOP_TEST.getCode())) {
                throw new IllegalStateException(String.format("样本 [%s] 已终止检验", applySample.getBarcode()));
            }

            if ((Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode())
                    || Objects.equals(applySample.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode())) && BooleanUtils.isTrue(isUpdateSexOrAge)) {
                throw new IllegalStateException(String.format("当前申请单下 条码 [%s] 已审核，不能修改性别和年龄", applySample.getBarcode()));
            }

            // 当前申请单样本的外部条码号
            if (applySampleIds.contains(applySample.getApplySampleId())) {
                from.setOutBarcode(applySample.getOutBarcode());
            }
        }
        from.put(UpdateApplyContext.SAMPLES, applySamples);
        from.setIsUpdateSexOrAge(isUpdateSexOrAge);
    }


    // 是样本信息录入的时候
    private void sampleUpdateCheckApplyinfo(UpdateApplyContext from, TestApplyDto testApply, ApplyDto apply) {
        if (!(testApply instanceof UpdateTestApplyDto)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(from.getTestItems())) {
            return;
        }

        if (BooleanUtils.isTrue(ApplyStatusEnum.isUnCheck(apply.getStatus()))) {
            throw new IllegalStateException("当前条码未完成复核，请至对应页面进行信息修改");
        }

        String barcode = testApply.getBarcode();
        if (StringUtils.isEmpty(barcode)) {
            throw new IllegalArgumentException("条码不能为空");
        }

        final List<ApplySampleDto> applySamples = applySampleService.selectByBarcode(barcode);
        // 获取applySampleId
        List<Long> applySampleIds = applySamples.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(applySamples)) {
            throw new IllegalStateException("申请单样本不存在");
        }
        final boolean isUpdateSexOrAge = isUpdateSexOrAge(testApply, apply);

        for (final ApplySampleDto applySample : applySamples) {

            if (applySampleIds.contains(applySample.getApplySampleId())
                    && Objects.equals(applySample.getStatus(), SampleStatusEnum.STOP_TEST.getCode())) {
                throw new IllegalStateException(String.format("样本 [%s] 已终止检验", applySample.getBarcode()));
            }

            if ((Objects.equals(applySample.getStatus(), SampleStatusEnum.AUDIT.getCode())
                    || Objects.equals(applySample.getStatus(), SampleStatusEnum.ONE_AUDIT.getCode())) && BooleanUtils.isTrue(isUpdateSexOrAge)) {
                throw new IllegalStateException(String.format("当前申请单下 条码 [%s] 已审核，不能修改性别和年龄", applySample.getBarcode()));
            }

            // 当前申请单样本的外部条码号
            if (applySampleIds.contains(applySample.getApplySampleId())) {
                from.setOutBarcode(applySample.getOutBarcode());
            }
        }
        from.put(UpdateApplyContext.SAMPLES, applySamples);
        from.setIsUpdateSexOrAge(isUpdateSexOrAge);
    }

    /**
     * 是否修改了性别或者年龄
     */
    public static boolean isUpdateSexOrAge(TestApplyDto testApply, ApplyDto apply) {
        return BooleanUtils.isFalse(Objects.equals(testApply.getPatientSex(), apply.getPatientSex())
                && Objects.equals(testApply.getPatientAge(), apply.getPatientAge())
                && Objects.equals(testApply.getPatientSubage(), apply.getPatientSubage())
                && Objects.equals(testApply.getPatientSubageUnit(), apply.getPatientSubageUnit()));
    }
}
