package com.labway.lims.apply.mapstruct;

import com.labway.lims.apply.api.dto.MaterialInventoryCheckDto;
import com.labway.lims.apply.api.dto.SelectMaterialInventoryCheckDto;
import com.labway.lims.apply.model.TbMaterialInventoryCheck;
import com.labway.lims.apply.vo.MaterialInventoryCheckListItemResponseVo;
import com.labway.lims.apply.vo.MaterialInventoryCheckListRequestVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 物料盘点 相关 转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 10:28
 */
@Mapper(componentModel = "spring")
public interface MaterialInventoryCheckConverter {

    TbMaterialInventoryCheck tbMaterialInventoryCheckFromTbObjDto(MaterialInventoryCheckDto dto);

    MaterialInventoryCheckDto materialInventoryCheckDtoFromTbObj(TbMaterialInventoryCheck obj);

    List<MaterialInventoryCheckDto> materialInventoryCheckDtoListFromTbObjList(List<TbMaterialInventoryCheck> list);

    @Mapping(target = "checkName", source = "creatorName")
    MaterialInventoryCheckListItemResponseVo
        materialInventoryCheckListItemResponseVoFromTbObjDto(MaterialInventoryCheckDto dto);

    SelectMaterialInventoryCheckDto
        selectMaterialInventoryCheckDtoFromRequestVo(MaterialInventoryCheckListRequestVo vo);

}
