package com.labway.lims.apply.mapstruct;

import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.model.TbSampleReport;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 样本报告 相关 转换
 * 
 * <AUTHOR>
 * @since 2023/5/8 10:28
 */
@Mapper(componentModel = "spring")
public interface SampleReportConverter {

    SampleReportDto sampleReportDtoFromTbObj(TbSampleReport obj);

    List<SampleReportDto> sampleReportDtoListFromTbObjList(List<TbSampleReport> list);

}
