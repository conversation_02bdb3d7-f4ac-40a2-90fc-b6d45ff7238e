package com.labway.lims.apply.vo;

import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.microbiology.MicroSusceptibilityEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 阳性统计
 */
@Getter
@Setter
public class MicrobiologyPositiveStatisticsVo {

    /**
     * 此ID无意义，提供给前端使用
     */
    private String id;

    /**
     * 病人名称
     */
    private String patientName;


    /**
     * 年龄
     */
    private Integer patientAge;

    /**
     * 子年龄
     */
    private Integer patientSubage;

    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;


    /**
     * 性别  性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;


    /**
     * 管型
     */
    private String tubeName;

    /**
     * 管型CODE
     */
    private String tubeCode;

    /**
     * 样本类型
     */
    private String sampleTypeName;

    /**
     * 样本类型CODE
     */
    private String sampleTypeCode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 申请单样本ID
     */
    private Long applySampleId;

    /**
     * 送检机构
     */
    private String hspOrgName;


    /**
     * 就诊卡号 (门诊|住院号)
     */

    private String patientVisitCard;

    /**
     * 细菌名称
     */
    private String germName;

    /**
     * 细菌备注
     */
    private String germRemark;

    /**
     * 药物名称
     */
    private String medicineName;

    /**
     * 敏感度
     *
     * @see MicroSusceptibilityEnum
     */
    private String susceptibility;

    /**
     * 单位
     */
    private String unit;

    /**
     * 参考值
     */
    private String range;

    /**
     * 药物结果前缀
     */
    private String formula;

    /**
     * 结果
     */
    private String result;
}
