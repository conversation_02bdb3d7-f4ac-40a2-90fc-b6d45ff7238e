package com.labway.lims.apply.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.*;
import com.labway.lims.apply.service.chain.apply.add.GroupSampleCommand;
import com.labway.lims.apply.vo.*;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.base.api.service.TestItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 申请单 - 物流
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/apply-logistics")
public class ApplyLogisticsController extends BaseController {


    @Resource
    private ApplyLogisticsService applyLogisticsService;

    @Resource
    private ApplyLogisticsSampleService applyLogisticsSampleService;

    @Resource
    private ApplyLogisticsSampleItemService applyLogisticsSampleItemService;

    @Resource
    private TestItemService testItemService;

    @Resource
    private ApplyService applyService;

    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @Resource
    private ApplySampleService applySampleService;

    @Resource
    private ApplySampleItemService applySampleItemService;

    @Resource
    private GroupSampleCommand groupSampleCommand;

    @Resource
    private SnowflakeService snowflakeService;


    @DubboReference
    private ReportItemService reportItemService;
    /**
     * 追加申请单样本
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/additional-apply-sample")
    public Object additionalApplyInfo(@RequestBody AdditionalApplySampleVo vo) {
        final Long applyId = vo.getApplyId();
        final Long applyLogisticsSampleId = vo.getApplyLogisticsSampleId();
        if (Objects.isNull(applyId)) {
            throw new IllegalArgumentException("申请单ID不能为空");
        }
        if (Objects.isNull(applyLogisticsSampleId)) {
            throw new IllegalArgumentException("物流样本ID不能为空");
        }
        return Map.of("applySampleId", applyLogisticsService.additionalApplyInfo(applyId,applyLogisticsSampleId));
    }

    /**
     * 查询已补录的申请单
     */
    @PostMapping("/supplement-list")
    public Object supplementList(@RequestBody HspOrgDateQueryVo queryVo) {
        final Long hspOrgId = queryVo.getHspOrgId();
        if (Objects.isNull(hspOrgId)) {
            return Collections.emptyList();
        }

        // 默认今天
        final Date now = new Date();
        queryVo.setStartDate(ObjectUtils.defaultIfNull(queryVo.getStartDate(), DateUtil.beginOfDay(now)));
        queryVo.setEndDate(ObjectUtils.defaultIfNull(queryVo.getEndDate(), DateUtil.endOfDay(now)));

        final HspOrgDateQueryDto dto = JSON.parseObject(JSON.toJSONString(queryVo), HspOrgDateQueryDto.class);

        List<LogisticsApplyDto> logisticsApplys = applyLogisticsSampleService.selectSupplementList(dto);

        return JSON.parseArray(JSON.toJSONString(logisticsApplys), LogisticsApplyVo.class);
    }


    /**
     * 补录物流信息
     */
    @PostMapping("/supplement-logistics-information")
    public Object supplementLogisticsInformation(@RequestBody TestApplyVo testApplyVo, @RequestParam(required = false) Long applyLogisticsSampleId) {
        if (Objects.isNull(applyLogisticsSampleId)) {
            throw new IllegalArgumentException("请选择需要补录的物流样本");
        }

        // 检查申请单参数
        ApplyController.checkApplyParam(testApplyVo);

        // 物流样本
        final ApplyLogisticsSampleDto logisticsSample = applyLogisticsSampleService.selectById(applyLogisticsSampleId);
        if (Objects.isNull(logisticsSample)) {
            throw new IllegalArgumentException("物流样本不存在");
        }

        // 物流申请单
        final ApplyLogisticsDto applyLogistics = applyLogisticsService.selectByApplyLogisticsId(logisticsSample.getApplyLogisticsId());

        if (BooleanUtils.isNotTrue(Objects.equals(logisticsSample.getStatus(), ApplyStatusEnum.PENDING_RECORDING.getCode()))) {
            throw new IllegalArgumentException(String.format("物流条码 %s 已补录", logisticsSample.getBarcode()));
        }

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(applyLogistics.getHspOrgId());
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalStateException("送检机构不存在");
        }

        // 申请单检验项目
        final List<ApplyLogisticsSampleItemDto> applyLogisticsSampleItems = applyLogisticsSampleItemService.selectByApplyLogisticsSampleId(applyLogisticsSampleId);
        if (CollectionUtils.isEmpty(applyLogisticsSampleItems)) {
            throw new IllegalStateException("物流申请单检验项目为空");
        }

        // 校验样本是否有检验项目
        final List<ApplyLogisticsSampleItemDto> logisticsSampleItems = applyLogisticsSampleItems.stream().filter(p -> Objects.equals(logisticsSample.getApplyLogisticsSampleId(), p.getApplyLogisticsSampleId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(logisticsSampleItems)) {
            throw new IllegalStateException(String.format("物流样本 [%s] 没有检验项目", logisticsSample.getBarcode()));
        }

        LogisticsTestApplyDto.Sample sample = new LogisticsTestApplyDto.Sample();
        sample.setBarcode(logisticsSample.getBarcode());
        sample.setEnableDoubleCheck(hspOrganization.getEnableDoubleCheck());

        final List<TestApplyDto.Item> items = logisticsSampleItems.stream().map(m -> {
            TestApplyDto.Item logisticsItem = new TestApplyDto.Item();
            logisticsItem.setTestItemId(m.getTestItemId());
            logisticsItem.setCount(NumberUtils.INTEGER_ONE);
            logisticsItem.setUrgent(UrgentEnum.URGENT.getCode());
            logisticsItem.setCustomCode(StringUtils.EMPTY);
            return logisticsItem;
        }).collect(Collectors.toList());

        sample.setItems(items);

        final LogisticsTestApplyDto testApply = JSON.parseObject(JSON.toJSONString(testApplyVo), LogisticsTestApplyDto.class);
        testApply.setApplySource(ApplySourceEnum.SUPPLEMENTARY);
        testApply.setSupplier("物流APP");
        testApply.setSample(sample);
        testApply.setMasterBarcode(applyLogistics.getMasterBarcode());
        testApply.setHspOrgCode(hspOrganization.getHspOrgCode());
        testApply.setLogisticsSample(logisticsSample);
        testApply.setApplyLogistics(applyLogistics);
        // 补录物流申请单信息
        return applyLogisticsService.supplementLogisticsSampleInformation(testApply);
    }

    /**
     * 待补录列表
     */
    @PostMapping("/pending-recording-list")
    public Object pendingRecordingList(@RequestBody HspOrgDateQueryVo queryVo) {
        final Long orgId = queryVo.getHspOrgId();
        if (Objects.isNull(orgId)) {
            return Collections.emptyList();
        }

        // 默认今天
        final Date now = new Date();
        queryVo.setStartDate(ObjectUtils.defaultIfNull(queryVo.getStartDate(), DateUtil.beginOfDay(now)));
        queryVo.setEndDate(ObjectUtils.defaultIfNull(queryVo.getEndDate(), DateUtil.endOfDay(now)));

        final HspOrgDateQueryDto dto = JSON.parseObject(JSON.toJSONString(queryVo), HspOrgDateQueryDto.class);
        dto.setStatusList(List.of(ApplyStatusEnum.PENDING_RECORDING.getCode()));

        return JSON.parseArray(JSON.toJSONString(applyLogisticsSampleService.selectApplyLogisticsDetail(dto)), SimpleLogisticsSampleVo.class);
    }

    /**
     * 查询待复核的物流申请单
     */
    @GetMapping("/query-un-reviewed-logistics-apply")
    public Object queryUnReviewedLogisticsApply(@RequestParam(required = false) Long hspOrgId) {
        if (Objects.isNull(hspOrgId)) {
            return Collections.emptyList();
        }

        final List<ApplyDto> applys = applyService.selectUnreviewedApplySampleByQuery(ApplySourceEnum.SUPPLEMENTARY.name(), hspOrgId);


        return JSON.parseArray(JSON.toJSONString(applys), ApplyVo.class);
    }

    /**
     * 查询物流申请单样本检验项目
     */
    @GetMapping("/query-logistics-sample-item")
    public Object queryLogisticsSampleItem(@RequestParam(required = false) Long applyLogisticsSampleId) {
        if (Objects.isNull(applyLogisticsSampleId)) {
            return Collections.emptyList();
        }

        final ApplyLogisticsSampleDto logisticsSample = applyLogisticsSampleService.selectById(applyLogisticsSampleId);
        if (Objects.isNull(logisticsSample)) {
            return Collections.emptyList();
        }

        final List<ApplyLogisticsSampleItemDto> applyLogisticsSampleItems = applyLogisticsSampleItemService.selectByApplyLogisticsSampleId(applyLogisticsSampleId);

        final Collection<Long> testItemIds = applyLogisticsSampleItems.stream().map(ApplyLogisticsSampleItemDto::getTestItemId).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(testItemIds)) {
            return Collections.emptyList();
        }

        final Map<Long, TestItemDto> testItemMap = testItemService.selectByTestItemIdsAsMap(testItemIds);
        // 根据检验项目id查询报告项目
        final Map<Long, List<ReportItemDto>> reportItemMap = reportItemService.selectByTestItemIds(testItemIds).stream().collect(Collectors.groupingBy(ReportItemDto::getTestItemId));

        final List<LogisticsSampleItemVo> vos = applyLogisticsSampleItems.stream().map(m -> {
            final TestItemDto testItem = testItemMap.get(m.getTestItemId());

            if (Objects.isNull(testItem)) {
                return null;
            }


            final LogisticsSampleItemVo vo = JSON.parseObject(JSON.toJSONString(testItem), LogisticsSampleItemVo.class);
            vo.setApplyLogisticsSampleItemId(m.getApplyLogisticsApplyItemId());
            Optional.ofNullable(reportItemMap.get(m.getTestItemId()))
                    .ifPresent(c -> vo.setReportItems(JSON.parseArray(JSON.toJSONString(c), ReportItemVo.class)));

            vo.setCount(NumberUtils.INTEGER_ONE);
            vo.setUrgent(YesOrNoEnum.NO.getCode());
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        return vos;
    }


    /**
     * 查询物流申请单的检验项目
     */
    @GetMapping("/query-logistics-item")
    public Object queryLogisticsItem(@RequestParam(required = false) Long applyLogisticsId) {
        if (Objects.isNull(applyLogisticsId)) {
            return Collections.emptyList();
        }

        // 物流申请单
        final ApplyLogisticsDto applyLogistics = applyLogisticsService.selectByApplyLogisticsId(applyLogisticsId);
        if (Objects.isNull(applyLogistics)) {
            return Collections.emptyList();
        }

        // 物流申请单样本
        final List<ApplyLogisticsSampleDto> applyLogisticsSamples = applyLogisticsSampleService.selectByApplyLogisticsId(applyLogisticsId);
        if (CollectionUtils.isEmpty(applyLogisticsSamples)) {
            return Collections.emptyList();
        }
        final Map<Long, ApplyLogisticsSampleDto> applyLogisticsSampleMap = applyLogisticsSamples.stream().collect(Collectors.toMap(ApplyLogisticsSampleDto::getApplyLogisticsSampleId, v -> v, (a, b) -> a));


        // 物流申请单样本检验项目
        final List<ApplyLogisticsSampleItemDto> applyLogisticsSampleItems = applyLogisticsSampleItemService.selectByApplyLogisticsId(applyLogisticsId);
        if (CollectionUtils.isEmpty(applyLogisticsSampleItems)) {
            return Collections.emptyList();
        }
        final Collection<Long> testItemIds = applyLogisticsSampleItems.stream().map(ApplyLogisticsSampleItemDto::getTestItemId).collect(Collectors.toSet());
        final List<TestItemDto> testItems = testItemService.selectByTestItemIds(testItemIds);
        if (CollectionUtils.isEmpty(testItems)) {
            return Collections.emptyList();
        }
        final Map<Long, TestItemDto> testItemMap = testItems.stream().collect(Collectors.toMap(TestItemDto::getTestItemId, v -> v, (a, b) -> a));

        final List<ApplyItemVo> vos = applyLogisticsSampleItems.stream().map(m -> {
            ApplyItemVo vo = new ApplyItemVo();


            final TestItemDto testItem = testItemMap.get(m.getTestItemId());
            if (Objects.isNull(testItem)) {
                return null;
            }
            vo.setTestItemId(testItem.getTestItemId());
            vo.setTestItemName(testItem.getTestItemName());
            vo.setTestItemCode(testItem.getTestItemCode());
            vo.setEnName(testItem.getEnName());
            vo.setAliasName(testItem.getAliasName());
            vo.setExamMethodName(testItem.getExamMethodName());
            vo.setShortName(testItem.getShortName());
            vo.setTubeName(testItem.getTubeName());
            vo.setTubeCode(testItem.getTubeCode());
            vo.setGroupName(testItem.getGroupName());
            vo.setEnableExport(testItem.getEnableExport());
            vo.setExportOrgName(testItem.getExportOrgName());
            vo.setItemType(testItem.getItemType());
            vo.setSampleTypeName(testItem.getSampleTypeName());
            vo.setSampleTypeCode(testItem.getSampleTypeCode());
            vo.setFeePrice(testItem.getFeePrice());

            Optional.ofNullable(applyLogisticsSampleMap.get(m.getApplyLogisticsApplyItemId())).ifPresent(f -> vo.setBarcode(f.getBarcode()));
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());


        return vos;
    }

}
