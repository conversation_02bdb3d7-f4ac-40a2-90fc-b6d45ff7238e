package com.labway.lims.apply.controller;

import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.apply.MaterialReceiveRecordStatusEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.MaterialReceiveRecordDto;
import com.labway.lims.apply.api.dto.MaterialReceiveRegisterItemDto;
import com.labway.lims.apply.api.dto.SelectMaterialReceiveRecordDto;
import com.labway.lims.apply.api.service.MaterialReceiveRecordService;
import com.labway.lims.apply.mapstruct.MaterialReceiveRecordConverter;
import com.labway.lims.apply.vo.MaterialReceiveRecordListRequestVo;
import com.labway.lims.apply.vo.MaterialReceiveRecordListResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 物料领用记录 API
 * 
 * <AUTHOR>
 * @since 2023/5/9 16:57
 */
@Slf4j
@RestController
@RequestMapping("/material-receive-record")
public class MaterialReceiveRecordController extends BaseController {
    @Resource
    private MaterialReceiveRecordService materialReceiveRecordService;
    @Resource
    private MaterialReceiveRecordConverter materialReceiveRecordConverter;

    /**
     * 领用记录 列表
     */
    @PostMapping("/list")
    public Object materialReceiveRecordList(@RequestBody MaterialReceiveRecordListRequestVo vo) {
        SelectMaterialReceiveRecordDto selectMaterialReceiveRecordDto =
            materialReceiveRecordConverter.fromMaterialReceiveRecordListRequestVo(vo);

        LoginUserHandler.User user = LoginUserHandler.get();
        selectMaterialReceiveRecordDto.setOrgId(user.getOrgId());
        selectMaterialReceiveRecordDto.setGroupId(user.getGroupId());

        // 查询领用记录
        List<MaterialReceiveRecordDto> materialReceiveRecordDtos =
            materialReceiveRecordService.selectBySelectMaterialReceiveRecordDto(selectMaterialReceiveRecordDto);

        // 转换响应结构
        List<MaterialReceiveRecordListResponseVo> targetList =
            Lists.newArrayListWithCapacity(materialReceiveRecordDtos.size());
        for (MaterialReceiveRecordDto item : materialReceiveRecordDtos) {

            MaterialReceiveRecordListResponseVo target =
                materialReceiveRecordConverter.recordListFromMaterialReceiveRecordListRequestVo(item);

            if (target.getInvalidDate().equals(DefaultDateEnum.DEFAULT_DATE.getDate())) {
                // 作废时间 为创建的时放置 的默认时间 页面不展示
                target.setInvalidDate(null);
            }

            target.setStatusDesc(MaterialReceiveRecordStatusEnum.getByCode(target.getStatus()).getDesc());

            targetList.add(target);
        }
        return targetList;

    }

    /**
     * 领用登记
     */
    @PostMapping("/register")
    public Object materialReceiveRegister(@RequestBody List<MaterialReceiveRegisterItemDto> list) {

        materialReceiveRecordService.materialReceiveRegister(list);

        return Collections.emptyMap();
    }

    /**
     * 领用作废
     */
    @PostMapping("/invalid")
    public Object materialReceiveInvalid(@RequestParam("receiveId") long receiveId) {

        materialReceiveRecordService.materialReceiveInvalid(receiveId);

        return Collections.emptyMap();

    }

}
