package com.labway.lims.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 物料领用 记录 请求参数Vo
 *
 * <AUTHOR>
 * @since 2023/5/9 17:01
 */
@Getter
@Setter
public class MaterialReceiveRecordListRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 领用时间 开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date beginReceiverDate;

    /**
     * 领用时间 结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endReceiverDate;
}
