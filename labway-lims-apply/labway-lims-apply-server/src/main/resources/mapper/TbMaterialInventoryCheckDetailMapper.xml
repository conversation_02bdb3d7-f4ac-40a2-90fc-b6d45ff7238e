<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbMaterialInventoryCheckDetailMapper">


    <insert id="batchAddMaterialInventoryCheckDetail">
        INSERT INTO tb_material_inventory_check_detail (
        detail_id,
        check_id,
        check_no,
        inventory_id,
        material_id,
        material_code,
        material_name,
        specification,
        batch_no,
        manufacturers,
        main_unit,
        main_inventory,
        actual_main_inventory,
        main_profit,
        assist_unit,
        assist_inventory,
        actual_assist_inventory,
        assist_profit,
        unit_conversion_rate,
        valid_date,
        group_id,
        group_name,
        is_delete,
        org_id,
        org_name,
        create_date,
        update_date,
        updater_id,
        updater_name,
        creator_id,
        creator_name
        )
        values
        <foreach collection="conditions" item="item" index="index" separator=",">
            (
            #{item.detailId},
            #{item.checkId},
            #{item.checkNo},
            #{item.inventoryId},
            #{item.materialId},
            #{item.materialCode},
            #{item.materialName},
            #{item.specification},
            #{item.batchNo},
            #{item.manufacturers},
            #{item.mainUnit},
            #{item.mainInventory},
            #{item.actualMainInventory},
            #{item.mainProfit},
            #{item.assistUnit},
            #{item.assistInventory},
            #{item.actualAssistInventory},
            #{item.assistProfit},
            #{item.unitConversionRate},
            #{item.validDate},
            #{item.groupId},
            #{item.groupName},
            #{item.isDelete},
            #{item.orgId},
            #{item.orgName},
            #{item.createDate},
            #{item.updateDate},
            #{item.updaterId},
            #{item.updaterName},
            #{item.creatorId},
            #{item.creatorName}
            )
        </foreach>

    </insert>
</mapper>
