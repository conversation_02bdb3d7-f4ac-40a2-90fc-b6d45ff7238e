<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbMaterialDeliveryDetailMapper">


    <insert id="batchAddMaterialDeliveryDetails">
        INSERT INTO tb_material_delivery_detail (
        detail_id,
        delivery_no,
        material_id,
        material_code,
        material_name,
        specification,
        batch_no,
        manufacturers,
        main_unit,
        delivery_main_number,
        assist_unit,
        delivery_assist_number,
        unit_conversion_rate,
        valid_date,
        org_id,
        org_name,
        create_date,
        update_date,
        updater_id,
        updater_name,
        creator_id,
        creator_name,
        is_delete,
        material_barcode
        )
        values
        <foreach collection="conditions" item="item" index="index" separator=",">
            (
            #{item.detailId},
            #{item.deliveryNo},
            #{item.materialId},
            #{item.materialCode},
            #{item.materialName},
            #{item.specification},
            #{item.batchNo},
            #{item.manufacturers},
            #{item.mainUnit},
            #{item.deliveryMainNumber},
            #{item.assistUnit},
            #{item.deliveryAssistNumber},
            #{item.unitConversionRate},
            #{item.validDate},
            #{item.orgId},
            #{item.orgName},
            #{item.createDate},
            #{item.updateDate},
            #{item.updaterId},
            #{item.updaterName},
            #{item.creatorId},
            #{item.creatorName},
            #{item.isDelete},
            #{item.materialBarcode}
            )
        </foreach>

    </insert>
</mapper>
