<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbApplySampleItemMapper">
    <insert id="insertBatch">
        insert into tb_apply_sample_item (apply_sample_item_id, apply_sample_id, apply_id, test_item_id, test_item_code,
        test_item_name, item_type, out_test_item_id, out_test_item_code, out_test_item_name,
        sample_type_name, sample_type_code, tube_code, tube_name, urgent,
        "count",split_code,is_outsourcing,export_org_id,
        export_org_name, group_id, group_name,remark,create_date, update_date,
        creator_id, creator_name, updater_id, updater_name, is_delete,is_free,fee_price,actual_fee_price,item_source)
        values
        <foreach collection="list" item="item" separator=",">
            ( #{item.applySampleItemId},
            #{item.applySampleId},
            #{item.applyId},
            #{item.testItemId},
            #{item.testItemCode},
            #{item.testItemName},
            #{item.itemType},
            #{item.outTestItemId},
            #{item.outTestItemCode},
            #{item.outTestItemName},
            #{item.sampleTypeName},
            #{item.sampleTypeCode},
            #{item.tubeCode},
            #{item.tubeName},
            #{item.urgent},
            #{item.count},
            #{item.splitCode},
            #{item.isOutsourcing},
            #{item.exportOrgId},
            #{item.exportOrgName},
            #{item.groupId},
            #{item.groupName},
            #{item.remark},
            #{item.createDate},
            #{item.updateDate},
            #{item.creatorId},
            #{item.creatorName},
            #{item.updaterId},
            #{item.updaterName},
            #{item.isDelete},
            #{item.isFree},
            #{item.feePrice},
            #{item.actualFeePrice},
            #{item.itemSource})
        </foreach>
    </insert>

    <select id="selectByBarcode" resultType="com.labway.lims.apply.api.dto.ApplySampleItemDto">
        select tasi.* from tb_apply_sample_item tasi
        inner join tb_apply_sample tas on tas.apply_sample_id = tasi.apply_sample_id and tas.is_delete = 0
        where tasi.is_delete = 0 and tas.barcode = #{barcode} and tas.org_id = #{orgId}
    </select>
</mapper>
