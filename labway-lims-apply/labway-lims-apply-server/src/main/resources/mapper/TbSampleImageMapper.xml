<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.apply.mapper.TbSampleImageMapper">

    <insert id="insertBatch">
        INSERT INTO tb_sample_image (sample_image_id, apply_id,
        sample_id, apply_sample_id, item_type,
        image_name,image_url, is_delete,
        create_date, creator_id, creator_name, update_date, updater_id,
        updater_name)
        VALUES
        <foreach collection="sampleImageDtos" item="item" separator=",">
            (
            #{item.sampleImageId},
            #{item.applyId},
            #{item.sampleId},
            #{item.applySampleId},
            #{item.itemType},
            #{item.imageName},
            #{item.imageUrl},
            #{item.isDelete},
            #{item.createDate},
            #{item.creatorId},
            #{item.creatorName},
            #{item.updateDate},
            #{item.updaterId},
            #{item.updaterName}
            )
        </foreach>
    </insert>

</mapper>
