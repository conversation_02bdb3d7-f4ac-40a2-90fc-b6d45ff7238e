package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.*;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

public interface ApplyLogisticsService {


    /**
     * 添加物流申请单
     */
    void add(ApplyLogisticsDto applyLogistics);

    /**
     * 根据 ID 查询物流申请单
     */
    ApplyLogisticsDto selectByApplyLogisticsId(long applyLogisticsId);

    /**
     * 补录物流样本信息
     */
    ApplyInfo supplementLogisticsSampleInformation(LogisticsTestApplyDto testApply);

    /**
     * 根据 ID 更新物流申请单
     */
    boolean updateById(ApplyLogisticsDto applyLogistics);

    /**
     * 查询物流申请单 根据 applyId
     *
     * @return
     */
    List<SimpleLogisticsSampleDto> selectByApplyIds(Collection<Long> applyIds);

    /**
     * 同步中心物流样本信息
     */
    void syncCenterLogisticsSample(BusinessLogisticsApplyDto logisticsApply);

    Set<Long> selectUnSyncInfoSampleByDate(Date start, Date end);

    /**
     * 物流补录 - 追加
     */
    Long additionalApplyInfo(long applyId, long applyLogisticsSampleId);

}
