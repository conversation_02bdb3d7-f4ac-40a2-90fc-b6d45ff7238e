package com.labway.lims.apply.api.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

/**
 * <pre>
 * OutsourcingTestItemStatisticsDto
 * 外送项目统计
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/2/18 14:11
 */
@Getter
@Setter
public class OutsourcingTestItemStatisticsDto implements Serializable {

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;
    /**
     * 数量合计
     */
    private Integer countSum;

    /**
     * 汇总行
     */
    private List<TestItemStatisticsItem> itemList = new LinkedList<>();

    @Getter
    @Setter
    public static class TestItemStatisticsItem implements Serializable {
        /**
         * 外送机构名称
         */
        private Long exportOrgId;
        private String exportOrgName;

        /**
         * 检验项目编码
         */
        private String testItemCode;
        /**
         * 检验项目名称
         */
        private String testItemName;
        /**
         * 数量
         */
        private Integer count;

    }
}
