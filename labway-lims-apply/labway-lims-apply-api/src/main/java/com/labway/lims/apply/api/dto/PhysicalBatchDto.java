package com.labway.lims.apply.api.dto;

import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 体检批次
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
public class PhysicalBatchDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long physicalBatchId;

    /**
     * 批次号
     */
    private String physicalBatchNumber;

    /**
     * 体检单位
     */
    private Long physicalCompanyId;

    /**
     * 体检单位名称
     */
    private String physicalCompanyName;

    /**
     * 导入日期
     */
    private Date importDate;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构
     */
    private String orgName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 1:删除 0:未删除
     * @see YesOrNoEnum
     * @see YesOrNoEnum
     */
    private Integer isDelete;
}
