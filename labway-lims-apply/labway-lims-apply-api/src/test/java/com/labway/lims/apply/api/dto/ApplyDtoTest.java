package com.labway.lims.apply.api.dto;


import cn.hutool.core.lang.ObjectId;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.io.FileInputStream;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

public class ApplyDtoTest {
    @Test
    public void test()throws Exception {

        final FileInputStream fileInputStream = new FileInputStream("/Users/<USER>/Software/projects/JRebelCrack/target/classes/com/starxg/janetfilter/plugin/jrebel/SDKLicenseInfoImplCrack.class");

        System.out.println(JSON.toJSONString(new ApplyDto(), SerializerFeature.WRITE_MAP_NULL_FEATURES, SerializerFeature.PrettyFormat));
    }

	@Test
	public void t3() {
		List<Person> list = new ArrayList<>(List.of(
				new Person("张三", "男", "123456789012345678", 18, LocalDate.of(2024, 10, 12), "a", 90),
				new Person("张三", "男", "123456789012345678", 0, LocalDate.of(2024, 10, 13), "a", 91),
				new Person("张三", "男", "123456789012345678", 0, LocalDate.of(2024, 10, 11), "b", 92),
				new Person("张三1", "男", "", 18, LocalDate.of(2024, 10, 11), "a", 93),
				new Person("张三1", "男", "", 14, LocalDate.of(2023, 9, 12), "b", 94),
				new Person("张三1", "女", "", 14, LocalDate.of(2023, 9, 12), "a", 95),
				new Person("张三1", "女", "", 14, LocalDate.of(2023, 9, 12), "b", 96),
				new Person("张三1", "男", "", 17, LocalDate.of(2023, 9, 13), "a", 97),
				new Person("张三", "男", "sadf132", 0, LocalDate.of(2024, 10, 11), "a", 98),
				new Person("sdf三", "男", "", 0, LocalDate.of(2024, 10, 11), "a", 99),
				new Person("李四", "男", "123456789012345顺利开幕的法兰克", 0, LocalDate.of(2024, 10, 11), "a", 88)
		));
		list.sort(Comparator.comparing(Person::getCreateDate).reversed());
		for (int i = 0; i < list.size() - 1; i++) {
			Person person = list.get(i);
			for (int j = i + 1; j < list.size() - 1; j++) {
				Person item = list.get(j);
				if (!item.getOrg().equals(person.getOrg())) {
					continue;
				}
				if ((StringUtils.isNotBlank(item.idCard) && StringUtils.isNotBlank(person.idCard))) {
					if (item.hashOrgIdCard() == person.hashOrgIdCard()) {
						item.setGroupId(person.groupId);
						continue;
					} else {
						continue;
					}
				}
				if (StringUtils.isNotBlank(item.idCard) && StringUtils.isBlank(person.idCard)) {
					continue;
				}
				if (StringUtils.isBlank(item.idCard) && StringUtils.isNotBlank(person.idCard)) {
					continue;
				}
				if (Objects.equals(item.getName(), person.getName()) && Objects.equals(item.getSex(), person.getSex())) {
					int agex = person.getAge() - item.getAge();

					if (agex <= 3 && agex >= 0) {
						item.setGroupId(person.groupId);
					}
				}
			}
		}
		Map<Integer, List<Person>> collect = list.stream().collect(Collectors.groupingBy(Person::getGroupId));
		Collection<List<Person>> values = collect.values();
		HashMap<String, List<Integer>> map = new HashMap<>();
		for (List<Person> value : values) {
			for (Person person : value) {
				String key = person.getName() + "-" + person.getSex() + "-" +person.getGroupId();
				List<Integer> collect1 = person.getReport().stream().collect(Collectors.toList());
				map.computeIfAbsent(key, k -> new ArrayList<>()).addAll(collect1);
			}
		}
		collect.forEach((key, value)-> {
			System.out.println("--------------------");
			value.forEach(System.out::println);
		});
		map.forEach((key, value)-> {
			System.out.println("--------------------");
			System.out.println(key + "   --|");
			value.forEach(System.out::println);
		});

	}

    @Test
    public void t1(){
        String originalString = "2131";
        String numberString = "2131000003";
        String lastSixDigits;
        if (numberString != null && !numberString.isEmpty()) {
            lastSixDigits = numberString.substring(Math.max(numberString.length() - 6, 0)); // 取最后6位字符串
        } else {
            lastSixDigits = "000000";
        }
        int number = Integer.parseInt(lastSixDigits); // 解析为整数
        number++; // 自增
        String formattedNumber = String.format("%06d", number); // 格式化为6位数的字符串
        // 拼接到原始字符串后面
       String a=   originalString + formattedNumber;
        System.out.println(a);
    }

	@Test
	public void t2() {
		List<Person> list = List.of(
				new Person("张三", "男", "123456789012345678", 18, LocalDate.of(2024,10,12),"a",1),
				new Person("张三", "男", "123456789012345678", 0, LocalDate.of(2024,10,13),"a",2),
				new Person("张三", "男", "123456789012345678", 0, LocalDate.of(2024,10,11),"b",3),
				new Person("张三1", "男", "",18, LocalDate.of(2024,10,11),"a",4),
				new Person("张三1", "男", "",14, LocalDate.of(2023,9,12),"a",5),
				new Person("张三1", "男", "",17, LocalDate.of(2023,9,13),"a",6),
				new Person("张三", "男", "sadf132",0, LocalDate.of(2024,10,11),"a",7),
				new Person("sdf三", "男", "",0, LocalDate.of(2024,10,11),"a",8),
				new Person("李四", "男", "123456789012345顺利开幕的法兰克",0, LocalDate.of(2024,10,11),"a",9)
		);
		HashMap<Person, List<Person>> personListHashMap = new HashMap<>();
		list.stream().sorted(Comparator.comparing(Person::getCreateDate)).forEach(item -> {

			personListHashMap.computeIfAbsent(item, k -> new ArrayList<>()).add(item);
		});
		personListHashMap.forEach((key, value)-> {
			System.out.println("--------------------");
			value.forEach(System.out::println);
		});
		/*Map<String, Map<String, List<Person>>> collect = list.stream().collect(Collectors.groupingBy(Person::getOrg,
				Collectors.groupingBy(Person::getIdCard, Collectors.toList())));
		collect.forEach((key, value) -> {
			List<Person> people = value.getOrDefault("", new ArrayList<>());
			people.sort(Comparator.comparing(Person::getCreateDate).reversed());
			people.iterator().forEachRemaining(item -> {

			});
		});*/


//		List<Person> nullCard = list.stream().filter(item -> StringUtils.isBlank(item.getIdCard())).collect(Collectors.toList());
//		Map<String, List<Person>> map = list.stream().collect(Collectors.groupingBy(item -> item.getName() + item.getSex() + item.getIdCard()));
//		for (Map.Entry<String, List<Person>> stringListEntry : map.entrySet()) {
//
//		}
//		list.stream().collect(Collectors.groupingBy(item -> StringUtils.isNotBlank(item.getIdCard(,"a"), LocalDate.of(2024,10,11")), new HashMap<>(), Collectors.toList()));

	/*	List<List<Person>> collect = list.stream().collect(Collectors.groupingBy(item -> StringUtils.isNotBlank(item.getIdCard()),
				Collectors.groupingBy(item -> {
					if (StringUtils.isNotBlank(item.getIdCard())) {
						return item.getIdCard();
					} else {
						return item.getName() + item.getSex();
					}
				}, Collectors.toList()))).values().stream().flatMap(item -> item.values().stream()).collect(Collectors.toList());
		for (List<Person> people : collect) {
			System.out.println(JSONUtil.toJsonStr(people));
			System.out.println("0--------------");
		}*/
	}

	@Test
	public void test4() {
		System.out.println(ObjectId.next());
		System.out.println(ObjectId.next());
		System.out.println(ObjectId.next());
		System.out.println(ObjectId.next());
		System.out.println(ObjectId.next());
		System.out.println(ObjectId.next());
		System.out.println(ObjectId.next().length());
		String fileName = ObjectId.next() + "名字-终审";
		System.out.println(fileName);
		System.out.println(fileName.substring(24));
	}

	@Data
	public static class Person {
		private String name;
		private String sex;
		private String idCard;
		private Integer age;
		private LocalDate createDate;
		private String org;
		private Integer groupId;

		private List<Integer> report = List.of(RandomUtil.randomInt(),RandomUtil.randomInt(),RandomUtil.randomInt());

		public Person(String name, String sex, String idCard, Integer age, LocalDate createDate, String org, Integer groupId) {
			this.name = name;
			this.sex = sex;
			this.idCard = idCard;
			this.age = age;
			this.createDate = createDate;
			this.org = org;
			this.groupId = groupId;
		}

		@Override
		public boolean equals(Object o) {
			/*if (this == o) return true;
			if (o == null || getClass() != o.getClass()) return false;
			Person person = (Person) o;
			if ((StringUtils.isNotBlank(this.idCard) && StringUtils.isNotBlank(person.idCard))) {
				if (this.hashOrgIdCard() == person.hashOrgIdCard()) {
					return true;
				} else {
					return false;
				}
			}
			if (StringUtils.isNotBlank(this.idCard) && StringUtils.isBlank(person.idCard)) {
				return false;
			}
			if (StringUtils.isBlank(this.idCard) && StringUtils.isNotBlank(person.idCard)) {
				return false;
			}
			if (this.hashOrgNameSex() == person.hashOrgNameSex()) {

				int agex;
				boolean after = this.createDate.isAfter(person.createDate);
				if (after) {
					agex = this.age - person.age;
				} else {
					agex = person.age - this.age;
				}
				if (agex <= 3 && agex >= 0) {
					return true;
				}
			}
			return false;*/
			return this == o;
		}

		@Override
		public int hashCode() {
			return Objects.hash(org);
		}
		public int hashOrgIdCard() {
			return Objects.hash(idCard, org);

		}
		public int hashOrgNameSex() {
			return Objects.hash(name, sex, org);
		}

		public String getKey() {
			return name + sex + idCard;
		}


	}
}
