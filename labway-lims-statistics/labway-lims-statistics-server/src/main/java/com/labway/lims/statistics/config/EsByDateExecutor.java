package com.labway.lims.statistics.config;

import cn.hutool.core.thread.NamedThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class EsByDateExecutor implements InitializingBean, DisposableBean {

    private ThreadPoolExecutor executorService;

    /**
     * 获取线程池
     */
    public ThreadPoolExecutor getPool() {
        return executorService;
    }

    @Override
    public void destroy() throws Exception {
        executorService.shutdownNow();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        executorService = new ThreadPoolExecutor(10, 30,
                180L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(180), new NamedThreadFactory("lims-es-by-date", null, true,
                (t, e) -> log.error("Thread [{}]] threw exception", t.getName(), e)), new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
