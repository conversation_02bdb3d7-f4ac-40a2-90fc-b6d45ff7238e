package com.labway.lims.statistics.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LabwayDateUtil;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.ScrollPage;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.dto.PushSampleStatisticsInfoDto;
import com.labway.lims.statistics.dto.UrgentSampleDto;
import com.labway.lims.statistics.api.client.SampleStatisticsService;
import com.labway.lims.statistics.vo.*;
import com.swak.frame.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * SampleStatisticsController
 * 条码综合信息查询
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/27 14:34
 */
@RestController
@RequestMapping("/sample-statistics")
@Slf4j
public class SampleStatisticsController extends BaseController {

    @Resource
    private SampleStatisticsService sampleStatisticsService;

    @DubboReference
    private ElasticSearchSampleService elasticSearchSampleService;

    @DubboReference
    private TestItemService testItemService;

    /**
     * 查询列表信息
     */
    @PostMapping("/samples")
    public SampleStatisticsVo sampleStatistics(@RequestBody SampleStatisticsQueryVo vo) {
        return sampleStatisticsService.selectSampleStatistics(vo);
    }

    /**
     * 查询检验项目
     */
    @GetMapping("/testItems")
    public List<SampleTestItemStatisticsVo> sampleTestItemStatistics(@RequestParam Long applySampleId) {
        return sampleStatisticsService.selectSampleTestItemStatistics(applySampleId);
    }

    /**
     * 查找未审核超时的样本列表
     */
    @PostMapping("/auditTimeoutList")
    public Object auditTimeoutList(@RequestBody TimeoutReminderQueryVo reminderQueryVo) {
        LoginUserHandler.User user = LoginUserHandler.get();
        final SampleEsQuery query = new SampleEsQuery();
        query.setIsAudit(YesOrNoEnum.NO.getCode());
        query.setIsTwoPick(YesOrNoEnum.YES.getCode());
        query.setItemTypes(Set.of(reminderQueryVo.getTypeName()));
        query.setGroupIds(Set.of(user.getGroupId()));
        // 过滤已终止的样本
        query.setExcludeSampleStatus(List.of(SampleStatusEnum.STOP_TEST.getCode()));
        query.setPageNo(1);
        query.setPageSize(Integer.MAX_VALUE);
        //正排 根据样本创建日期
        query.setSorts(
                Lists.newArrayList(SampleEsQuery.Sort.builder().filedName("createDate").order("ASC").build()));
        ScrollPage<BaseSampleEsModelDto> modelDtoScrollPage = elasticSearchSampleService.searchAfter(null, query);
        //组装数据
        List<TimeoutReminderSampleVo> finalsampleVoList = new ArrayList<>();
        List<BaseSampleEsModelDto> modelDtos = modelDtoScrollPage.getData();
        if (!CollectionUtils.isEmpty(modelDtos)) {
            List<TimeoutReminderSampleVo> sampleVoList = new ArrayList<>();
            Set<Long> testItemIds = new HashSet<>();
            for (BaseSampleEsModelDto modelDto : modelDtos) {
                //组装项目
                if (!CollectionUtils.isEmpty(modelDto.getTestItems())) {
                    //平铺数据
                    for (BaseSampleEsModelDto.TestItem testItem : modelDto.getTestItems()) {
                        TimeoutReminderSampleVo sampleVo = new TimeoutReminderSampleVo();
                        BeanUtils.copyProperties(testItem, sampleVo);
                        BeanUtils.copyProperties(modelDto, sampleVo);
                        sampleVoList.add(sampleVo);
                        //收集所有的testItem Id
                        testItemIds.add(testItem.getTestItemId());
                    }
                }
            }
            //批量查找项目信息
            Map<Long, TestItemDto> testItemMap = testItemService.selectByTestItemIds(testItemIds).stream()
                    .collect(Collectors.toMap(TestItemDto::getTestItemId, Function.identity()));
            if (!CollectionUtils.isEmpty(testItemMap)) {
                Date now = new Date();
                for (TimeoutReminderSampleVo sampleVo : sampleVoList) {
                    //进行时间的处理
                    TestItemDto testItemDto = testItemMap.get(sampleVo.getTestItemId());
                    if (sampleVo.getSignDate() != null && testItemDto != null && testItemDto.getTestDate() != null
                            && 0 != testItemDto.getTestDate()) {
                        //公式 超时时间= 当前时间-（创建时间+1）-限制天数
                        int outTime = ((int) ((now.getTime() - sampleVo.getCreateDate().getTime()) / 1000 / 60 / 60 / 24) - 1 - testItemDto.getTestDate());
                        if (outTime > 0) {
                            sampleVo.setCheckLimitDay(testItemDto.getTestDate());
                            sampleVo.setTimeOutDays(outTime);
                            finalsampleVoList.add(sampleVo);
                        }
                    }
                }

                //重排序 根据超时时间倒排
                finalsampleVoList.sort((o1, o2) -> o2.getTimeOutDays() - o1.getTimeOutDays());
            }


        }
        return finalsampleVoList;
    }

    /**
     * 急诊样本查询
     */
    @PostMapping("urgent-sample-statistics")
    public UrgentSampleStatisticsResultVo urgentSampleStatistics(@RequestBody UrgentSampleStatisticsQueryVo vo) {
        if (Objects.isNull(vo.getStartDate()) || Objects.isNull(vo.getEndDate())) {
            throw new IllegalArgumentException("日期错误");
        }

        final SampleEsQuery query = new SampleEsQuery();
        query.setPageNo(NumberUtils.INTEGER_ONE);
        query.setPageSize(Integer.MAX_VALUE);
        query.setOrgId(LoginUserHandler.get().getOrgId());
        query.setItemUrgent(YesOrNoEnum.YES.getCode());
        query.setItemTypes(Set.of(ItemTypeEnum.ROUTINE.name(), ItemTypeEnum.OUTSOURCING.name()));
        query.setStartCreateDate(vo.getStartDate()).setEndCreateDate(vo.getEndDate());

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(vo.getHspOrgIds())) {
            query.setHspOrgIds(new HashSet<>(vo.getHspOrgIds()));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(vo.getGroupIds())) {
            query.setGroupIds(new HashSet<>(vo.getGroupIds()));
        }
        ScrollPage<BaseSampleEsModelDto> modelDtoScrollPage = elasticSearchSampleService.searchAfter(null, query);

        List<UrgentSampleDto> urgentSampleDtos = modelDtoScrollPage.getData().stream().map(e -> {
            UrgentSampleDto urgentSampleDto = JSON.parseObject(JSON.toJSONString(e), UrgentSampleDto.class);
            // 样本状态
            urgentSampleDto.setSampleStatus(SampleStatusEnum.getStatusByCode(e.getSampleStatus()).getDesc());
            // 急诊项目编码
            urgentSampleDto.setTestItemCodes(e.getTestItems().stream()
                    .filter(item -> Objects.equals(item.getUrgent(), YesOrNoEnum.YES.getCode()))
                    .filter(item -> Objects.equals(item.getStopStatus(), StopTestStatus.NO_STOP_TEST.getCode()))
                    .map(BaseSampleEsModelDto.TestItem::getTestItemCode)
                    .collect(Collectors.joining(StringPool.COMMA)));
            // 急诊项目名称
            urgentSampleDto.setTestItemNames(e.getTestItems().stream()
                    .filter(item -> Objects.equals(item.getUrgent(), YesOrNoEnum.YES.getCode()))
                    .filter(item -> Objects.equals(item.getStopStatus(), StopTestStatus.NO_STOP_TEST.getCode()))
                    .map(BaseSampleEsModelDto.TestItem::getTestItemName)
                    .collect(Collectors.joining(StringPool.COMMA)));
            urgentSampleDto.setFinalCheckDate(LabwayDateUtil.getNonDefaultDbDate(urgentSampleDto.getFinalCheckDate()));
            return urgentSampleDto;
        }).sorted(Comparator.comparing(UrgentSampleDto::getSignDate))
                .filter(e -> StringUtils.isNotBlank(e.getTestItemCodes()) && StringUtils.isNotBlank(e.getTestItemNames()))
                .collect(Collectors.toList());

        UrgentSampleStatisticsResultVo statisticsResultVo = new UrgentSampleStatisticsResultVo();
        statisticsResultVo.setRecords(urgentSampleDtos);
        statisticsResultVo.setTotalCount(urgentSampleDtos.size());

        return statisticsResultVo;
    }


    /**
     * 推送样本统计信息到业务中台
     */
    @PostMapping("/pushSampleStatisticsInfo")
    public PushSampleStatisticsInfoVo pushSampleStatisticsInfo(@RequestBody PushSampleStatisticsInfoDto dto) {
        return sampleStatisticsService.pushSampleStatisticsInfo(dto);
    }

    /**
     * 推送样本统计信息到业务中台
     */
    @PostMapping("/pushSampleStatisticsInfoNew")
    public PushSampleStatisticsInfoVo pushSampleStatisticsInfoNew(@RequestBody PushSampleStatisticsInfoDto dto) {
        return sampleStatisticsService.pushSampleStatisticsInfoNew(dto);
    }

}
