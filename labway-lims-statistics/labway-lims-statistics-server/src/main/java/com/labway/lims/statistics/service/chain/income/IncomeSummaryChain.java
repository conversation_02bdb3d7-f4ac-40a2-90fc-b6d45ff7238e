package com.labway.lims.statistics.service.chain.income;

import com.labway.lims.statistics.service.chain.EsDataToSampleTestItemCommand;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class IncomeSummaryChain extends ChainBase implements InitializingBean {


    @Resource
    private IncomeSummaryPaddingDataCommand incomeSummaryPaddingDataCommand;

    @Resource
    private EsDataToSampleTestItemCommand esDataToSampleTestItemCommand;
    @Resource
    private IncomeSummaryCalculateResultCommand incomeSummaryCalculateResultCommand;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 填充数据
        addCommand(incomeSummaryPaddingDataCommand);

        // 整理数据，处理财务套餐
        addCommand(esDataToSampleTestItemCommand);

        // 计算结果  组装数据返回前端
        addCommand(incomeSummaryCalculateResultCommand);

        addCommand(e -> PROCESSING_COMPLETE);
    }
}
