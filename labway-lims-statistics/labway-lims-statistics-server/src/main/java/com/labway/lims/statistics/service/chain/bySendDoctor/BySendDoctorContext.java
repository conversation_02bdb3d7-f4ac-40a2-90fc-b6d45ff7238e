package com.labway.lims.statistics.service.chain.bySendDoctor;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.apply.api.dto.HspOrgSendDoctorStatisticsResponseDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.statistics.service.chain.byPlateform.PlatformStatisticsContext;
import com.labway.lims.statistics.vo.HspOrgSendDoctorStatisticsRequestVo;
import org.apache.commons.chain.Context;

public class BySendDoctorContext extends PlatformStatisticsContext {


    /**
     * ES查询参数
      */
    public static final String SAMPLE_ES_QUERY = "SAMPLE_ES_QUERY_" + IdUtil.objectId();

    public HspOrgSendDoctorStatisticsResponseDto getSendDoctorResult() {
        return (HspOrgSendDoctorStatisticsResponseDto) get(RESULT);
    }
    public SampleEsQuery getSampleEsQuery() {
        return (SampleEsQuery) get(SAMPLE_ES_QUERY);
    }

    public HspOrgSendDoctorStatisticsRequestVo getQuery() {
        return (HspOrgSendDoctorStatisticsRequestVo) get(FINANCE_STATISTICS_QUERY);
    }


    public static BySendDoctorContext from(Context context) {
        return (BySendDoctorContext)context;
    }
}
