package com.labway.lims.statistics.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Slf4j
public class DetectionMergeStrategy extends AbstractMergeStrategy {

    private final List<Integer> mergeRows; //检测方数据条数小计行数
    private final Map<Integer, List<RowRangeDto>> strategyMap; //需要合并行的坐标

    private final Map<Sheet, Set<Integer>> MERGED_ROW = new HashMap<>();

    private final Integer col;

    public DetectionMergeStrategy(List<Integer> mergeRows, Map<Integer, List<RowRangeDto>> strategyMap, Integer col) {
        this.mergeRows = mergeRows;
        this.strategyMap = strategyMap;
        this.col = col;
    }

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
        // 当前行
        int curRowIndex = cell.getRowIndex();
        // 当前列
        int curColIndex = cell.getColumnIndex();

        if (Objects.nonNull(mergeRows) && mergeRows.contains(curRowIndex) &&
                MERGED_ROW.computeIfAbsent(sheet, k -> new HashSet<>()).add(curRowIndex)) {
            if (Objects.nonNull(strategyMap) && strategyMap.containsKey(curRowIndex)) {
                strategyMap.get(curRowIndex).forEach(rowRangeDto -> {
                    // 添加一个合并请求
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(curRowIndex, curRowIndex, rowRangeDto.getStart(), rowRangeDto.getEnd()));
                });
            } else {
                // 添加一个合并请求
                sheet.addMergedRegionUnsafe(new CellRangeAddress(curRowIndex, curRowIndex, 0, col));
            }
        }
    }
}