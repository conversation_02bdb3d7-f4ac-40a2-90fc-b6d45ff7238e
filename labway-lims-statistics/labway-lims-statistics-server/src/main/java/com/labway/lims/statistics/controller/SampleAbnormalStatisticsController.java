package com.labway.lims.statistics.controller;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.http.HttpDownloader;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.EsConfig;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.stream.StreamUtils;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleAbnormalDto;
import com.labway.lims.apply.api.dto.SampleAbnormalStatisticsDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SelectSampleAbnormalDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.service.SampleAbnormalService;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.DictService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.config.EsByDateExecutor;
import com.labway.lims.statistics.dto.SampleAbnormalStatisticsRequestDto;
import com.labway.lims.statistics.vo.HspStatisticsVo;
import com.labway.lims.statistics.vo.ReasonStatisticsVo;
import com.labway.lims.statistics.vo.SampleAbnormalStatisticsRequestVo;
import com.labway.lims.statistics.vo.SampleUnqualifiedStatisticsRequestVo;
import com.labway.lims.statistics.vo.SampleUnqualifiedStatisticsResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 送检机构异常样本统计
 */
@Slf4j
@RestController
@RequestMapping("/sample-abnormal-statistics")
public class SampleAbnormalStatisticsController extends BaseController {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;

    @DubboReference
    private SampleAbnormalService sampleAbnormalService;

    @DubboReference
    private DictService dictService;

    @DubboReference
    private TestItemService testItemService;

    @Resource
    private EsConfig esConfig;

    @Resource
    private EsByDateExecutor esByDateExecutor;

    private static final List<String> fixedHeaders = List.of("序号", "送检机构", "送检机构编码", "总条码数", "总人数", "总标本数");
    private static final List<String> fixedHeaderFields = List.of("no", "hspOrgName", "hspOrgCode", "totalBarcodeCount", "totalPeopleCount", "totalSampleCount");

    // 样本性状code 正常 == 00102004-00000000
    private static final String samplePropertyCode = "00102004-00000000";
    /**
     * 根据机构统计
     */
    @PostMapping("/hsp-statistics")
    public Map<String, Object> hspStatistics(@RequestBody SampleAbnormalStatisticsRequestVo vo) {

        if (Objects.isNull(vo.getBeginSignDate()) || Objects.isNull(vo.getEndSignDate())) {
            throw new IllegalStateException("日期错误");
        }


        final Long orgId = LoginUserHandler.get().getOrgId();
        final List<SampleAbnormalStatisticsDto> list = new LinkedList<>(sampleAbnormalService.selectSampleAbnormalStatistics(vo.getBeginSignDate(), vo.getEndSignDate(), orgId));

        if (Objects.nonNull(vo.getHspOrgId())) {
            list.removeIf(e -> !Objects.equals(e.getHspOrgId(), vo.getHspOrgId()));
        }

        if (StringUtils.isNotBlank(vo.getAbnormalReasonCode())) {
            list.removeIf(e -> !Objects.equals(e.getAbnormalReasonCode(), vo.getAbnormalReasonCode()));
        }

        if (CollectionUtils.isEmpty(list)) {
            return Map.of("columns", Collections.emptyList(), "samples", Collections.emptyList());
        }


        // 类型
        final List<String> headers = list.stream().map(SampleAbnormalStatisticsDto::getAbnormalReasonName)
                .distinct().collect(Collectors.toCollection(LinkedList::new));

        // 行
        final Map<Long, List<SampleAbnormalStatisticsDto>> hspOrgSamples = list.stream()
                .collect(Collectors.groupingBy(SampleAbnormalStatisticsDto::getHspOrgId));

        final List<HspStatisticsVo> rows = new LinkedList<>();
        for (var e : hspOrgSamples.entrySet()) {
            final HspStatisticsVo v = new HspStatisticsVo();
            v.setHspOrgName(e.getValue().iterator().next().getHspOrgName());
            // 条码 + 异常登记ID，登记一次算一次，一个条码可以登记多次
            v.setBarcodeCount(e.getValue().stream().map(k -> k.getBarcode() + k.getSampleAbnormalId()).distinct().count());
            v.setPatientCount(e.getValue().stream().map(k -> k.getPatientName() + k.getPatientSex()).distinct().count());
            v.setSampleCount(e.getValue().stream().map(SampleAbnormalStatisticsDto::getBarcode).count());

            // 统计原因
            final Map<String, List<SampleAbnormalStatisticsDto>> map = e.getValue().stream()
                    .collect(Collectors.groupingBy(SampleAbnormalDto::getAbnormalReasonName));
            v.setCauses(new LinkedHashMap<>());

            for (var k : map.entrySet()) {
                v.getCauses().put(k.getKey(), k.getValue().size());
            }

            rows.add(v);
        }


        return Map.of("columns", headers, "samples", rows);
    }


    /**
     * 根据原因统计
     */
    @PostMapping("/reason-statistics")
    public Map<String, Object> reasonStatistics(@RequestBody SampleAbnormalStatisticsRequestVo vo) {

        if (Objects.isNull(vo.getBeginSignDate()) || Objects.isNull(vo.getEndSignDate())) {
            throw new IllegalStateException("日期错误");
        }


        final Long orgId = LoginUserHandler.get().getOrgId();
        final List<SampleAbnormalStatisticsDto> list = new LinkedList<>(sampleAbnormalService.selectSampleAbnormalStatistics(vo.getBeginSignDate(), vo.getEndSignDate(), orgId));

        if (Objects.nonNull(vo.getHspOrgId())) {
            list.removeIf(e -> !Objects.equals(e.getHspOrgId(), vo.getHspOrgId()));
        }

        if (StringUtils.isNotBlank(vo.getAbnormalReasonCode())) {
            list.removeIf(e -> !Objects.equals(e.getAbnormalReasonCode(), vo.getAbnormalReasonCode()));
        }

        if (CollectionUtils.isEmpty(list)) {
            return Map.of("columns", Collections.emptyList(), "rows", Collections.emptyList());
        }

        // 送检机构
        final List<String> headers = list.stream().map(SampleAbnormalStatisticsDto::getHspOrgName).distinct()
                .collect(Collectors.toCollection(LinkedList::new));

        // 行
        final List<ReasonStatisticsVo> rows = new LinkedList<>();

        // 有多少个异常原因 就有多少行
        for (String abnormalReasonName : list.stream().map(SampleAbnormalStatisticsDto::getAbnormalReasonName).collect(Collectors.toSet())) {
            final ReasonStatisticsVo v = new ReasonStatisticsVo();
            // 异常原因
            v.setAbnormalReasonName(abnormalReasonName);
            v.setOrgs(new LinkedHashMap<>());

            for (String header : headers) {
                v.getOrgs().put(header, list.stream().filter(e -> Objects.equals(header, e.getHspOrgName()))
                        .filter(e -> Objects.equals(abnormalReasonName, e.getAbnormalReasonName()))
                        .map(SampleAbnormalDto::getSampleAbnormalId)
                        .distinct()
                        .count());
            }

            rows.add(v);

        }

        return Map.of("columns", headers, "rows", rows);
    }

    /**
     * 不合格标本分布
     */
    @PostMapping("unqualified-statistics")
    public SampleUnqualifiedStatisticsResultVo selectSampleUnqualifiedStatistics(@RequestBody SampleUnqualifiedStatisticsRequestVo vo) {
        SampleUnqualifiedStatisticsResultVo resultVo = new SampleUnqualifiedStatisticsResultVo();

        if (Objects.isNull(vo.getStartDate()) || Objects.isNull(vo.getEndDate())) {
            throw new IllegalArgumentException("日期错误");
        }

        List<BaseSampleEsModelDto> sampleEsModelDtos = this.selectSamples(vo);

        if (CollectionUtils.isEmpty(sampleEsModelDtos)) {
            return resultVo;
        }
        Set<String> barcodes = sampleEsModelDtos.stream().map(BaseSampleEsModelDto::getBarcode).collect(Collectors.toSet());
        Map<Long, List<BaseSampleEsModelDto>> sampleEsModelMapByHspOrgId = sampleEsModelDtos.stream()
                .collect(Collectors.groupingBy(BaseSampleEsModelDto::getHspOrgId));

        // 总条码数，总人数 统计
        long totalBarcodeCount = 0, totalPeopleCount = 0;
        Map<Long, SampleUnqualifiedStatisticsResultVo.UnqualifiedSampleStatisticsItem> sampleStatisticsItemMap = new HashMap<>();
        for (Map.Entry<Long, List<BaseSampleEsModelDto>> entry : sampleEsModelMapByHspOrgId.entrySet()) {
            Long hspOrgId = entry.getKey();
            List<BaseSampleEsModelDto> sampleEsModelDtoList = entry.getValue();
            String hspOrgCode = sampleEsModelDtoList.get(0).getHspOrgCode();
            String hspOrgName = sampleEsModelDtoList.get(0).getHspOrgName();
            long barcodeCount = sampleEsModelDtoList.stream().map(BaseSampleEsModelDto::getBarcode).distinct().count();
            long peopleCount = sampleEsModelDtoList.stream().map(e -> StringUtils.isBlank(e.getPatientCard()) ? e.getHspOrgCode() + e.getPatientName() + e.getPatientSex() : e.getPatientCard()).distinct().count();

            SampleUnqualifiedStatisticsResultVo.UnqualifiedSampleStatisticsItem item = new SampleUnqualifiedStatisticsResultVo.UnqualifiedSampleStatisticsItem();
            item.setHspOrgCode(hspOrgCode);
            item.setHspOrgName(hspOrgName);
            item.setTotalBarcodeCount(barcodeCount);
            item.setTotalPeopleCount(peopleCount);
            item.setTotalSampleCount(barcodeCount);
            sampleStatisticsItemMap.put(hspOrgId, item);

            totalBarcodeCount += barcodeCount;
            totalPeopleCount += peopleCount;
        }
        SampleUnqualifiedStatisticsResultVo.UnqualifiedSampleStatisticsItem summary = new SampleUnqualifiedStatisticsResultVo.UnqualifiedSampleStatisticsItem();
        summary.setTotalBarcodeCount(totalBarcodeCount);
        summary.setTotalPeopleCount(totalPeopleCount);
        summary.setTotalSampleCount(totalBarcodeCount);

        // 查询所有异常原因
        List<SampleUnqualifiedStatisticsResultVo.ColumnItem> reasons = dictService.selectAbnormalReasonsFromBusinessCenter().stream()
                .map(e -> new SampleUnqualifiedStatisticsResultVo.ColumnItem(reasonCodeConvert(e.getDictCode()), e.getDictName())).collect(Collectors.toList());

        // 查询异常样本数据
        SelectSampleAbnormalDto selectSampleAbnormalDto = new SelectSampleAbnormalDto();
        selectSampleAbnormalDto.setOrgId(LoginUserHandler.get().getOrgId());
        selectSampleAbnormalDto.setRegistDateStart(vo.getStartDate());
        // selectSampleAbnormalDto.setRegistDateEnd(vo.getEndDate());
        List<SampleAbnormalDto> sampleAbnormalDtos = sampleAbnormalService.selectBySelectSampleAbnormalDto(selectSampleAbnormalDto)
                .stream()
                .filter(e -> barcodes.contains(e.getBarcode()))
                .filter(e -> CollectionUtils.isEmpty(vo.getHspOrgIds()) || vo.getHspOrgIds().contains(e.getHspOrgId()))
                .flatMap(e -> Arrays.stream(e.getAbnormalReasonCode().split(StringPool.COMMA)).map(code -> {
                    SampleAbnormalDto abnormalDto = new SampleAbnormalDto();
                    BeanUtils.copyProperties(e, abnormalDto);
                    abnormalDto.setAbnormalReasonCode(code);
                    return abnormalDto;
                }))
                .collect(Collectors.toList());

        Map<Long, List<SampleAbnormalDto>> sampleAbnormalMapByHspOrgId = sampleAbnormalDtos.stream()
                .collect(Collectors.groupingBy(SampleAbnormalDto::getHspOrgId));

        // 异常原因统计
        List<SampleUnqualifiedStatisticsResultVo.UnqualifiedSampleStatisticsItem> items = new ArrayList<>();
        for (Map.Entry<Long, SampleUnqualifiedStatisticsResultVo.UnqualifiedSampleStatisticsItem> entry : sampleStatisticsItemMap.entrySet()) {
            Long hspOrgId = entry.getKey();
            List<SampleAbnormalDto> sampleAbnormalDtoList = sampleAbnormalMapByHspOrgId.getOrDefault(hspOrgId, List.of());

            // 异常原因数据转换成字段
            SampleUnqualifiedStatisticsResultVo.UnqualifiedSampleStatisticsItem columnMap = reasons.stream()
                    .collect(SampleUnqualifiedStatisticsResultVo.UnqualifiedSampleStatisticsItem::new,
                            (map, reason) -> map.put(reason.getCode(), 0),
                            SampleUnqualifiedStatisticsResultVo.UnqualifiedSampleStatisticsItem::putAll);

            for (SampleAbnormalDto sampleAbnormalDto : sampleAbnormalDtoList) {
                columnMap.computeIfPresent(reasonCodeConvert(sampleAbnormalDto.getAbnormalReasonCode()), (k, v) -> (Integer) v + 1);
            }

            SampleUnqualifiedStatisticsResultVo.UnqualifiedSampleStatisticsItem statisticsItem = entry.getValue();
            columnMap.setHspOrgId(hspOrgId);
            columnMap.setHspOrgCode(statisticsItem.getHspOrgCode());
            columnMap.setHspOrgName(statisticsItem.getHspOrgName());
            columnMap.setTotalBarcodeCount(statisticsItem.getTotalBarcodeCount());
            columnMap.setTotalPeopleCount(statisticsItem.getTotalPeopleCount());
            columnMap.setTotalSampleCount(statisticsItem.getTotalSampleCount());
            items.add(columnMap);
        }
        items.sort(Comparator.comparing(SampleUnqualifiedStatisticsResultVo.UnqualifiedSampleStatisticsItem::getHspOrgId));

        resultVo.setColumns(reasons);
        resultVo.setItems(items);
        resultVo.setSummary(summary);

        return resultVo;
    }


    private List<BaseSampleEsModelDto> selectSamples(SampleUnqualifiedStatisticsRequestVo vo) {
        final Date startDate = vo.getStartDate();
        final Date endDate = vo.getEndDate();
        SampleEsQuery query = SampleEsQuery.builder()
                .pageNo(NumberUtils.INTEGER_ONE)
                .pageSize(esConfig.getMaxPageSize())
                // 未终止的
                .excludeStopStatus(Set.of(StopTestStatus.STOP_TEST_CHARGE.getCode(),
                        StopTestStatus.STOP_TEST_FREE.getCode()))
                .build();

        if (CollectionUtils.isNotEmpty(vo.getHspOrgIds())) {
            query.setHspOrgIds(Sets.newHashSet(vo.getHspOrgIds()));
        }
        if (CollectionUtils.isNotEmpty(vo.getApplyTypeCodes())) {
            query.setApplyTypes(Sets.newHashSet(vo.getApplyTypeCodes()));
        }

        final List<Pair<Date, Date>> datePairs = new ArrayList<>();

        long between = DateUtil.between(startDate, endDate, DateUnit.HOUR);
        boolean hasNext = true;
        Date start = startDate;
        if (between > 24) {
            do {
                Date end = DateUtil.offsetHour(start, 24);
                if (end.after(endDate)) {
                    hasNext = false;
                    end = endDate;
                }
                datePairs.add(Pair.of(DateUtil.beginOfDay(start), end));
                start = end;
            } while (hasNext);
        } else {
            datePairs.add(Pair.of(startDate, endDate));
        }

        // 根据拆分出来的时间段，分批并行查询数据
        @SuppressWarnings("all")
        CompletableFuture<List<BaseSampleEsModelDto>>[] completableFutures = datePairs.stream().map(datePair -> {
            return CompletableFuture.supplyAsync(() -> {
                SampleEsQuery sampleEsQuery = JSON.parseObject(JSON.toJSONString(query), SampleEsQuery.class);
                sampleEsQuery.setStartSignDate(datePair.getKey());
                sampleEsQuery.setEndSignDate(datePair.getValue());
                return elasticSearchSampleService.selectSamples(sampleEsQuery);
            }, esByDateExecutor.getPool());
        }).collect(Collectors.toList()).toArray(new CompletableFuture[0]);

        try {
            CompletableFuture.allOf(completableFutures).get();
        } catch (Exception e) {
            log.error("调用查询异常", e);
            return Collections.emptyList();
        }

        // 合并并行查出来的数据，过滤掉 null 和 重复的数据
        return Arrays.stream(completableFutures)
                .flatMap(e -> {
                    try {
                        return e.get().stream();
                    } catch (Exception ex) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .filter(StreamUtils.distinctByKey(BaseSampleEsModelDto::getApplySampleId))
                .collect(Collectors.toList());

    }

    /**
     * 不合格标本分布
     */
    @PostMapping("unqualified-statistics-export")
    public ResponseEntity<StreamingResponseBody> exportSampleUnqualifiedStatistics(@RequestBody SampleUnqualifiedStatisticsRequestVo vo) {
        SampleUnqualifiedStatisticsResultVo statisticsResultVo = selectSampleUnqualifiedStatistics(vo);
        List<SampleUnqualifiedStatisticsResultVo.ColumnItem> columns = statisticsResultVo.getColumns();
        String dateRange = DateUtils.format(vo.getStartDate(), "yyyy年MM月dd日") + " - " + DateUtils.format(vo.getEndDate(), "yyyy年MM月dd日");
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("不合格标本分布及分析.xlsx", StandardCharsets.UTF_8)))
                .header(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION, "filename")
                .header("filename", URLEncoder.encode("不合格标本分布及分析.xlsx", StandardCharsets.UTF_8))
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .contentType(new MediaType("application", "vnd.ms-excel"))
                .body(os -> {
                    List<List<Object>> dataList = data(columns, statisticsResultVo, dateRange);
                    EasyExcel.write(os)
                            .registerWriteHandler(new TitleMergeStrategy(columns.size()))
                            .sheet(0, "Sheet1")
                            .doWrite(dataList);
                });
    }

    /**
     * 异常样本统计查询
     */
    @PostMapping("/list")
    public Object abnormalSampleStatistics(@RequestBody SampleAbnormalStatisticsRequestDto dto){

        Long orgId = LoginUserHandler.get().getOrgId();
        final List<SampleAbnormalStatisticsDto> list = sampleAbnormalService.selectSampleAbnormalStatisticsBySendDate(dto.getBeginSendDate(), dto.getEndSendDate(), orgId);

        // 先查检验项目
        List<TestItemDto> testItemDtoList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(dto.getTestItemIds())){
            testItemDtoList.addAll(testItemService.selectByTestItemIds(dto.getTestItemIds()));
        }

        // 过滤数据
        Stream<SampleAbnormalStatisticsDto> stream = list.stream()
                .filter(e -> !Objects.equals(samplePropertyCode, e.getSamplePropertyCode()));

        if(CollectionUtils.isNotEmpty(dto.getHspOrgIds())){
            stream = stream.filter(e -> dto.getHspOrgIds().contains(e.getHspOrgId()));
        }

        if(CollectionUtils.isNotEmpty(dto.getGroupIds())){
            stream = stream.filter(e -> dto.getGroupIds().contains(e.getGroupId()));
        }

        if(CollectionUtils.isNotEmpty(dto.getSamplePropertyCodes())){
            stream = stream.filter(e -> dto.getSamplePropertyCodes().contains(e.getSamplePropertyCode()));
        }

        if(CollectionUtils.isNotEmpty(testItemDtoList)){
            stream = stream.filter(e -> {
                List<String> testItemNames = testItemDtoList.stream().map(TestItemDto::getTestItemName).collect(Collectors.toList());
                String[] abnormalTestItemNames = StringUtils.split(e.getTestItemName(), ",");
                for (String abnormalTestItemName : abnormalTestItemNames) {
                    if(testItemNames.contains(abnormalTestItemName)){
                        return true;
                    }
                }
                return false;
            });
        }
        return stream.collect(Collectors.groupingBy(SampleAbnormalStatisticsDto::getSampleProperty));
    }

    @PostMapping("/export/abnormal-statistics")
    public Object exportAbnormalStatistics(@RequestBody SampleAbnormalStatisticsRequestDto dto){
        Map<String, List<SampleAbnormalStatisticsDto>> map = (Map<String, List<SampleAbnormalStatisticsDto>>)this.abnormalSampleStatistics(dto);

        ExcelWriter excelWriter = null;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            // 头的策略
            WriteCellStyle headCellStyle = new WriteCellStyle();
            headCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());

            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontName("微软雅黑");
            headWriteFont.setFontHeightInPoints((short) 13);
            headCellStyle.setWriteFont(headWriteFont);

            // 内容策略
            WriteCellStyle contentCellStyle = new WriteCellStyle();
            contentCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            contentCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            contentCellStyle.setBorderLeft(BorderStyle.THIN);
            contentCellStyle.setBorderTop(BorderStyle.THIN);
            contentCellStyle.setBorderRight(BorderStyle.THIN);
            contentCellStyle.setBorderBottom(BorderStyle.THIN);

            HorizontalCellStyleStrategy excelStyle = new HorizontalCellStyleStrategy(headCellStyle, contentCellStyle);

            List<List<Object>> list0 = com.google.common.collect.Lists.newArrayList();
            List<List<String>> header0 = com.google.common.collect.Lists.newArrayList();

            // 设置表头
            List<String> headList = com.google.common.collect.Lists.newArrayList("序号", "送检机构", "条码号", "姓名", "性别", "年龄",
                   "就诊类型", "专业组", "检验项目", "样本类型", "送检日期",  "门诊/住院号", "送检医生", "科室", "床号");
            for (String item : headList) {
                header0.add(List.of(item));
            }

            excelWriter = EasyExcelFactory.write(out).excelType(ExcelTypeEnum.XLSX).registerWriteHandler(excelStyle)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();

            int i = 0;
            for (Map.Entry<String, List<SampleAbnormalStatisticsDto>> entry : map.entrySet()) {
                list0 = com.google.common.collect.Lists.newArrayList();

                for (int j = 0; j < entry.getValue().size(); j++) {
                    SampleAbnormalStatisticsDto statisticsDto = entry.getValue().get(j);
                    List<Object> content = Lists.newArrayListWithCapacity(headList.size());
                    content.add(j+1);
                    content.add(statisticsDto.getHspOrgName());
                    content.add(statisticsDto.getBarcode());
                    content.add(statisticsDto.getPatientName());
                    content.add(SexEnum.getByCode(statisticsDto.getPatientSex()).getDesc());
                    content.add(statisticsDto.getPatientAge());
                    content.add(statisticsDto.getApplyTypeName());
                    content.add(statisticsDto.getGroupName());
                    content.add(statisticsDto.getTestItemName());
                    content.add(statisticsDto.getSampleTypeName());
                    content.add(statisticsDto.getSendDate());
                    content.add(statisticsDto.getPatientVisitCard());
                    content.add(statisticsDto.getSendDoctorName());
                    content.add(statisticsDto.getDept());
                    content.add(statisticsDto.getPatientBed());
                    list0.add(content);
                }

                // 获取sheet对象
                WriteSheet sheet = EasyExcelFactory.writerSheet(i++, entry.getKey()).head(header0).needHead(Boolean.TRUE).build();

                excelWriter.write(list0, sheet);
            }

        } catch (Exception e) {
            log.error("导出数据错误", e);
            throw new LimsException(e.getMessage(), e);
        } finally {
            if (Objects.nonNull(excelWriter)) {
                excelWriter.finish();
            }
            try {
                out.close();
            } catch (IOException e) {
                log.error("关闭 流错误", e);
            }
        }
        byte[] data = out.toByteArray();

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("异常查询统计.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body(data);

    }

    public static class TitleMergeStrategy extends AbstractMergeStrategy {

        private final Integer fixedColumnCount = fixedHeaders.size();
        private final Integer totalColumnCount;

        private Set<Integer> mergedRows = new HashSet<>();

        public TitleMergeStrategy(Integer dynamicColumnCount) {
            this.totalColumnCount = fixedColumnCount + dynamicColumnCount;
        }

        @Override
        protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
            // 当前行
            int curRowIndex = cell.getRowIndex();
            // 当前列
            int curColIndex = cell.getColumnIndex();

            if (curRowIndex == 0 && mergedRows.add(curRowIndex)) {
                int middle = totalColumnCount / 2;
                sheet.addMergedRegionUnsafe(new CellRangeAddress(curRowIndex, curRowIndex, 0, middle));
                sheet.addMergedRegionUnsafe(new CellRangeAddress(curRowIndex, curRowIndex, middle + 1, totalColumnCount - 1));

                return;
            }

            if (curRowIndex < 3 && mergedRows.add(curRowIndex)) {
                // 添加一个合并请求
                sheet.addMergedRegionUnsafe(new CellRangeAddress(curRowIndex, curRowIndex, 0, totalColumnCount - 1));
            }
        }

        public void setColumnWidth(CellWriteHandlerContext context) {
            Cell cell = context.getCell();
            Sheet sheet = cell.getSheet();
            sheet.setColumnWidth(0, 15 * 256);
            sheet.setColumnWidth(1, 25 * 256);
            sheet.setColumnWidth(2, 20 * 256);
            for (int i = 3; i < totalColumnCount; i++) {
                sheet.setColumnWidth(i, 15 * 256);
            }
            Row row = cell.getRow();
            if (cell.getRowIndex() < 3) {
                row.setHeightInPoints(30);
                if (cell.getRowIndex() == 2) {
                    row.setHeightInPoints(20);
                }
            } else {
                row.setHeightInPoints(15);
            }
        }

        @Override
        public void afterCellDispose(CellWriteHandlerContext context) {
            // 当前事件会在 数据设置到poi的cell里面才会回调
            // 判断不是头的情况 如果是fill 的情况 这里会==null 所以用not true
            if (BooleanUtils.isNotTrue(context.getHead())) {
                setColumnWidth(context);

                Cell cell = context.getCell();
                if (cell.getRowIndex() < 3) {
                    // 合并单元格
                    merge(context.getWriteSheetHolder().getSheet(), cell, context.getHeadData(), context.getRelativeRowIndex());

                    WriteCellStyle headerWriteCellStyle = new WriteCellStyle();
                    // 字体大小
                    WriteFont contentWriteFont = new WriteFont();
                    contentWriteFont.setFontHeightInPoints((short) 12);
                    contentWriteFont.setBold(true);

                    //边框
                    // headerWriteCellStyle.setBorderBottom(BorderStyle.THIN);
                    // headerWriteCellStyle.setBorderLeft(BorderStyle.THIN);
                    // headerWriteCellStyle.setBorderRight(BorderStyle.THIN);
                    // headerWriteCellStyle.setBorderTop(BorderStyle.THIN);
                    // 水平居中
                    headerWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
                    // 垂直居中
                    headerWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                    if (cell.getRowIndex() == 1) {
                        headerWriteCellStyle.setWriteFont(contentWriteFont);
                    }
                    if (cell.getRowIndex() == 2) {
                        headerWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
                    }
                    if (cell.getRowIndex() == 0 && cell.getColumnIndex() == 6) {
                        headerWriteCellStyle.setBorderLeft(BorderStyle.NONE);
                        // 字体大小
                        WriteFont noFont = new WriteFont();
                        noFont.setFontHeightInPoints((short) 9);
                        headerWriteCellStyle.setWriteFont(noFont);
                        headerWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.RIGHT);
                        headerWriteCellStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
                    }

                    // 设置自动换行，前提内容中需要加「\n」才有效
                    headerWriteCellStyle.setWrapped(true);

                    // 这里要把 WriteCellData的样式清空， 不然后面还有一个拦截器 FillStyleCellWriteHandler 默认会将 WriteCellStyle 设置到
                    // cell里面去 会导致自己设置的不一样
                    context.getFirstCellData().setWriteCellStyle(headerWriteCellStyle);

                    return;
                }

                // 这里要把 WriteCellData的样式清空， 不然后面还有一个拦截器 FillStyleCellWriteHandler 默认会将 WriteCellStyle 设置到
                WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
                // 水平居中
                contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
                // 垂直居中
                contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                // cell里面去 会导致自己设置的不一样
                context.getFirstCellData().setWriteCellStyle(contentWriteCellStyle);
            }

            if (context.getHead()) {
                return;
            }

            super.afterCellDispose(context);
        }
    }

    private List<List<Object>> data(List<SampleUnqualifiedStatisticsResultVo.ColumnItem> columns,
                                    SampleUnqualifiedStatisticsResultVo statisticsResultVo,
                                    String dateRange) {
        ArrayList<String> fields = Lists.newArrayList(fixedHeaderFields);
        columns.forEach(col -> fields.add(col.getCode()));

        AtomicInteger rowNo = new AtomicInteger(1);
        List<List<Object>> datas = statisticsResultVo.getItems().stream().map(e -> {
            return fields.stream().map(field -> {
                if (field.equals("no")) {
                    return rowNo.getAndIncrement();
                } else {
                    return e.get(field);
                }
            }).collect(Collectors.toList());
        }).collect(Collectors.toList());

        List<List<Object>> titleRows = new ArrayList<>(fixedHeaders.size() + columns.size());
        byte[] bytes = HttpDownloader.downloadBytes("https://obs.labway.cn/labway-lims/prod/2023/11/09/10/55/a89a38cbe907485184ff1e1457966154");
        ArrayList<Object> logoRow = Lists.newArrayList();
        logoRow.add(bytes);
        int totalColumnCount = fixedHeaderFields.size() + columns.size();
        for (int i = 1; i < totalColumnCount; i++) {
            if (i < (totalColumnCount / 2) + 1) {
                logoRow.add("");
            } else {
                logoRow.add("编号：LW-WHCL-PA-SOP-G-009-01  版号/修号：4/2");
                break;
            }
        }

        titleRows.add(logoRow);
        titleRows.add(Lists.newArrayList("不合格标本分布及分析"));
        titleRows.add(Lists.newArrayList(String.format("    %s", dateRange)));
        List<Object> headers = new ArrayList<>(fixedHeaders);
        columns.forEach(column -> {
            headers.add(column.getTitle());
        });
        titleRows.add(headers);

        titleRows.addAll(datas);

        return titleRows;
    }

    private String reasonCodeConvert(String reasonCode) {
        return "Z" + reasonCode;
    }

}
