package com.labway.lims.statistics.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdcardUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Lists;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.config.EsConfig;
import com.labway.lims.api.enums.PdfTemplateTypeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.PrintStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ExcelMergeRowsDto;
import com.labway.lims.apply.api.dto.PathologyReportItemResponseDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SelectPathologyReportListRequestDto;
import com.labway.lims.apply.api.dto.business.MergeReportDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.BloodCultureInspectionDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import com.labway.lims.apply.api.dto.es.SelectReportListDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.PathologyReportService;
import com.labway.lims.apply.api.vo.MergeReportVo;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.ReportTemplateBindService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.api.client.ReportService;
import com.labway.lims.statistics.enums.BingliReportStatus;
import com.labway.lims.statistics.excel.ExportReportPatientInfoMergeStrategy;
import com.labway.lims.statistics.vo.ExportPatientInfoVo;
import com.labway.lims.statistics.vo.MergePdfsPreviewRequestVo;
import com.labway.lims.statistics.vo.ReportItemResponseVo;
import com.labway.lims.statistics.vo.SelectPathologyReportListRequestVo;
import com.labway.lims.statistics.vo.SelectPathologyReportListResponseVo;
import com.labway.lims.statistics.vo.SelectPathologyReportResponseVo;
import com.labway.lims.statistics.vo.SelectReportListRequestVo;
import com.labway.lims.statistics.vo.SelectReportListResponseVo;
import com.labway.lims.statistics.vo.SelectReportResponseVo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocumentInformation;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 报告单 API
 *
 * <AUTHOR>
 * @since 2023/4/13 10:36
 */
@Slf4j
@RestController
@RequestMapping("/report")
public class ReportController extends BaseController {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;

    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private PdfReportService pdfReportService;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private ReportTemplateBindService reportTemplateBindService;
    @Resource
    private EsConfig esConfig;
    @DubboReference
    private SystemParamService systemParamService;

    @DubboReference
    private PathologyReportService pathologyReportService;
    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ReportService reportService;

    /**
     * 报告单 列表
     */
    @PostMapping("/list")
    public Object reportList(@RequestBody SelectReportListRequestVo vo) {
        // 构造查询参数
        SampleEsQuery dto = new SampleEsQuery();
        dto.setPageNo(NumberUtils.INTEGER_ONE);
        dto.setPageSize(esConfig.getPageSize());
        // 查询审核的
        dto.setIsAudit(YesOrNoEnum.YES.getCode());
        boolean hasDate = false;
        // 签收日期
        if (Objects.nonNull(vo.getBeginSignDate()) && Objects.nonNull(vo.getEndSignDate())) {
            dto.setStartCreateDate(vo.getBeginSignDate());
            dto.setEndCreateDate(vo.getEndSignDate());
            hasDate = true;
        }

        // 审核日期开始
        if (Objects.nonNull(vo.getBeginCheckDate()) && Objects.nonNull(vo.getEndCheckDate())) {
            dto.setStartFinalCheckDate(vo.getBeginCheckDate());
            dto.setEndFinalCheckDate(vo.getEndCheckDate());
            hasDate = true;
        }

        // 判断如果没有选择日期范围进行提示
        if (!hasDate) {
            throw new IllegalArgumentException("请选择日期范围");
        }

        // 送检机构ID
        if (Objects.nonNull(vo.getHspOrgId())) {
            dto.setHspOrgIds(Set.of(vo.getHspOrgId()));
        }

        // 体检单位ID
        if (Objects.nonNull(vo.getPhysicalGroupId())) {
            dto.setPhysicalGroupIds(Set.of(vo.getPhysicalGroupId()));
        }

        // 科室
        if (StringUtils.isNotBlank(vo.getDept())) {
            dto.setDept(vo.getDept());
        }

        // 专业组
        if (Objects.nonNull(vo.getGroupId())) {
            dto.setGroupIds(Set.of(vo.getGroupId()));
        }

        // 检验项目
        if (Objects.nonNull(vo.getTestItemId())) {
            dto.setTestItemIds(Collections.singleton(vo.getTestItemId()));
        }
        // 检验项目(批量)
        if (CollectionUtils.isNotEmpty(vo.getTestItemIds())) {
            dto.setTestItemIds(vo.getTestItemIds());
        }

        if (StringUtils.isNotBlank(vo.getBarcode())) {
            dto.setBarcodes(Collections.singleton(vo.getBarcode()));
        }

        // 姓名
        if (StringUtils.isNotBlank(vo.getPatientName())) {
            dto.setPatientName(vo.getPatientName());
        }

        // 性别
        if (Objects.nonNull(vo.getPatientSex()) && !Objects.equals(vo.getPatientSex(), SexEnum.DEFAULT.getCode())) {
            dto.setPatientSex(vo.getPatientSex());
        }

        // 就诊类型
        if (CollectionUtils.isNotEmpty(vo.getApplyTypes())) {
            dto.setApplyTypes(vo.getApplyTypes());
        }
        // 门诊/住院号
        if (StringUtils.isNotBlank(vo.getPatientVisitCard())) {
            dto.setPatientVisitCard(vo.getPatientVisitCard());
        }
        // 是否打印:1已打印，0未打印
        if (Objects.nonNull(vo.getIsPrint())) {
            dto.setIsPrint(vo.getIsPrint());
        }

        // 前段传过来需要排序的字段
        List<SelectReportListRequestVo.SortData> sortData = vo.getSortData();

        return getSelectReportResponseVo(getSelectReportListResponseVos(dto, sortData));
    }

    private SelectReportResponseVo getSelectReportResponseVo(List<SelectReportListResponseVo> list) {
        final SelectReportResponseVo v = new SelectReportResponseVo();
        final Set<Long> applyIds = new HashSet<>();
        final Set<String> peopleCountSet = new HashSet<>();

        final Set<Long> applySampleIds = new HashSet<>();
        v.setHspOrgReports(list);

        for (SelectReportListResponseVo e : list) {
            for (ReportItemResponseVo k : e.getReportItemList()) {
                String onePeople;
                //判断同一人逻辑1、身份证号相同；2、同一机构且同一姓名且同一性别
                if (IdcardUtil.isValidCard(k.getPatientCard())) {
                    onePeople = k.getPatientCard();
                } else {
                    onePeople = k.getHspOrgId() + k.getPatientName() + k.getPatientSex();
                }
                peopleCountSet.add(onePeople);
                applySampleIds.add(k.getApplySampleId());
            }
        }

        v.setPeopleCount(peopleCountSet.size());
        v.setBarcodeCount(applySampleIds.size());
        return v;
    }

    /**
     * 报告单 患者信息导出
     */
    @PostMapping("/export-patient-info")
    public Object exportPatientInfo(@RequestBody ExportPatientInfoVo param) {
        param.check();

        // 构造查询参数
        SampleEsQuery dto = SampleEsQuery.builder()
                .pageSize(Integer.MAX_VALUE).pageNo(NumberUtils.INTEGER_ONE)
                .applySampleIds(param.getApplySampleIds())
                .applyIds(param.getApplyIds())
                .isAudit(YesOrNoEnum.YES.getCode()).build();

        // 展示数据
        List<SelectReportListResponseVo> targetList = getSelectReportListResponseVos(dto, param.getSortData());

        ExcelWriter excelWriter = null;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            // 头的策略
            WriteCellStyle headCellStyle = new WriteCellStyle();
            headCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());

            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontName("微软雅黑");
            headWriteFont.setFontHeightInPoints((short) 11);
            headCellStyle.setWriteFont(headWriteFont);

            // 内容策略
            WriteCellStyle contentCellStyle = new WriteCellStyle();
            contentCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            HorizontalCellStyleStrategy excelStyle = new HorizontalCellStyleStrategy(headCellStyle, contentCellStyle);

            List<List<Object>> list0 = Lists.newArrayList();
            List<List<String>> header0 = Lists.newArrayList();

            // 设置表头
            List<String> headList = Lists.newArrayList("序号", "姓名", "性别", "年龄", "就诊类型", "科室",
                    "门诊/住院号", "床号", "临床诊断", "结果备注", "打印状态", "条码号", "外部条码号", "录入/签收时间", "采样时间", "检验时间",
                    "报告时间", "专业组", "一审人", "二审人", "原始机构", "检验项目", "报告项目");
            for (String item : headList) {
                header0.add(List.of(item));
            }

            // 设置 合并规则 放置数据
            List<CellRangeAddress> list = new ArrayList<>();
            setMergeRuleAndFillExcelContent(list, list0, headList, targetList);

            excelWriter = EasyExcelFactory.write(out).excelType(ExcelTypeEnum.XLSX).registerWriteHandler(excelStyle)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy() {
                        @Override
                        protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
                            if (cell.getColumnIndex() == 0 || cell.getColumnIndex() == 1) {
                                writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), 10 * 512);
                            } else if (cell.getColumnIndex() == 1) {
                                writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), 10 * 512);
                            } else {
                                super.setColumnWidth(writeSheetHolder, cellDataList, cell, head, relativeRowIndex, isHead);
                            }
                        }
                    })
                    .registerWriteHandler(new ExportReportPatientInfoMergeStrategy(list)).build();

            // 获取sheet对象
            WriteSheet sheet0 = EasyExcelFactory.writerSheet(0, "患者信息").head(header0).needHead(Boolean.TRUE).build();

            excelWriter.write(list0, sheet0);
        } catch (Exception e) {
            log.error("导出数据错误", e);
            throw new LimsException(e.getMessage(), e);
        } finally {
            if (Objects.nonNull(excelWriter)) {
                excelWriter.finish();
            }
            try {
                out.close();
            } catch (IOException e) {
                log.error("关闭 流错误", e);
            }
        }
        byte[] data = out.toByteArray();

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("报告患者信息.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body(data);
    }

    /**
     * 报告单 修改打印状态
     */
    @PostMapping("/update-print-status")
    public Object updatePrintStatus(@RequestBody Set<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            return Collections.emptyMap();
        }
        final List<BaseSampleEsModelDto> esIdBySampleEsQueryDtoList =
                elasticSearchSampleService.selectSamples(SampleEsQuery.builder().applySampleIds(applySampleIds).build());
        final List<Long> esApplySampleIds = esIdBySampleEsQueryDtoList.stream()
                .map(BaseSampleEsModelDto::getApplySampleId).collect(Collectors.toList());
        if (applySampleIds.stream().anyMatch(item -> !esApplySampleIds.contains(item))) {
            throw new LimsException("存在无效报告单");
        }
        LoginUserHandler.User user = LoginUserHandler.get();
        // 对应所有申请单样本
        final List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplySampleIds(applySampleIds);

        // 对应所有未打印的申请单样本id
        final List<Long> unprintedApplySampleIds =
                applySampleDtos.stream().filter(obj -> !Objects.equals(obj.getIsPrint(), YesOrNoEnum.YES.getCode()))
                        .map(ApplySampleDto::getApplySampleId).collect(Collectors.toList());
        Date date = new Date();
        if (CollectionUtils.isNotEmpty(unprintedApplySampleIds)) {
            // 更新申请单样本 打印状态
            ApplySampleDto update = new ApplySampleDto();
            update.setIsPrint(YesOrNoEnum.YES.getCode());
            update.setPrintDate(date);
            update.setPrinterId(user.getUserId());
            update.setPrinterName(user.getNickname());
            applySampleService.updateByApplySampleIds(update, unprintedApplySampleIds);
        }

        return Collections.emptyMap();
    }

    /**
     * 报告单 合并打印
     */
    @PostMapping("/merge-print")
    public Object mergePrint(@RequestBody Set<Long> applySampleIds) {

        // 合并逻辑提到了service层 这里配置默认从service层处理合并
        return Map.of("url", reportService.mergePrint(applySampleIds));
    }

    /**
     * 报告单 到处 pdf
     */
    @PostMapping("/export-pdf")
    public Object exportPdf(@RequestBody Set<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            throw new IllegalArgumentException("请选择文件名称");
        }
        SampleEsQuery dto = new SampleEsQuery();
        dto.setPageNo(NumberUtils.INTEGER_ONE);
        dto.setPageSize(Integer.MAX_VALUE);
        // 查询审核的
        dto.setIsAudit(YesOrNoEnum.YES.getCode());
        dto.setApplySampleIds(applySampleIds);
        // 查询结果
        List<BaseSampleEsModelDto> esModelDtos = elasticSearchSampleService.selectSamples(dto);

        // 创建一个字节数组输出流，用于构建ZIP文件
        Integer count = 0;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        Map<String, Integer> nameCount = new HashMap<>();
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(byteArrayOutputStream)) {
            for (BaseSampleEsModelDto esModelDto : esModelDtos) {
                List<BaseSampleEsModelDto.Report> reports = esModelDto.getReports();
                if (CollectionUtils.isEmpty(reports)) {
                    continue;
                }
                String format = String.format("%s_%s", esModelDto.getBarcode(), esModelDto.getPatientName());
                for (int i = 0; i < reports.size(); i++) {

                    // 使用RestTemplate下载文件内容
                    RestTemplate restTemplate = new RestTemplate();
                    byte[] fileContent = null;
                    try {
                        fileContent = restTemplate.getForObject(reports.get(i).getUrl(), byte[].class);
                    } catch (Exception e) {
                        log.error("下载{}失败：{}", reports.get(i).getUrl(), e.getMessage());
                    }
                    if (Objects.isNull(fileContent)) {
                        continue;
                    }
                    String formatTemp = format;
                    if (nameCount.containsKey(formatTemp)) {
                        formatTemp = formatTemp + "_" + nameCount.get(formatTemp);
                    }

                    try {
                        log.info("开始下载文件{},url为：{}", formatTemp, reports.get(i).getUrl());
                        downloadAndAddToZip(zipOutputStream, formatTemp + ".pdf", fileContent);
                    } catch (IOException e) {
                        log.error("下载{}失败：{}", reports.get(i).getUrl(), e.getMessage());
                    }

                    nameCount.put(format, ObjectUtils.defaultIfNull(nameCount.get(formatTemp), 0) + 1);
                    count += 1;
                }
            }
            // 完成ZIP文件的构建
            zipOutputStream.finish();

            // 构建字节数组资源
            ByteArrayResource resource = new ByteArrayResource(byteArrayOutputStream.toByteArray());

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", System.currentTimeMillis() + "_" + count + ".zip");

            // 返回ResponseEntity
            return ResponseEntity.ok().headers(headers).contentLength(resource.contentLength()).body(resource);
        } catch (IOException e) {
            log.info("下载失败：{}", e.getMessage());
        }

        // 如果出现异常，返回空响应
        return ResponseEntity.noContent().build();
    }

    /**
     * 分块 下载
     */
    private void downloadAndAddToZip(ZipOutputStream zipOutputStream, String filename, byte[] fileContent)
            throws IOException {
        ZipEntry zipEntry = new ZipEntry(filename);
        zipOutputStream.putNextEntry(zipEntry);

        int chunkSize = 1024;
        int offset = 0;
        int length = Math.min(chunkSize, fileContent.length);

        while (offset < fileContent.length) {
            zipOutputStream.write(fileContent, offset, length);
            offset += length;
            length = Math.min(chunkSize, fileContent.length - offset);
        }

        zipOutputStream.closeEntry();
    }

    private static final String DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final String DATE_PATTERN_SIMPLE = "yyyy-MM-dd HH:mm";

    /**
     * 设置 合并规则 放置数据
     *
     * @param list           合并规则
     * @param list0          excel 数据
     * @param headList       表头
     * @param handleDataList 报告单列表
     */
    private void setMergeRuleAndFillExcelContent(List<CellRangeAddress> list, List<List<Object>> list0,
                                                 List<String> headList, List<SelectReportListResponseVo> handleDataList) {
        // 送检机构合并
        List<ExcelMergeRowsDto> hspOrgMergeRows = Lists.newArrayList();
        int endMergeColumn = 0;
        // 数据列合并
        List<ExcelMergeRowsDto> dataMergeRows = Lists.newArrayList();
        int startDataColumn = 0, endDataColumn = 20;
        // 检验项目列合并
        List<ExcelMergeRowsDto> testItemMergeRows = Lists.newArrayList();
        int startTestItemColumn = 21, endTestItemColumn = startTestItemColumn;

        int startRow = 1;
        for (SelectReportListResponseVo reportListResponseVo : handleDataList) {
            String hspOrgName = reportListResponseVo.getHspOrgName();
            List<ReportItemResponseVo> reportItemList = reportListResponseVo.getReportItemList();

            // 送检机构行
            {
                // 送检机构行合并
                ExcelMergeRowsDto mergeRowsDto = new ExcelMergeRowsDto();
                mergeRowsDto.setStartRow(startRow);
                mergeRowsDto.setEndRow(startRow);
                mergeRowsDto.setStartColumn(NumberUtils.INTEGER_ZERO);
                mergeRowsDto.setEndColumn(headList.size() - 1);
                hspOrgMergeRows.add(mergeRowsDto);

                // 添加送检机构行
                List<Object> content = Lists.newArrayListWithCapacity(headList.size());
                content.add(String.format("%s（共%s份）", hspOrgName, reportItemList.size()));
                list0.add(content);
                startRow += 1;
            }

            // 序号
            AtomicInteger rowNo = new AtomicInteger(NumberUtils.INTEGER_ONE);
            for (ReportItemResponseVo itemResponseVo : reportItemList) {
                // 检验项目
                List<BaseSampleEsModelDto.TestItem> testItems = itemResponseVo.getTestItems();
                // 报告项目
                List<RoutineInspectionDto.RoutineReportItem> reportItems = itemResponseVo.getReportItems();

                List<TestItemReportItem> testItemReportItems = Lists.newArrayList();
                // 检验项目列表
                if (CollectionUtils.isNotEmpty(testItems)) {
                    testItemReportItems = testItems.stream()
                            .map(e -> new TestItemReportItem(e.getTestItemCode(), e.getTestItemName(), StringPool.EMPTY))
                            .collect(Collectors.toList());
                }
                // 报告项目列表
                if (CollectionUtils.isNotEmpty(reportItems)) {
                    testItemReportItems = reportItems.stream()
                            .map(e -> new TestItemReportItem(e.getTestItemCode(), e.getTestItemName(), e.getReportItemName()))
                            .collect(Collectors.toList());
                }

                // 一个样本有多个检验项目，报告项目，合并样本信息
                int columnRowCnt = testItemReportItems.size();
                if (columnRowCnt > 1) {
                    ExcelMergeRowsDto mergeRowsDto = new ExcelMergeRowsDto();
                    mergeRowsDto.setStartRow(startRow);
                    mergeRowsDto.setEndRow(startRow + columnRowCnt - 1);
                    dataMergeRows.add(mergeRowsDto);
                }

                // 一个检验项目有多个报告项目，合并检验项目信息
                int testItemStartRow = startRow;
                LinkedHashMap<String, AtomicInteger> testItemMergeRowCnt = new LinkedHashMap<>();
                if (CollectionUtils.isNotEmpty(reportItems) && reportItems.size() > 1) {
                    reportItems.forEach(e -> {
                        testItemMergeRowCnt.computeIfAbsent(e.getTestItemCode(), k -> new AtomicInteger(0)).incrementAndGet();
                    });

                    for (Map.Entry<String, AtomicInteger> e : testItemMergeRowCnt.entrySet()) {
                        int testItemRowCnt = e.getValue().get();

                        if (testItemRowCnt > 1) {
                            ExcelMergeRowsDto mergeRowsDto = new ExcelMergeRowsDto();
                            mergeRowsDto.setStartRow(testItemStartRow);
                            mergeRowsDto.setEndRow(testItemStartRow + testItemRowCnt - 1);
                            testItemMergeRows.add(mergeRowsDto);
                        }
                        testItemStartRow += testItemRowCnt;
                    }
                }

                // 同一个样本可能会有多行，序号一样
                int row = rowNo.getAndIncrement();
                if (CollectionUtils.isNotEmpty(testItemReportItems)) {
                    for (TestItemReportItem testItemReportItem : testItemReportItems) {
                        List<Object> content = Lists.newArrayListWithCapacity(headList.size());
                        addRowToContent(content, row, itemResponseVo, testItemReportItem);
                        list0.add(content);
                        startRow += 1;
                    }
                } else {
                    List<Object> content = Lists.newArrayListWithCapacity(headList.size());
                    addRowToContent(content, row, itemResponseVo, null);
                    list0.add(content);
                    startRow += 1;
                }
            }
        }

        // 送检机构合并
        for (ExcelMergeRowsDto toMergeRow : hspOrgMergeRows) {
            if (toMergeRow.getEndColumn() > 0) {
                list.add(new CellRangeAddress(toMergeRow.getStartRow(), toMergeRow.getEndRow(), toMergeRow.getStartColumn(), toMergeRow.getEndColumn()));
            } else {
                for (int column = 0; column <= endMergeColumn; column++) {
                    list.add(new CellRangeAddress(toMergeRow.getStartRow(), toMergeRow.getEndRow(), column, column));
                }
            }
        }
        // 数据字段合并
        for (ExcelMergeRowsDto mergeRowsDto : dataMergeRows) {
            for (int column = startDataColumn; column <= endDataColumn; column++) {
                list.add(new CellRangeAddress(mergeRowsDto.getStartRow(), mergeRowsDto.getEndRow(), column, column));
            }
        }
        // 检验项目字段合并
        for (ExcelMergeRowsDto mergeRowsDto : testItemMergeRows) {
            for (int column = startTestItemColumn; column <= endTestItemColumn; column++) {
                list.add(new CellRangeAddress(mergeRowsDto.getStartRow(), mergeRowsDto.getEndRow(), column, column));
            }
        }
    }

    private void addRowToContent(
            List<Object> content, int row, ReportItemResponseVo itemResponseVo, TestItemReportItem testItemReportItem) {
        content.add(row);
        content.add(itemResponseVo.getPatientName());
        content.add(SexEnum.getByCode(itemResponseVo.getPatientSex()).getDesc());
        content.add(itemResponseVo.getPatientAge());
        content.add(itemResponseVo.getApplyType());
        content.add(itemResponseVo.getDept());
        content.add(itemResponseVo.getPatientVisitCard());
        content.add(itemResponseVo.getPatientBed());
        content.add(itemResponseVo.getDiagnosis());
        content.add(itemResponseVo.getResultRemark());
        content.add(PrintStatusEnum.getByCode(itemResponseVo.getIsPrint()).getDesc());
        content.add(itemResponseVo.getBarcode());
        content.add(itemResponseVo.getOutBarcode());
        content.add(DateFormatUtils.format(itemResponseVo.getCreateDate(), DATE_PATTERN));
        content.add(DateFormatUtils.format(itemResponseVo.getSamplingDate(), DATE_PATTERN));
        content.add(DateFormatUtils.format(itemResponseVo.getTestDate(), DATE_PATTERN));
        content.add(DateFormatUtils.format(itemResponseVo.getReportDate(), DATE_PATTERN));
        content.add(itemResponseVo.getGroupName());
        content.add(itemResponseVo.getOneCheckerName());
        content.add(itemResponseVo.getTwoCheckerName());
        content.add(itemResponseVo.getOriginalOrgName());
        if (Objects.nonNull(testItemReportItem)) {
            // 检验项目
            content.add(testItemReportItem.getTestItemName());
            // 报告项目
            content.add(testItemReportItem.getReportItemName());
        } else {
            // 检验项目
            content.add(StringPool.EMPTY);
            // 报告项目
            content.add(StringPool.EMPTY);
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    static class TestItemReportItem {
        private String testItemCode;
        private String testItemName;
        private String reportItemName;
    }

    @PostMapping("/pathology-list")
    public Object reportPathologyList(@RequestBody SelectPathologyReportListRequestVo vo) {
        return getSelectPathologyReportResponseVo(selectPathologyReportListResponseVos(vo));
    }

    /**
     * 病理报告单打印根据病理类型打印外送清单
     */
    @PostMapping("/print-pathology-list")
    public Object printPathologyList(@RequestBody SelectPathologyReportListRequestVo vo) {
        if (CollectionUtils.isEmpty(vo.getBarcodes())) {
            throw new IllegalArgumentException("请选择要打印的病理数据");
        }
        // 补充申请单类型
        vo.setApplyTypes(new HashSet<>());
        final List<SelectPathologyReportListResponseVo> pathologyReports = this.selectPathologyReportListResponseVos(vo);

        if (pathologyReports.stream()
                .flatMap(e -> e.getReportItemList().stream())
                .anyMatch(e -> !e.isReport())) {
            throw new IllegalStateException("存在未报告的数据，无法打印");
        }

        // 根据病理类型分组
        final Map<String, List<PathologyReportItemResponseDto>> byPathologyTypeMap = pathologyReports.stream()
                .flatMap(e -> e.getReportItemList().stream())
                .collect(Collectors.groupingBy(PathologyReportItemResponseDto::getPathologyType));

        final Set<byte[]> collect = byPathologyTypeMap.entrySet().stream().map(e -> {
            final String pathologyType = e.getKey();
            final List<PathologyReportItemResponseDto> list = e.getValue();

            PdfReportParamDto reportParam = new PdfReportParamDto();
            reportParam.put("pathologyType", pathologyType);
            reportParam.put("reportItemList", list);
            return pdfReportService.build(PdfTemplateTypeEnum.PATHOLOGY_REPORT_DETAIL.getCode(), reportParam);
        }).collect(Collectors.toSet());

        return Map.of("url", pdfReportService.mergePdf2Url(collect, NumberUtils.INTEGER_ONE));
    }

    @PostMapping("/merge-pdfs-preview")
    public Object mergePdfsPreview(@RequestBody MergePdfsPreviewRequestVo requestVo) throws Exception {
        List<File> files = null;
        try {
            final PDFMergerUtility merger = new PDFMergerUtility();
            List<String> pdfUrls = requestVo.getPdfUrls();
            if (CollectionUtils.isEmpty(pdfUrls)) {
                return Map.of("url", "");
            }

            if (pdfUrls.size() == 1) {
                return Map.of("url", pdfUrls.get(0));
            }

            String key = redisPrefix.getBasePrefix() + "LIMS:MERGE-PDFS:" + pdfUrls.stream().map(
                    e -> e.substring(e.lastIndexOf(StringPool.SLASH) + 1)).collect(Collectors.joining());
            if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(key))) {
                return Map.of("url", stringRedisTemplate.opsForValue().get(key));
            }

            files = pdfUrls.stream().map(pdfUrl -> {
                try {
                    File tempFile = FileUtil.createTempFile();
                    IOUtils.copy(new URL(pdfUrl), tempFile);
                    return tempFile;
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }).collect(Collectors.toList());

            files.forEach(file -> {
                try {
                    merger.addSource(file);
                } catch (FileNotFoundException e) {
                    throw new RuntimeException(e);
                }
            });

            final File pdf = FileUtil.createTempFile();

            final PDDocumentInformation information = new PDDocumentInformation();
            information.setKeywords("合并PDF");
            merger.setDestinationFileName(pdf.getAbsolutePath());
            merger.setDestinationDocumentInformation(information);
            merger.mergeDocuments(MemoryUsageSetting.setupTempFileOnly());

            try (final FileInputStream fis = new FileInputStream(pdf)) {
                String url = huaweiObsUtils.upload(fis, MediaType.APPLICATION_PDF_VALUE, 1);
                stringRedisTemplate.opsForValue().set(key, url, 1, TimeUnit.DAYS);
                return Map.of("url", url);
            } finally {
                FileUtils.deleteQuietly(pdf);
            }
        } catch (Exception e) {
            throw new IllegalStateException("合并PDF失败", e);
        } finally {
            if (CollectionUtils.isNotEmpty(files)) {
                files.forEach(FileUtils::deleteQuietly);
            }
        }
    }

    private SelectPathologyReportResponseVo getSelectPathologyReportResponseVo(List<SelectPathologyReportListResponseVo> list) {
        final SelectPathologyReportResponseVo v = new SelectPathologyReportResponseVo();
        final Set<String> peopleCountSet = new HashSet<>();

        final Set<Long> applySampleIds = new HashSet<>();
        v.setHspOrgReports(list);

        for (SelectPathologyReportListResponseVo e : list) {
            for (PathologyReportItemResponseDto k : e.getReportItemList()) {
                String onePeople;
                //判断同一人逻辑1、身份证号相同；2、同一机构且同一姓名且同一性别
                if (IdcardUtil.isValidCard(k.getPatientCard())) {
                    onePeople = k.getPatientCard();
                } else {
                    onePeople = k.getHspOrgCode() + k.getPatientName() + k.getPatientSex();
                }
                peopleCountSet.add(onePeople);
                applySampleIds.add(k.getApplySampleId());
            }
        }

        v.setPeopleCount(peopleCountSet.size());
        v.setBarcodeCount(applySampleIds.size());
        return v;
    }

    /**
     * 获取报告单展示数据
     *
     * @param dto 查询参数
     */
    private List<SelectPathologyReportListResponseVo> selectPathologyReportListResponseVos(SelectPathologyReportListRequestVo dto) {
        SelectPathologyReportListRequestDto requestDto = new SelectPathologyReportListRequestDto();
        BeanUtils.copyProperties(dto, requestDto);

        //先从业务中台把有结果的查出来
        List<PathologyReportItemResponseDto> reportListDtoList = pathologyReportService.selectPathologyReportList(requestDto);
        //再根据条件来判断是否需要获取本地数据
        BingliReportStatus reportStatus = BingliReportStatus.getByCode(dto.getReportStatus());
        //除了只查询已报告的 其他场景需要把本地的也查出来做筛选
        if (reportStatus != BingliReportStatus.REPORT) {
            //查询本地
            List<? extends BaseSampleEsModelDto> localList = selectLocalBingliList(dto);
            //本地都没有 就直接返回
            if (CollectionUtils.isEmpty(localList)) {
                return Collections.emptyList();
            }

            List<PathologyReportItemResponseDto> newList = new ArrayList<>();
            final List<PathologyReportItemResponseDto> finalReportListDtoList = reportListDtoList;
            localList.forEach(limsSample -> {
                AtomicBoolean match = new AtomicBoolean(false);
                finalReportListDtoList.forEach(bingliSample -> {
                    if (bingliSample.getBarcode().equals(limsSample.getBarcode())) {
                        //匹配到了
                        match.set(true);
                        //如果是全部 则加进去 否则就是需要非报告的，非报告的就不能加
                        if (reportStatus == BingliReportStatus.ALL) {
                            //加进去
                            newList.add(bingliSample);
                        }
                    }
                });
                if (!match.get()) {
                    // 加进去
                    PathologyReportItemResponseDto e = JSON.parseObject(JSON.toJSONString(limsSample), PathologyReportItemResponseDto.class);
                    // TODO 登记日期，先不设置，这里有歧义
                    // e.setCheckInDate(limsSample.getCreateDate());
                    newList.add(e);
                }
            });

            reportListDtoList = newList;
        }

        if (CollectionUtils.isEmpty(reportListDtoList)) {
            return List.of();
        }

        // 以送检机构分组
        final Map<String, List<PathologyReportItemResponseDto>> reportListDtoGroupByHspOrg =
                reportListDtoList.stream().collect(Collectors.groupingBy(PathologyReportItemResponseDto::getHspOrgCode));

        final AtomicLong soleId = new AtomicLong(System.currentTimeMillis());

        List<SelectPathologyReportListResponseVo> targetList = Lists.newArrayListWithCapacity(reportListDtoGroupByHspOrg.size());
        for (Map.Entry<String, List<PathologyReportItemResponseDto>> entry : reportListDtoGroupByHspOrg.entrySet()) {
            List<PathologyReportItemResponseDto> value = entry.getValue();

            List<PathologyReportItemResponseDto> reportItemList = Lists.newArrayListWithCapacity(value.size());

            value.forEach(item -> {
                PathologyReportItemResponseDto reportItem = JSON.parseObject(JSON.toJSONString(item), PathologyReportItemResponseDto.class);
                reportItem.setSoleId(soleId.incrementAndGet());
                reportItemList.add(reportItem);
            });

            // 默认 姓名 排序
            List<PathologyReportItemResponseDto> sortedByReportDate = reportItemList.stream().sorted(Comparator
                            .comparing(PathologyReportItemResponseDto::getPatientName, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(PathologyReportItemResponseDto::getReportDate, Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());

            List<SelectReportListRequestVo.SortData> sortData = dto.getSortData();
            // 叠加排序
            if (CollectionUtils.isNotEmpty(sortData)) {
                Comparator<PathologyReportItemResponseDto> comparator = (o1, o2) -> 0;
                for (SelectReportListRequestVo.SortData e : sortData) {
                    comparator = comparator.thenComparing((a, b) -> {
                        switch (e.getSortField()) {
                            case "pathologyNo":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getPathologyNo().compareTo(b.getPathologyNo());
                                }
                                return b.getPathologyNo().compareTo(a.getPathologyNo());
                            case "patientName":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getPatientName().compareTo(b.getPatientName());
                                }
                                return b.getPatientName().compareTo(a.getPatientName());
                            case "barcode":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getBarcode().compareTo(b.getBarcode());
                                }
                                return b.getBarcode().compareTo(a.getBarcode());
                            case "remark":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getRemark().compareTo(b.getRemark());
                                }
                                return b.getRemark().compareTo(a.getRemark());
                            case "originalOrgName":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getOriginalOrgName().compareTo(b.getOriginalOrgName());
                                }
                                return b.getOriginalOrgName().compareTo(a.getOriginalOrgName());
                            case "patientSexs":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getPatientSex().compareTo(b.getPatientSex());
                                }
                                return b.getPatientSex().compareTo(a.getPatientSex());
                            case "patientAge":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getPatientAge().compareTo(b.getPatientAge());
                                }
                                return b.getPatientAge().compareTo(a.getPatientAge());
                            case "applyType":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getApplyType().compareTo(b.getApplyType());
                                }
                                return b.getApplyType().compareTo(a.getApplyType());
                            case "pathologyType":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getPathologyType().compareTo(b.getPathologyType());
                                }
                                return b.getPathologyType().compareTo(a.getPathologyType());
                            case "dept":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getDept().compareTo(b.getDept());
                                }
                                return b.getDept().compareTo(a.getDept());
                            case "patientVisitCard":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getPatientVisitCard().compareTo(b.getPatientVisitCard());
                                }
                                return b.getPatientVisitCard().compareTo(a.getPatientVisitCard());
                            case "patientBed":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getPatientBed().compareTo(b.getPatientBed());
                                }
                                return b.getPatientBed().compareTo(a.getPatientBed());
                            case "diagnosis":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getDiagnosis().compareTo(b.getDiagnosis());
                                }
                                return b.getDiagnosis().compareTo(a.getDiagnosis());
                            case "resultRemark":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getResultRemark().compareTo(b.getResultRemark());
                                }
                                return b.getResultRemark().compareTo(a.getResultRemark());
                            case "isPrints":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getIsPrint().compareTo(b.getIsPrint());
                                }
                                return b.getIsPrint().compareTo(a.getIsPrint());
                            case "checkInDate":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getCheckInDate().compareTo(b.getCheckInDate());
                                }
                                return b.getCheckInDate().compareTo(a.getCheckInDate());
                            case "samplingDate":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getSamplingDate().compareTo(b.getSamplingDate());
                                }
                                return b.getSamplingDate().compareTo(a.getSamplingDate());
                            case "testDate":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getTestDate().compareTo(b.getTestDate());
                                }
                                return b.getTestDate().compareTo(a.getTestDate());
                            case "reportDate":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getReportDate().compareTo(b.getReportDate());
                                }
                                return b.getReportDate().compareTo(a.getReportDate());
                            case "groupName":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getGroupName().compareTo(b.getGroupName());
                                }
                                return b.getGroupName().compareTo(a.getGroupName());
                            case "oneCheckerName":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getOneCheckerName().compareTo(b.getOneCheckerName());
                                }
                                return b.getOneCheckerName().compareTo(a.getOneCheckerName());
                            case "twoCheckerName":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getTwoCheckerName().compareTo(b.getTwoCheckerName());
                                }
                                return b.getTwoCheckerName().compareTo(a.getTwoCheckerName());
                            case "reportDoctorName":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getReportDoctorName().compareTo(b.getReportDoctorName());
                                }
                                return b.getReportDoctorName().compareTo(a.getReportDoctorName());
                            case "reauditDoctorName":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getReauditDoctorName().compareTo(b.getReauditDoctorName());
                                }
                                return b.getReauditDoctorName().compareTo(a.getReauditDoctorName());
                            case "createDate":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getCreateDate().compareTo(b.getCreateDate());
                                }
                                return b.getCreateDate().compareTo(a.getCreateDate());
                            default:
                                return 0;
                        }
                    });
                }

                sortedByReportDate.sort(comparator);
            }

            SelectPathologyReportListResponseVo temp = new SelectPathologyReportListResponseVo();
            temp.setHspOrgCode(value.get(0).getHspOrgCode());
            temp.setHspOrgName(value.get(0).getHspOrgName());
            temp.setReportItemList(sortedByReportDate);
            temp.setSoleId(soleId.incrementAndGet());
            targetList.add(temp);
        }

        return targetList;
    }

    private List<? extends BaseSampleEsModelDto> selectLocalBingliList(SelectPathologyReportListRequestVo vo){
        // 构造查询参数
        SampleEsQuery dto = new SampleEsQuery();
        dto.setPageNo(NumberUtils.INTEGER_ONE);
        dto.setPageSize(esConfig.getPageSize());
        // 查询审核的
//        dto.setIsAudit(YesOrNoEnum.YES.getCode());
        boolean hasDate = false;
        // 签收日期
        if (Objects.nonNull(vo.getBeginCheckInDate()) && Objects.nonNull(vo.getEndCheckInDate())) {
            dto.setStartCreateDate(vo.getBeginCheckInDate());
            dto.setEndCreateDate(vo.getEndCheckInDate());
            hasDate = true;
        }

        // 审核日期开始
        if (Objects.nonNull(vo.getBeginCheckDate()) && Objects.nonNull(vo.getEndCheckDate())) {
            // dto.setStartFinalCheckDate(vo.getBeginCheckDate());
            // dto.setEndFinalCheckDate(vo.getEndCheckDate());
            // hasDate = true;
        }

        // 判断如果没有选择日期范围进行提示
        if (!hasDate && CollectionUtils.isEmpty(vo.getBarcodes()) && StringUtils.isBlank(vo.getBarcode())) {
            throw new IllegalArgumentException("请选择日期范围");
        }

        // 送检机构ID
        if (Objects.nonNull(vo.getHspOrgId())) {
            dto.setHspOrgIds(Set.of(vo.getHspOrgId()));
        }

        // 科室
        if (StringUtils.isNotBlank(vo.getDept())) {
            dto.setDept(vo.getDept());
        }

        //条码
        if (StringUtils.isNotBlank(vo.getBarcode())) {
            dto.setBarcodes(Collections.singleton(vo.getBarcode()));
        }

        // 条码号批量查询
        if (CollectionUtils.isNotEmpty(vo.getBarcodes())) {
            dto.setBarcodes(vo.getBarcodes());
        }

        // 姓名
        if (StringUtils.isNotBlank(vo.getPatientName())) {
            dto.setPatientName(vo.getPatientName());
        }

        // 性别
        if (Objects.nonNull(vo.getPatientSex()) && !Objects.equals(vo.getPatientSex(), SexEnum.DEFAULT.getCode())) {
            dto.setPatientSex(vo.getPatientSex());
        }

        // 就诊类型
        if (CollectionUtils.isNotEmpty(vo.getApplyTypes())) {
            dto.setApplyTypes(vo.getApplyTypes());
        }
        // 是否打印:1已打印，0未打印
        if (Objects.nonNull(vo.getIsPrint())) {
            dto.setIsPrint(vo.getIsPrint());
        }
        //病理样本类型
        dto.setItemTypes(Collections.singleton(ItemTypeEnum.PATHOLOGY.name()));

        // 查询结果
        final List<? extends BaseSampleEsModelDto> baseSampleEsModelDtos =
                elasticSearchSampleService.selectSamples(dto);

        return baseSampleEsModelDtos;
    }

    private List<SelectReportListResponseVo> getSelectReportListResponseVos(SampleEsQuery dto, List<SelectReportListRequestVo.SortData> sortData) {
        // 查询结果
        final List<? extends BaseSampleEsModelDto> baseSampleEsModelDtos =
                elasticSearchSampleService.selectSamples(dto);

        if (CollectionUtils.isEmpty(baseSampleEsModelDtos)) {
            return List.of();
        }

        // 血培养特殊处理
        baseSampleEsModelDtos.removeIf(e -> {
            if (!(e instanceof BloodCultureInspectionDto)) {
                return false;
            }

            final BloodCultureInspectionDto bloodCultureInspection = (BloodCultureInspectionDto) e;

            List<BaseSampleEsModelDto.Report> reports = new ArrayList<>(ObjectUtils.defaultIfNull(e.getReports(), List.of()));
            if (CollectionUtils.isEmpty(reports)) {
                return true;
            }

            reports = reports.stream().filter(k -> Objects.equals(k.getSampleReportId(), bloodCultureInspection.getTwoCheckSampleReportId()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(reports)) {
                e.setReports(reports);
            }

            return CollectionUtils.isEmpty(reports);
        });

        final List<SelectReportListDto> reportListDtoList = baseSampleEsModelDtos.stream()
                .map(obj -> JSON.parseObject(JSON.toJSONString(obj), SelectReportListDto.class))
                .collect(Collectors.toList());
        // 以送检机构分组
        final Map<Long, List<SelectReportListDto>> reportListDtoGroupByHspOrg =
                reportListDtoList.stream().collect(Collectors.groupingBy(SelectReportListDto::getHspOrgId));

        final AtomicLong soleId = new AtomicLong(System.currentTimeMillis());

        List<SelectReportListResponseVo> targetList = Lists.newArrayListWithCapacity(reportListDtoGroupByHspOrg.size());
        for (Map.Entry<Long, List<SelectReportListDto>> entry : reportListDtoGroupByHspOrg.entrySet()) {
            List<SelectReportListDto> value = entry.getValue();

            List<ReportItemResponseVo> reportItemList = Lists.newArrayListWithCapacity(value.size());

            value.forEach(item -> {
                ReportItemResponseVo reportItem = JSON.parseObject(JSON.toJSONString(item), ReportItemResponseVo.class);
                reportItem.setReportDate(item.getFinalCheckDate());
                reportItem.setApplyType(item.getApplyTypeName());
                reportItem.setTwoCheckerId(item.getFinalCheckerId());
                reportItem.setTwoCheckerName(item.getFinalCheckerName());
                reportItem.setSoleId(soleId.incrementAndGet());
                reportItemList.add(reportItem);
            });
            // 默认 姓名 排序
            List<ReportItemResponseVo> sortedByReportDate = reportItemList.stream().sorted(Comparator
                            .comparing(ReportItemResponseVo::getPatientName, Comparator.nullsLast(Comparator.naturalOrder()))
                            .thenComparing(ReportItemResponseVo::getReportDate, Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());

            // 叠加排序
            if (CollectionUtils.isNotEmpty(sortData)) {
                Comparator<ReportItemResponseVo> comparator = (o1, o2) -> 0;
                for (SelectReportListRequestVo.SortData e : sortData) {
                    comparator = comparator.thenComparing((a, b) -> {
                        switch (e.getSortField()) {
                            case "patientName":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getPatientName().compareTo(b.getPatientName());
                                }
                                return b.getPatientName().compareTo(a.getPatientName());
                            case "barcode":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getBarcode().compareTo(b.getBarcode());
                                }
                                return b.getBarcode().compareTo(a.getBarcode());
                            case "remark":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getRemark().compareTo(b.getRemark());
                                }
                                return b.getRemark().compareTo(a.getRemark());
                            case "originalOrgName":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getOriginalOrgName().compareTo(b.getOriginalOrgName());
                                }
                                return b.getOriginalOrgName().compareTo(a.getOriginalOrgName());
                            case "patientSexs":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getPatientSex().compareTo(b.getPatientSex());
                                }
                                return b.getPatientSex().compareTo(a.getPatientSex());
                            case "patientAge":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getPatientAge().compareTo(b.getPatientAge());
                                }
                                return b.getPatientAge().compareTo(a.getPatientAge());
                            case "applyType":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getApplyType().compareTo(b.getApplyType());
                                }
                                return b.getApplyType().compareTo(a.getApplyType());
                            case "dept":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getDept().compareTo(b.getDept());
                                }
                                return b.getDept().compareTo(a.getDept());
                            case "patientVisitCard":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getPatientVisitCard().compareTo(b.getPatientVisitCard());
                                }
                                return b.getPatientVisitCard().compareTo(a.getPatientVisitCard());
                            case "patientBed":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getPatientBed().compareTo(b.getPatientBed());
                                }
                                return b.getPatientBed().compareTo(a.getPatientBed());
                            case "diagnosis":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getDiagnosis().compareTo(b.getDiagnosis());
                                }
                                return b.getDiagnosis().compareTo(a.getDiagnosis());
                            case "resultRemark":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getResultRemark().compareTo(b.getResultRemark());
                                }
                                return b.getResultRemark().compareTo(a.getResultRemark());
                            case "isPrints":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getIsPrint().compareTo(b.getIsPrint());
                                }
                                return b.getIsPrint().compareTo(a.getIsPrint());
                            case "samplingDate":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getSamplingDate().compareTo(b.getSamplingDate());
                                }
                                return b.getSamplingDate().compareTo(a.getSamplingDate());
                            case "testDate":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getTestDate().compareTo(b.getTestDate());
                                }
                                return b.getTestDate().compareTo(a.getTestDate());
                            case "reportDate":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getReportDate().compareTo(b.getReportDate());
                                }
                                return b.getReportDate().compareTo(a.getReportDate());
                            case "groupName":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getGroupName().compareTo(b.getGroupName());
                                }
                                return b.getGroupName().compareTo(a.getGroupName());
                            case "oneCheckerName":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getOneCheckerName().compareTo(b.getOneCheckerName());
                                }
                                return b.getOneCheckerName().compareTo(a.getOneCheckerName());
                            case "twoCheckerName":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getTwoCheckerName().compareTo(b.getTwoCheckerName());
                                }
                                return b.getTwoCheckerName().compareTo(a.getTwoCheckerName());
                            case "createDate":
                                if (Objects.equals(e.getSortRule(), "Asc")) {
                                    return a.getCreateDate().compareTo(b.getCreateDate());
                                }
                                return b.getCreateDate().compareTo(a.getCreateDate());
                            default:
                                return 0;
                        }
                    });
                }

                sortedByReportDate.sort(comparator);
            }

            SelectReportListResponseVo temp = new SelectReportListResponseVo();
            temp.setHspOrgId(value.get(0).getHspOrgId());
            temp.setHspOrgName(value.get(0).getHspOrgName());
            temp.setReportItemList(sortedByReportDate);
            temp.setSoleId(soleId.incrementAndGet());
            targetList.add(temp);
        }

        return targetList;
    }

    /**
     * 报告单 合并打印
     */
    @PostMapping("/merge-print-business")
    public MergeReportVo mergePrintBusiness(@RequestBody MergeReportDto mergeReportDto) {
        log.info("业务中台请求合并打印报告单，请求参数：{}", JSONObject.toJSONString(mergeReportDto));

        // 查询申请单样本id
        List<ApplySampleDto> applySampleDtos = applySampleService.selectByBarcodes(mergeReportDto.getBarcodes());

        if (CollectionUtils.isEmpty(applySampleDtos)) {
            log.warn("未查询到样本信息，不进行报告单合并！");
            return new MergeReportVo(mergeReportDto.getOrgCode(), mergeReportDto.getBarcodes(), mergeReportDto.getReportUrls(), YesOrNoEnum.YES.getCode());
        }

        //判断送检机构是否配置了合并打印
        // 是否允许多PDF
        final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.MERGE_PRINT.getCode(), applySampleDtos.get(0).getOrgId());
        log.info("合并打印报告单配置：{}", JSONObject.toJSONString(param));

        if (param == null || StringUtils.isBlank(param.getParamValue()) || !Arrays.asList(param.getParamValue().split(",")).contains(applySampleDtos.get(0).getHspOrgCode())) {
            log.warn("送检机构未配置合并打印，不进行报告单合并！");
            return new MergeReportVo(mergeReportDto.getOrgCode(), mergeReportDto.getBarcodes(), mergeReportDto.getReportUrls(), YesOrNoEnum.YES.getCode());
        }

        // 报告单没出全，并且配置了合并报告单功能
        if (mergeReportDto.getIsMergerReport() != null && mergeReportDto.getIsMergerReport() == YesOrNoEnum.NO.getCode() && Arrays.asList(param.getParamValue().split(",")).contains(applySampleDtos.get(0).getHspOrgCode())) {
            log.info("报告单位出全并且实验室配置了合并报工单功能，报告结果回传表示为0！！");
            return new MergeReportVo(mergeReportDto.getOrgCode(), mergeReportDto.getBarcodes(), mergeReportDto.getReportUrls(), YesOrNoEnum.NO.getCode());
        }

        log.info("报告单合并校验通过，开始合并报告单。。。");
        // 申请单合并
        Map<String, List<String>> map = (Map) this.mergePrint(new HashSet<Long>(applySampleDtos.stream().map(e -> e.getApplySampleId()).collect(Collectors.toList())));

        log.info("报告单合并校验通过，合并报告单完成！！！");
        return new MergeReportVo(mergeReportDto.getOrgCode(), mergeReportDto.getBarcodes(), map.get("url"), YesOrNoEnum.YES.getCode());
    }

}
