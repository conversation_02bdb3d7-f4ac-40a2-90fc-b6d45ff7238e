package com.labway.lims.statistics.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.statistics.dto.ExportRecordDto;
import com.labway.lims.statistics.enums.ExportStatus;
import com.labway.lims.statistics.mapper.TbExportFileMapper;
import com.labway.lims.statistics.model.TbExportFile;
import com.labway.lims.statistics.service.ExportService;
import com.labway.lims.statistics.vo.ExportFileCancelRequestVo;
import com.labway.lims.statistics.vo.ExportFileDeleteRequestVo;
import com.labway.lims.statistics.vo.ExportFileDownloadRequestVo;
import com.labway.lims.statistics.vo.ExportFileRequestVo;
import com.labway.lims.statistics.vo.ExportFileResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <pre>
 * ExportServiceImpl
 * 描述信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/4/9 10:48
 */
@Slf4j
@Service
public class ExportServiceImpl implements ExportService, InitializingBean {

    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private TbExportFileMapper exportFileMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisPrefix redisPrefix;

    @Value("${file.download.period:10}")
    private Integer period;

    private static final LinkedList<Future<ExportRecordDto>> QUEUE = new LinkedList<>();

    private static final ScheduledExecutorService SINGLE_SCHEDULED  = Executors.newSingleThreadScheduledExecutor();

    @Override
    public Future<ExportRecordDto> submitExportTask(ExportRecordDto recordDto, Callable<ExportRecordDto> callable) {

        LoginUserHandler.User user = LoginUserHandler.get();

        recordDto.setExportFileId(snowflakeService.genId());
        recordDto.setIsDelete(YesOrNoEnum.NO.getCode());
        recordDto.setCreatorId(user.getUserId());
        recordDto.setCreatorName(user.getNickname());
        recordDto.setCreateDate(DateUtil.date());
        recordDto.setUpdaterId(user.getUserId());
        recordDto.setUpdaterName(user.getNickname());
        recordDto.setUpdateDate(DateUtil.date());
        recordDto.setOrgId(user.getOrgId());
        recordDto.setOrgName(user.getOrgName());

        int insert = exportFileMapper.insert(JSON.parseObject(JSON.toJSONString(recordDto), TbExportFile.class));

        log.info("记录导出文件：{}", insert);

        Future<ExportRecordDto> future = threadPoolConfig.getPool().submit(callable);
        QUEUE.add(future);
        return future;
    }

    @Override
    public ExportFileResultVo list(ExportFileRequestVo requestVo) {
        String source = requestVo.getSource();
        LoginUserHandler.User user = LoginUserHandler.get();

        LambdaQueryWrapper<TbExportFile> wrapper = Wrappers.lambdaQuery(TbExportFile.class)
                .orderByDesc(TbExportFile::getCreateDate)
                .eq(TbExportFile::getIsDelete, YesOrNoEnum.NO.getCode())
                .eq(TbExportFile::getSource, source)
                .eq(TbExportFile::getOrgId, user.getOrgId())
                .notIn(TbExportFile::getStatus, ExportStatus.CANCEL.getCode(), ExportStatus.UNKNOW.getCode());

        List<TbExportFile> files = exportFileMapper.selectList(wrapper);

        log.info("查询导出文件：{}", JSON.toJSONString(files));

        return new ExportFileResultVo() {{
            setItems(files.stream().map(e -> JSON.parseObject(JSON.toJSONString(e), ExportFileResultVo.ExportFileItem.class)).collect(Collectors.toList()));
        }};
    }

    @Override
    public void download(ExportFileDownloadRequestVo requestVo) {
        Assert.notNull(requestVo.getExportFileId(), "文件ID不能为空");

        log.info("用户下载文件：{}", requestVo.getExportFileId());

        exportFileMapper.deleteById(requestVo.getExportFileId());
    }

    @Override
    public void cancel(ExportFileCancelRequestVo requestVo) {
        Assert.notNull(requestVo.getExportFileId(), "文件ID不能为空");

        Long exportFileId = requestVo.getExportFileId();
        LambdaUpdateWrapper<TbExportFile> wrapper = Wrappers.lambdaUpdate(TbExportFile.class)
                .set(TbExportFile::getStatus, ExportStatus.CANCEL.getCode())
                .eq(TbExportFile::getExportFileId, exportFileId)
                .in(TbExportFile::getStatus, ExportStatus.RUNNING.getCode(), ExportStatus.SUCCESS.getCode(), ExportStatus.FAIL.getCode());

        exportFileMapper.update(null, wrapper);

        log.info("用户取消文件 {} 下载", requestVo.getExportFileId());

    }

    @Override
    public void delete(ExportFileDeleteRequestVo requestVo) {
        Assert.notNull(requestVo.getExportFileId(), "文件ID不能为空");

        log.info("用户删除文件 {}", requestVo.getExportFileId());

        exportFileMapper.deleteById(requestVo.getExportFileId());
    }

    @Override
    public void afterPropertiesSet() throws Exception {

        SINGLE_SCHEDULED.scheduleAtFixedRate(() -> {
            Iterator<Future<ExportRecordDto>> iterator = QUEUE.iterator();

            while (iterator.hasNext()) {
                Future<ExportRecordDto> future = iterator.next();
                if (future.isDone()) {
                    try {
                        ExportRecordDto recordDto = future.get();
                        log.info("下载任务结束：{} 耗时 【{} s】", JSON.toJSONString(recordDto), DateUtil.between(DateUtil.date(), recordDto.getCreateDate(), DateUnit.SECOND));

                        Long exportFileId = recordDto.getExportFileId();
                        LambdaUpdateWrapper<TbExportFile> wrapper = Wrappers.lambdaUpdate(TbExportFile.class)
                                .set(TbExportFile::getUrl, recordDto.getUrl())
                                .set(TbExportFile::getFileType, recordDto.getFileType())
                                .set(TbExportFile::getFileName, recordDto.getFileName())
                                .set(TbExportFile::getStatus, Objects.isNull(recordDto.getEx()) ? ExportStatus.SUCCESS.getCode() : ExportStatus.FAIL.getCode())
                                .set(TbExportFile::getUpdateDate, DateUtil.date())
                                .eq(TbExportFile::getExportFileId, exportFileId)
                                .eq(TbExportFile::getStatus, ExportStatus.RUNNING.getCode());

                        int update = exportFileMapper.update(null, wrapper);
                        log.info("下载任务更新：{}", update);
                    } catch (Exception e) {
                        log.error("下载任务处理异常：{}", e.getMessage(), e);
                    }

                    iterator.remove();
                }
            }

        }, 1, period, TimeUnit.SECONDS);

    }

}
