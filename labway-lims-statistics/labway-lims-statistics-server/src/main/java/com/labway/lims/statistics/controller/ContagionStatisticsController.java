package com.labway.lims.statistics.controller;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.PatientAges;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.StopStatusEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.enums.routine.TestJudgeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.dto.SystemParamDto;
import com.labway.lims.base.api.service.ReportItemService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.dto.QueryContagionReportItemInfoDto;
import com.labway.lims.statistics.dto.QueryContagionSampleInfoDto;
import com.labway.lims.statistics.vo.QueryContagionSampleInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * ContagionStatisticsController
 * 传染病统计
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/14 10:30
 */
@Slf4j
@RestController
@RequestMapping("/contagion-statistics")
public class ContagionStatisticsController extends BaseController {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;
    @DubboReference
    private SystemParamService systemParamService;
    @DubboReference
    private ReportItemService reportItemService;

    /**
     * 查询配置的传染病报告项目信息
     */
    @GetMapping("/queryContagionReportItemInfo")
    public List<QueryContagionReportItemInfoDto> queryContagionReportItemInfo() {

        LoginUserHandler.User user = LoginUserHandler.get();

        // 查询传染病报告项目配置
        final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.INFECTIOUS_DISEASES_REPORT_CODES.getCode(), user.getOrgId());
        if (param == null || StringUtils.isBlank(param.getParamValue())) {
            throw new LimsException("暂未配置传染病报告项目信息!");
        }

        List<String> reportItemCodelist = Arrays.asList(param.getParamValue().split(","));

        // 查询报告项目信息
        List<ReportItemDto> reportItemDtos = reportItemService.selectByReportItemCodes(reportItemCodelist, user.getOrgId());
        if (CollectionUtils.isEmpty(reportItemDtos)) {
            return Collections.emptyList();
        }

        Map<String, ReportItemDto> reportItemDtoMap = reportItemDtos
                .stream().collect(Collectors.toMap(ReportItemDto::getReportItemCode, Function.identity(), (a, b) -> a));

        return reportItemDtoMap.values().stream().map(dto -> {
            QueryContagionReportItemInfoDto queryContagionReportItemInfoDto = new QueryContagionReportItemInfoDto();
            queryContagionReportItemInfoDto.setReportItemCode(dto.getReportItemCode());
            queryContagionReportItemInfoDto.setReportItemId(dto.getReportItemId());
            queryContagionReportItemInfoDto.setReportItemName(dto.getReportItemName());
            return queryContagionReportItemInfoDto;
        }).collect(Collectors.toList());

    }


    /**
     * 查询传染病样本信息
     */
    @RequestMapping("/queryContagionSampleInfo")
    public List<QueryContagionSampleInfoDto> queryContagionSampleInfo(@RequestBody QueryContagionSampleInfoVo queryContagionSampleInfoVo) {

        LoginUserHandler.User user = LoginUserHandler.get();

        // 查询微生物项目样本性状配置
        if (CollectionUtils.isEmpty(queryContagionSampleInfoVo.getReportItemCodes())) {
            final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.INFECTIOUS_DISEASES_REPORT_CODES.getCode(), user.getOrgId());
            if (param == null || StringUtils.isBlank(param.getParamValue())) {
                throw new LimsException("未配置传染病报告项目信息!");
            }
            queryContagionSampleInfoVo.setReportItemCodes(Arrays.asList(param.getParamValue().split(",")));
        }

        SampleEsQuery sampleEsQuery = fillQuery(queryContagionSampleInfoVo);

        List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(sampleEsQuery);

        if (CollectionUtils.isEmpty(baseSampleEsModelDtos)) {
            return Collections.emptyList();
        }

        // 筛选符合传染病的样本(只统计常规检验)
        List<RoutineInspectionDto> routineInspectionDtoList = baseSampleEsModelDtos.stream().filter(RoutineInspectionDto.class::isInstance).map(dto -> (RoutineInspectionDto) dto).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(routineInspectionDtoList)) {
            return Collections.emptyList();
        }

        // 结果值是UP或者↑的样本
        List<RoutineInspectionDto> queryContagionSampleInfoDtos = routineInspectionDtoList.stream().filter(dto -> {
            return dto.getReportItems().stream().anyMatch(e -> StringUtils.equals(e.getJudge(), TestJudgeEnum.UP.getValue()));
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(queryContagionSampleInfoDtos)) {
            return Collections.emptyList();
        }

        // 结果转换
        return convertResultDto(queryContagionSampleInfoDtos, queryContagionSampleInfoVo);
    }


    //==================================================================================================================


    // 填充查询条件
    private SampleEsQuery fillQuery(QueryContagionSampleInfoVo queryContagionSampleInfoVo) {

        SampleEsQuery sampleEsQuery = new SampleEsQuery();

        sampleEsQuery.setStartSignDate(queryContagionSampleInfoVo.getReceiveDateStart());
        sampleEsQuery.setEndSignDate(queryContagionSampleInfoVo.getReceiveDateEnd());
        sampleEsQuery.setReportItemCodes(new HashSet<>(queryContagionSampleInfoVo.getReportItemCodes()));

        // 送检机构
        if (CollectionUtils.isNotEmpty(queryContagionSampleInfoVo.getSendOrgCodes())) {
            sampleEsQuery.setHspOrgCodes(new HashSet<>(queryContagionSampleInfoVo.getSendOrgCodes()));
        }

        // 病人姓名
        if (StringUtils.isNotBlank(queryContagionSampleInfoVo.getPatientName())) {
            sampleEsQuery.setPatientName(queryContagionSampleInfoVo.getPatientName());
        }

        // 条码号
        if (StringUtils.isNotBlank(queryContagionSampleInfoVo.getBarcode())) {
            sampleEsQuery.setBarcodeOrOutbarcodes(Collections.singleton(queryContagionSampleInfoVo.getBarcode()));
        }

        // 排除终止检验的
        sampleEsQuery.setItemStatus(StopStatusEnum.NO_STOP_TEST.getCode());


        return sampleEsQuery;
    }


    // 转换结果实体
    // 展示报告项目对应的检验项目 同时该检验项目
    private List<QueryContagionSampleInfoDto> convertResultDto(List<RoutineInspectionDto> queryContagionSampleInfoDtos, QueryContagionSampleInfoVo queryContagionSampleInfoVo) {

        List<QueryContagionSampleInfoDto> queryContagionSampleInfoDtoList = new ArrayList<>();

        List<String> reportItemCodes = queryContagionSampleInfoVo.getReportItemCodes();
        for (RoutineInspectionDto tempContagionSampleInfoDto : queryContagionSampleInfoDtos) {

            // 这里过滤出配置的报告项目编码 并且检验结果是UP或者⬆️的项目
            List<RoutineInspectionDto.RoutineReportItem> tempReportItems = tempContagionSampleInfoDto.getReportItems().stream()
                    .filter(reportItem -> reportItemCodes.contains(reportItem.getReportItemCode())
                            && StringUtils.isNotBlank(reportItem.getJudge())
                            && Objects.equals(reportItem.getJudge(), TestJudgeEnum.UP.getValue()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(tempReportItems)) {
                continue;
            }

            QueryContagionSampleInfoDto temp = new QueryContagionSampleInfoDto();
            temp.setHspOrgId(tempContagionSampleInfoDto.getHspOrgId());
            temp.setHspOrgName(tempContagionSampleInfoDto.getHspOrgName());
            temp.setHspOrgCode(tempContagionSampleInfoDto.getHspOrgCode());
            temp.setPatientName(tempContagionSampleInfoDto.getPatientName());
            temp.setSex(SexEnum.getByCode(tempContagionSampleInfoDto.getPatientSex()).getDesc());
            temp.setAge(PatientAges.toText(tempContagionSampleInfoDto));
            temp.setDepartment(tempContagionSampleInfoDto.getDept());
            temp.setBarcode(tempContagionSampleInfoDto.getBarcode());
            temp.setOutBarcode(tempContagionSampleInfoDto.getOutBarcode());
            temp.setReceiveTime(tempContagionSampleInfoDto.getSignDate());
            temp.setAuditTime(tempContagionSampleInfoDto.getFinalCheckDate());

            // 报告项目根据检验项目编码分组
            Map<String, List<RoutineInspectionDto.RoutineReportItem>> reportItemGroup = tempContagionSampleInfoDto.getReportItems().stream().collect(Collectors.groupingBy(RoutineInspectionDto.RoutineReportItem::getTestItemCode));
            // 传染病项目编码
            Set<String> contagionReportItemCodes = tempReportItems.stream().map(e -> e.getTestItemCode()).collect(Collectors.toSet());
            // 需要回显的检验项目
            List<BaseSampleEsModelDto.TestItem> testItems = tempContagionSampleInfoDto.getTestItems().stream().filter(e -> contagionReportItemCodes.contains(e.getTestItemCode())).collect(Collectors.toList());

            // 转换检验项目信息 （根据传染病项目回显检验项目，以及该检验项目下包含的其他报告项目）
            temp.setTestItems(testItems.stream().map(e -> this.convertTestItem(e, reportItemGroup)).collect(Collectors.toList()));

            queryContagionSampleInfoDtoList.add(temp);
        }

        // 按照接收时间正序排序返回
        return queryContagionSampleInfoDtoList.stream().sorted(Comparator.comparing(QueryContagionSampleInfoDto::getReceiveTime)).collect(Collectors.toList());
    }

    // 检验项目转化
    private QueryContagionSampleInfoDto.ContagionTestItemDto convertTestItem(BaseSampleEsModelDto.TestItem testItem, Map<String, List<RoutineInspectionDto.RoutineReportItem>> reportItemGroup) {
        QueryContagionSampleInfoDto.ContagionTestItemDto tempContagionTestItemDto = new QueryContagionSampleInfoDto.ContagionTestItemDto();
        tempContagionTestItemDto.setTestItemId(String.valueOf(testItem.getTestItemId()));
        tempContagionTestItemDto.setTestItemCode(testItem.getTestItemCode());
        tempContagionTestItemDto.setTestItemName(testItem.getTestItemName());
        List<RoutineInspectionDto.RoutineReportItem> routineReportItems = reportItemGroup.get(testItem.getTestItemCode());
        if (CollectionUtils.isNotEmpty(routineReportItems)) {
            tempContagionTestItemDto.setReportItemDtos(routineReportItems.stream()
                    .sorted(Comparator.comparing(BaseSampleEsModelDto.ReportItem::getPrintSort))
                    .map(this::convertReportItem)
                    .collect(Collectors.toList()));
        }

        return tempContagionTestItemDto;
    }

    // 遗传报告项目转换
    private QueryContagionSampleInfoDto.ContagionReportItemDto convertReportItem(RoutineInspectionDto.RoutineReportItem routineReportItem) {
        QueryContagionSampleInfoDto.ContagionReportItemDto temp = new QueryContagionSampleInfoDto.ContagionReportItemDto();
        temp.setReportItemCode(routineReportItem.getReportItemCode());
        temp.setReportItemName(routineReportItem.getReportItemName());
        temp.setResult(routineReportItem.getResult());
        temp.setJudge(routineReportItem.getJudge());
        return temp;
    }


}
