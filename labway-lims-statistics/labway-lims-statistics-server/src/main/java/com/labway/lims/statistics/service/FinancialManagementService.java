package com.labway.lims.statistics.service;

import com.labway.lims.api.enums.apply.CustomerNameTypeEnum;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.statistics.vo.HspOrgSendDoctorStatisticsRequestVo;

import java.util.List;

/**
 * 财务管理 Service
 *
 * <AUTHOR>
 * @since 2023/5/15 10:05
 */
public interface FinancialManagementService {

    /**
     * 查询样本列表 - 根据时间拆分并行查询
     */
    List<BaseSampleEsModelDto> selectSamples(SampleEsQuery query, HspOrgSendDoctorStatisticsRequestVo vo);

    /**
     * 查询样本列表 - 根据时间拆分并行查询
     */
    List<BaseSampleEsModelDto> selectSamples(SampleEsQuery query, boolean financialMonth);

    /**
     * 获取销售项目 收入汇总数据查询
     * 
     * @param dto es查询条件
     * @param filterDto 过滤数据条件
     * @param baseSampleEsModelDtosAll 所有es 数据审核后的
     * @return 汇总响应数据
     */
    List<TestItemIncomeSummaryResponseDto> getTestItemIncomeSummaryResponseDtos(SampleEsQuery dto,
        TestItemIncomeFilterDto filterDto, List<BaseSampleEsModelDto> baseSampleEsModelDtosAll);

    /**
     * 获取销售项目 明细数据查询
     * 
     * @param dto 查询条件
     * @param filterDto 过滤数据条件
     * @param baseSampleEsModelDtos 所有es 数据审核后的
     */
    List<TestItemIncomeDetailResponseDto> getTestItemIncomeDetailResponseDto(SampleEsQuery dto,
        TestItemIncomeFilterDto filterDto, List<BaseSampleEsModelDto> baseSampleEsModelDtos);

    /**
     * 获取销售项目 明细数据查询【检验项目分开显示】
     *
     * @param dto 查询条件
     * @param filterDto 过滤数据条件
     * @param baseSampleEsModelDtos 所有es 数据审核后的
     */
    List<TestItemIncomeDetailResponseDto> getTestItemIncomeDetailResponseDto2(SampleEsQuery dto,
                                                                              TestItemIncomeFilterDto filterDto, List<BaseSampleEsModelDto> baseSampleEsModelDtos);

    /**
     * 获取机构送检医生 统计
     *
     * @param dto 查询条件
     * @param filterDto 过滤数据条件
     * @param baseSampleEsModelDtos 所有es 数据审核后的
     */
    List<HspOrgSendDoctorStatisticsResponseDto> getHspOrgSendDoctorStatisticsResponseDto(SampleEsQuery dto,
        TestItemIncomeFilterDto filterDto, List<BaseSampleEsModelDto> baseSampleEsModelDtos);

    /**
     * 分平台统计（支持多送检机构查询）
     *
     * @param dto 查询条件
     * @param customerNameType 客户名称 类别
     * @param hspOrganizationDtos 送检机构
     * @param baseSampleEsModelDtosAll 所有es 数据
     * @return 响应数据
     */
    ByPlatformStatisticsResponseDto getByPlatformStatisticsResponseDto(
            SampleEsQuery dto, CustomerNameTypeEnum customerNameType, List<HspOrganizationDto> hspOrganizationDtos,
            List<BaseSampleEsModelDto> baseSampleEsModelDtosAll);

    /**
     * 分平台统计
     *
     * @param dto 查询条件
     * @param customerNameType 客户名称 类别
     * @param hspOrganization 送检机构
     * @param baseSampleEsModelDtos 所有es 数据
     * @return 响应数据
     */
    ByPlatformStatisticsResponseDto getByPlatformStatisticsResponseDto(SampleEsQuery dto,
        CustomerNameTypeEnum customerNameType, HspOrganizationDto hspOrganization,
        List<BaseSampleEsModelDto> baseSampleEsModelDtos);

    /**
     * 将es 数据 处理为最细颗粒度（样本检验项目）
     *
     * @param baseSampleEsModelDtosAll es数据 审核后的
     * @param stopTestChargeEsModelDtos 相同条件查找的 不管审核状态 但检验项目为终止检验收费的
     * @param pathologyEsModelDtos 相同条件查找的 病理检验 已经一次分拣
     * @param filterDto 数据过滤条件
     * @param needFinanceGroup 是否需要数据赋值 财务专业组
     */
    List<SampleTestItemDto> handleEsDataToSampleTestItemDto(List<BaseSampleEsModelDto> baseSampleEsModelDtosAll,
        List<BaseSampleEsModelDto> stopTestChargeEsModelDtos, List<BaseSampleEsModelDto> pathologyEsModelDtos,
        TestItemIncomeFilterDto filterDto, boolean needFinanceGroup);
}
