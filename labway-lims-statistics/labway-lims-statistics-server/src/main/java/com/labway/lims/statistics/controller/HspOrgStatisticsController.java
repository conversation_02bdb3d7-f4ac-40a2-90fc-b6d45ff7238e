
package com.labway.lims.statistics.controller;

import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplyLogisticsDto;
import com.labway.lims.apply.api.dto.ApplyLogisticsSampleDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.service.ApplyLogisticsSampleService;
import com.labway.lims.apply.api.service.ApplyLogisticsService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.vo.HspOrgStatisticsRequestVo;
import com.labway.lims.statistics.vo.HspOrgStatisticsVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 送检机构申请单查询
 */
@RestController
@RequestMapping("/hsp-org-statistics")
public class HspOrgStatisticsController extends BaseController {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;
    @DubboReference
    private ApplyLogisticsSampleService applyLogisticsSampleService;
    @DubboReference
    private ApplyLogisticsService applyLogisticsService;

    @PostMapping("/statistics")
    public List<HspOrgStatisticsVo> statistics(@RequestBody HspOrgStatisticsRequestVo vo) {

        if (Objects.isNull(vo.getBeginSignDate()) || Objects.isNull(vo.getEndSignDate())) {
            throw new IllegalStateException("日期错误");
        }

        final SampleEsQuery.SampleEsQueryBuilder builder = SampleEsQuery.builder();
        builder.startCreateDate(vo.getBeginSignDate());
        builder.endCreateDate(vo.getEndSignDate());

        if (Objects.nonNull(vo.getHspOrgId())) {
            builder.hspOrgIds(Set.of(vo.getHspOrgId()));
        }

        if (Objects.nonNull(vo.getTestItemId())) {
            builder.testItemIds(Set.of(vo.getTestItemId()));
        }

        if (StringUtils.isNotBlank(vo.getPatientName())) {
            builder.patientName(vo.getPatientName());
        }

        if (StringUtils.isNotBlank(vo.getBarcode())) {
            builder.barcodes(Set.of(vo.getBarcode()));
        }
        // 过滤已终止的样本
        builder.excludeSampleStatus(List.of(SampleStatusEnum.STOP_TEST.getCode()));

        final List<BaseSampleEsModelDto> samples = elasticSearchSampleService.selectSamples(builder.build());
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        final Map<HspOrgStatisticsVo, List<HspOrgStatisticsVo.Sample>> map = new LinkedHashMap<>();
        for (BaseSampleEsModelDto sample : samples) {
            final HspOrgStatisticsVo.Sample s = new HspOrgStatisticsVo.Sample();
            BeanUtils.copyProperties(sample, s);

            s.setPatientSubage(sample.getPatientSubage());
            s.setPatientSubageUnit(sample.getPatientSubageUnit());

            if (Objects.equals(SampleStatusEnum.AUDIT.getCode(), sample.getSampleStatus())) {
                s.setCheckDate(sample.getFinalCheckDate());
            }

            s.setStatus(sample.getSampleStatus());
            s.setTestItemNames(Collections.emptyList());

            if (CollectionUtils.isNotEmpty(sample.getTestItems())) {
                // 检验项目 排除终止的
                List<BaseSampleEsModelDto.TestItem> testItems = sample.getTestItems().stream()
                        .filter(obj -> !(Objects.equals(obj.getStopStatus(), StopTestStatus.STOP_TEST_CHARGE.getCode())
                                || Objects.equals(obj.getStopStatus(), StopTestStatus.STOP_TEST_FREE.getCode())))
                        .collect(Collectors.toList());
                s.setTestItemNames(testItems.stream().map(BaseSampleEsModelDto.TestItem::getTestItemName)
                        .collect(Collectors.toList()));
            }

            final HspOrgStatisticsVo v = new HspOrgStatisticsVo();
            v.setHspOrgId(sample.getHspOrgId());
            v.setHspOrgName(sample.getHspOrgName());

            map.computeIfAbsent(v, k -> new LinkedList<>()).add(s);
        }

        return map.entrySet().stream().map(e -> {
            final HspOrgStatisticsVo v = new HspOrgStatisticsVo();
            v.setHspOrgId(e.getKey().getHspOrgId());
            v.setHspOrgName(e.getKey().getHspOrgName());
            v.setSignCount(e.getValue().size());
            // 已经审核的数量
            v.setAuditCount((int) e.getValue().stream().filter(l -> Objects.equals(l.getStatus(), SampleStatusEnum.AUDIT.getCode())).count());
            v.setSamples(e.getValue());
            return v;
        }).collect(Collectors.toList());
    }

    @PostMapping("/images")
    public List<String> images(Long applyId) {

        if (Objects.isNull(applyId)) {
            throw new IllegalStateException("参数错误");
        }

        final ApplyLogisticsSampleDto als = applyLogisticsSampleService.selectByApplyId(applyId);
        if (Objects.isNull(als)) {
            return Collections.emptyList();
        }

        final ApplyLogisticsDto applyLogistics = applyLogisticsService.selectByApplyLogisticsId(als.getApplyLogisticsId());
        if (Objects.isNull(applyLogistics)) {
            return Collections.emptyList();
        }

        if (StringUtils.isBlank(applyLogistics.getApplyImage())) {
            return Collections.emptyList();
        }

        return Arrays.asList(applyLogistics.getApplyImage().split(","));

    }


}
