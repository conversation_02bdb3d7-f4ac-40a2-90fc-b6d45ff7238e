package com.labway.lims.statistics.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.labway.lims.statistics.dto.SampleArchiveDto;
import com.labway.lims.statistics.dto.SampleArchivePageDto;
import com.labway.lims.statistics.dto.SampleArchiveQueryDto;
import com.labway.lims.statistics.dto.SampleArchiveReportItemDto;

import javax.annotation.Nullable;
import java.util.List;

/**
 * <p>
 * SampleArchiveService
 * 样本归档信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/31 13:14
 */
public interface SampleArchiveService {

    /**
     * 样本归档信息查询
     */
    Page<SampleArchiveDto> selectSampleArchive(SampleArchiveQueryDto queryDto);

    SampleArchivePageDto selectSampleArchivePage(SampleArchiveQueryDto queryDto);

    @Nullable
    List<SampleArchiveDto> selectSampleArchive(String barcode, Long groupId);

    /**
     * 根据申请单样本ID查询报告项目结果
     */
    List<SampleArchiveReportItemDto> selectReportItem(Long applySampleId);

}
