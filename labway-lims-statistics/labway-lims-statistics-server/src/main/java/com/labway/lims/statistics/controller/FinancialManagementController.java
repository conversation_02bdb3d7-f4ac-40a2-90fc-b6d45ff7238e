package com.labway.lims.statistics.controller;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.DataFormatData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Lists;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.EsConfig;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.PdfTemplateTypeEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.HspOrgSendDoctorStatisticsItemDto;
import com.labway.lims.apply.api.dto.HspOrgSendDoctorStatisticsResponseDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.TestItemIncomeDetailItemDto;
import com.labway.lims.apply.api.dto.TestItemIncomeDetailResponseDto;
import com.labway.lims.apply.api.dto.TestItemIncomeSummaryResponseDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.dto.ExportRecordDto;
import com.labway.lims.statistics.dto.FieldData;
import com.labway.lims.statistics.enums.DisplayOrgType;
import com.labway.lims.statistics.enums.ExportFileType;
import com.labway.lims.statistics.enums.ExportStatus;
import com.labway.lims.statistics.excel.DetectionMergeStrategy;
import com.labway.lims.statistics.excel.RowRangeDto;
import com.labway.lims.statistics.service.ExportService;
import com.labway.lims.statistics.service.FinancialManagementService;
import com.labway.lims.statistics.service.bySendDoctor.HspOrgBySendDoctorService;
import com.labway.lims.statistics.service.impl.FinancialManagementServiceImpl;
import com.labway.lims.statistics.service.income.IncomeSummaryService;
import com.labway.lims.statistics.vo.ExportResultVo;
import com.labway.lims.statistics.vo.HspOrgSendDoctorStatisticsRequestVo;
import com.labway.lims.statistics.vo.IncomeSummaryVo;
import com.labway.lims.statistics.vo.TestItemIncomeRequestVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.labway.lims.api.ConditionCheckUtils.isValidMonthFormat;

/**
 * 财务管理 API
 *
 * <AUTHOR>
 * @since 2023/5/15 9:53
 */
@Slf4j
@RefreshScope
@RestController
@RequestMapping("/financial-management")
public class FinancialManagementController extends BaseController implements ApplicationContextAware {

    @Resource
    private FinancialManagementService financialManagementService;
    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;

    @DubboReference
    private PdfReportService pdfReportService;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @Resource
    private EnvDetector envDetector;
    @Resource
    private EsConfig esConfig;
    @Resource
    private ExportService exportService;

    @Value("${excel.company.content:广州兰卫医学检验实验室有限公司\n" +
            "开户银行：工商银行广州中山六路支行        银行账号：3602014109200137868\n" +
            "TEL:020-87603168FAX020-87603168\n" +
            "如有疑问敬请来电，谢谢\n" +
            "=======================================================================================================================\n" +
            "对账回执单\n" +
            "1、数据无误\n" +
            "         单位签章：                   日期：\n" +
            "\n" +
            "\n" +
            "\n" +
            "2、数据不符及需说明的事项\n" +
            "         单位签章：                   日期\n" +
            "\n}")
    private String companyContentRow;

    @Value("${statistics.by-send-doctor.version:2.0}")
    private String bySendDoctorVersion;
    @Value("${statistics.income-summary.version:2.0}")
    private String incomeSummaryVersion;

    // 根据送检医生统计财务
    private final Map<String, HspOrgBySendDoctorService> HSPORG_BY_SEND_DOCTOR_SERVICE_MAP = new HashMap<>();

    // 销售项目收入查询Map
    private final Map<String, IncomeSummaryService> INCOME_SUMMARY_SERVICE_MAP = new HashMap<>();


    /**
     * 销售项目收入查询--汇总
     */
    @PostMapping("/test-item/income-summary")
    public IncomeSummaryVo testItemIncomeSummary(@RequestBody TestItemIncomeRequestVo vo) {

        return getIncomeSummaryService().incomeSummaryStatistics(vo);

    }

    /**
     * 销售项目收入查询--汇总 导出，支持单个和全部
     * <p>
     * <p>
     * 导出单个的时候传入这个机构的客商ID，全部的时候为空
     */
    @Deprecated
    @PostMapping("/test-item/income-summary-export-new")
    public ResponseEntity<StreamingResponseBody> testItemIncomeSummarySingleExport(@RequestBody TestItemIncomeRequestVo vo) {
        final List<TestItemIncomeSummaryResponseDto> list = new ArrayList<>(testItemIncomeSummary(vo).getList());
        final FinancialManagementServiceImpl.DateRange range = FinancialManagementServiceImpl.getDateRange(getSampleEsQueryFromTestItemIncomeSummaryRequestVo(vo));

        return ResponseEntity.ok()
                .header(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION)
                .contentType(new MediaType("application", "zip")).header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode(range.getStartDate() + "至" + range.getEndDate()
                                + "-销售项目收入.zip", StandardCharsets.UTF_8)))
                .body(os -> {
                    final AtomicInteger index = new AtomicInteger(0);
                    try (final ZipOutputStream zos = ZipUtil.getZipOutputStream(os, StandardCharsets.UTF_8)) {

                        // 合并就讲所有的数据汇集在一个对象里面
                        if (Objects.equals(vo.getDisplayOrgType(), DisplayOrgType.MERGE.getType())) {
                            if (CollectionUtils.isNotEmpty(list)) {
                                final Iterator<TestItemIncomeSummaryResponseDto> iterator = list.iterator();
                                final TestItemIncomeSummaryResponseDto next = iterator.next();
                                if (Objects.isNull(next.getDetail())) {
                                    final TestItemIncomeDetailResponseDto dto = new TestItemIncomeDetailResponseDto();
                                    dto.setCountSum(0);
                                    dto.setFeePriceSum(BigDecimal.ZERO);
                                    dto.setItemList(Lists.newArrayList());
                                    dto.setCustomerName(StringUtils.EMPTY);
                                    next.setDetail(dto);
                                }
                                while (iterator.hasNext()) {
                                    final TestItemIncomeSummaryResponseDto temp = iterator.next();
                                    if (Objects.nonNull(next.getDetail())) {
                                        final TestItemIncomeDetailResponseDto detail = temp.getDetail();
                                        if (Objects.nonNull(detail)) {
                                            next.getDetail().getItemList().addAll(ObjectUtils.defaultIfNull(detail.getItemList(), Lists.newArrayList()));
                                            next.getDetail().setFeePriceSum(next.getDetail().getFeePriceSum().add(detail.getFeePriceSum()));
                                            next.getDetail().setCountSum(next.getDetail().getCountSum() + detail.getCountSum());
                                            next.getDetail().setCustomerName(StringUtils.EMPTY);
                                        }
                                    }
                                    next.getItemList().addAll(temp.getItemList());
                                    next.setPayAmountSum(next.getPayAmountSum().add(temp.getPayAmountSum()));
                                    next.setFeePriceSum(next.getFeePriceSum().add(temp.getFeePriceSum()));
                                    next.setCountSum(next.getCountSum() + temp.getCountSum());
                                    next.setCustomerName(StringUtils.EMPTY);
                                }

                                next.setCustomerName(StringUtils.EMPTY);
                                next.setCustomerId(NumberUtils.LONG_ZERO);
                                list.clear();
                                list.add(next);
                            }
                        }

                        for (TestItemIncomeSummaryResponseDto e : list) {
                            {
                                // 获取模板文件
                                try (InputStream is = new ClassPathResource("template/销售项目收入汇总查询模版.xlsx").getInputStream();) {
                                    final ByteArrayOutputStream out = new ByteArrayOutputStream();
                                    final XSSFWorkbook workbook = new XSSFWorkbook(is);
                                    workbook.setSheetName(0, StringUtils.defaultIfBlank(StringUtils.substring(e.getCustomerName(), 0, 31),
                                            "Sheet"));
                                    workbook.write(out);

                                    final String filename = index.incrementAndGet() + "-" + e.getCustomerName()
                                            + "-" + range.getStartDate() + "至" + range.getEndDate() + "-销售项目收入统计-汇总数据.xlsx";
                                    zos.putNextEntry(new ZipEntry(filename));

                                    try (ExcelWriter ew = EasyExcelFactory.write(zos).autoCloseStream(false).excelType(ExcelTypeEnum.XLSX)
                                            .withTemplate(new ByteArrayInputStream(out.toByteArray())).build()) {
                                        final FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                                        // 填充第 i+1 个 Sheet 的数据
                                        final WriteSheet writeSheet = EasyExcelFactory.writerSheet(0).build();
                                        // 列表数据
                                        for (List<TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem> itemList : Lists.partition(e.getItemList(), 1000)) {
                                            ew.fill(itemList, fillConfig, writeSheet);
                                        }
                                        // 其他数据
                                        e.setCustomerName(StringUtils.isNotBlank(e.getCustomerName()) ? ("客户名称：" + e.getCustomerName()) : StringUtils.EMPTY);
                                        ew.fill(e, writeSheet);
                                        ew.finish();
                                    }

                                    zos.closeEntry();
                                }
                            }

                            // 获取模板文件
                            String templatePath = "template/销售项目收入明细查询模版.xlsx";
                            if (envDetector.isChangzhou()) {
                                templatePath = "template/cz/销售项目收入明细查询模版.xlsx";
                            }
                            try (InputStream is = new ClassPathResource(templatePath).getInputStream();) {
                                final ByteArrayOutputStream out = new ByteArrayOutputStream();
                                final XSSFWorkbook workbook = new XSSFWorkbook(is);
                                workbook.setSheetName(0, StringUtils.defaultIfBlank(StringUtils.substring(e.getCustomerName(), 0, 31),
                                        "Sheet"));
                                workbook.write(out);

                                final String filename = index.incrementAndGet() + "-" + e.getCustomerName()
                                        + "-" + range.getStartDate() + "至" + range.getEndDate() + "-销售项目收入统计-明细数据.xlsx";
                                zos.putNextEntry(new ZipEntry(filename));
                                try (ExcelWriter excelWriter = EasyExcelFactory.write(zos).autoCloseStream(false).excelType(ExcelTypeEnum.XLSX)
                                        .withTemplate(new ByteArrayInputStream(out.toByteArray())).build()) {
                                    final FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                                    // 填充第 i+1 个 Sheet 的数据
                                    WriteSheet writeSheet = EasyExcelFactory.writerSheet(0).build();
                                    // 列表数据
                                    final Optional<TestItemIncomeDetailResponseDto> detail = Optional.ofNullable(e.getDetail());
                                    List<TestItemIncomeDetailItemDto> dataList = detail.map(TestItemIncomeDetailResponseDto::getItemList).orElse(Lists.newArrayList());
                                    for (List<TestItemIncomeDetailItemDto> itemList : Lists.partition(dataList, 1000)) {
                                        excelWriter.fill(itemList, fillConfig, writeSheet);
                                    }
                                    // 其他数据
                                    TestItemIncomeDetailResponseDto data = detail.orElse(new TestItemIncomeDetailResponseDto());
                                    data.setCustomerName(StringUtils.isNotBlank(data.getCustomerName()) ? ("客户名称：" + data.getCustomerName()) : StringUtils.EMPTY);
                                    excelWriter.fill(data, writeSheet);
                                    excelWriter.finish();
                                }
                                zos.closeEntry();
                            }
                        }
                    }
                });
    }

    /**
     * 售项目收入查询--汇总 导出 异步
     */
    @PostMapping("/test-item/income-summary-export-new-async")
    public ResponseEntity<ExportResultVo> testItemIncomeSummarySingleExportAsync(@RequestBody TestItemIncomeRequestVo vo) {
        Assert.notNull(vo.getSource(), "页面来源不能为空");
        LoginUserHandler.User user = LoginUserHandler.get();

        final FinancialManagementServiceImpl.DateRange range = FinancialManagementServiceImpl.getDateRange(getSampleEsQueryFromTestItemIncomeSummaryRequestVo(vo));
        final String exportFileName = range.getStartDate() + "至" + range.getEndDate() + "-销售项目收入.zip";

        final ExportRecordDto exportRecordDto = new ExportRecordDto();
        exportRecordDto.setFileName(exportFileName);
        exportRecordDto.setFileType(ExportFileType.ZIP.getValue());
        exportRecordDto.setStatus(ExportStatus.RUNNING.getCode());
        exportRecordDto.setSource(vo.getSource());

        exportService.submitExportTask(exportRecordDto, () -> {
            LoginUserHandler.set(user);
            ByteArrayOutputStream os = new ByteArrayOutputStream();

            final AtomicInteger index = new AtomicInteger(0);
            try (final ZipOutputStream zos = ZipUtil.getZipOutputStream(os, StandardCharsets.UTF_8)) {
                final List<TestItemIncomeSummaryResponseDto> list = new ArrayList<>(testItemIncomeSummary(vo).getList());
                // 合并就讲所有的数据汇集在一个对象里面
                if (Objects.equals(vo.getDisplayOrgType(), DisplayOrgType.MERGE.getType())) {
                    if (CollectionUtils.isNotEmpty(list)) {
                        final Iterator<TestItemIncomeSummaryResponseDto> iterator = list.iterator();
                        final TestItemIncomeSummaryResponseDto next = iterator.next();
                        if (Objects.isNull(next.getDetail())) {
                            final TestItemIncomeDetailResponseDto dto = new TestItemIncomeDetailResponseDto();
                            dto.setCountSum(0);
                            dto.setFeePriceSum(BigDecimal.ZERO);
                            dto.setDiscountFeePriceSum(BigDecimal.ZERO);
                            dto.setItemList(Lists.newArrayList());
                            dto.setCustomerName(StringUtils.EMPTY);
                            next.setDetail(dto);
                        }
                        while (iterator.hasNext()) {
                            final TestItemIncomeSummaryResponseDto temp = iterator.next();
                            if (Objects.nonNull(next.getDetail())) {
                                final TestItemIncomeDetailResponseDto detail = temp.getDetail();
                                if (Objects.nonNull(detail)) {
                                    next.getDetail().getItemList().addAll(ObjectUtils.defaultIfNull(detail.getItemList(), Lists.newArrayList()));
                                    next.getDetail().setFeePriceSum(next.getDetail().getFeePriceSum().add(detail.getFeePriceSum()));
                                    next.getDetail().setCountSum(next.getDetail().getCountSum() + detail.getCountSum());
                                    next.getDetail().setDiscountFeePriceSum(next.getDetail().getDiscountFeePriceSum().add(detail.getDiscountFeePriceSum()));
                                    next.getDetail().setCustomerName(StringUtils.EMPTY);
                                }
                            }
                            next.getItemList().addAll(temp.getItemList());
                            next.setPayAmountSum(next.getPayAmountSum().add(temp.getPayAmountSum()));
                            next.setFeePriceSum(next.getFeePriceSum().add(temp.getFeePriceSum()));
                            next.setCountSum(next.getCountSum() + temp.getCountSum());
                            next.setCustomerName(StringUtils.EMPTY);
                        }

                        next.setCustomerName(StringUtils.EMPTY);
                        next.setCustomerId(NumberUtils.LONG_ZERO);
                        list.clear();
                        list.add(next);
                    }
                }

                final int headerRowCount = 3;
                for (TestItemIncomeSummaryResponseDto e : list) {
                    /*{
                        // 获取模板文件
                        try (InputStream is = new ClassPathResource("template/销售项目收入汇总查询模版.xlsx").getInputStream();) {
                            final ByteArrayOutputStream out = new ByteArrayOutputStream();
                            final XSSFWorkbook workbook = new XSSFWorkbook(is);
                            workbook.setSheetName(0, StringUtils.defaultIfBlank(StringUtils.substring(e.getCustomerName(), 0, 31),
                                    "Sheet"));
                            workbook.write(out);

                            final String filename = index.incrementAndGet() + "-" + e.getCustomerName()
                                    + "-" + range.getStartDate() + "至" + range.getEndDate() + "-销售项目收入统计-汇总数据.xlsx";
                            zos.putNextEntry(new ZipEntry(filename));

                            try (ExcelWriter ew = EasyExcelFactory.write(zos).autoCloseStream(false).excelType(ExcelTypeEnum.XLSX)
                                    .withTemplate(new ByteArrayInputStream(out.toByteArray())).build()) {
                                final FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                                // 填充第 i+1 个 Sheet 的数据
                                final WriteSheet writeSheet = EasyExcelFactory.writerSheet(0).build();
                                // 列表数据
                                for (List<TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem> itemList : Lists.partition(e.getItemList(), 1000)) {
                                    ew.fill(itemList, fillConfig, writeSheet);
                                }
                                // 其他数据
                                e.setCustomerName(StringUtils.isNotBlank(e.getCustomerName()) ? ("客户名称：" + e.getCustomerName()) : StringUtils.EMPTY);
                                ew.fill(e, writeSheet);
                                ew.finish();
                            }

                            zos.closeEntry();
                        }
                    }*/
                    {
                        final String filename = index.incrementAndGet() + "-" + e.getCustomerName()
                                + "-" + range.getStartDate() + "至" + range.getEndDate() + "-销售项目收入统计-汇总数据.xlsx";
                        zos.putNextEntry(new ZipEntry(filename));

                        List<Integer> mergeRows = new ArrayList<>();
                        Map<Integer, List<RowRangeDto>> strategyMap = new HashMap<>();
                        mergeRows.add(0);
                        strategyMap.put(0, List.of(new RowRangeDto(0, TESTITEM_INCOME_SUMMARY_HEADERS.size() - 1)));
                        mergeRows.add(1);
                        strategyMap.put(1, List.of(new RowRangeDto(0, TESTITEM_INCOME_SUMMARY_HEADERS.size() - 1)));

                        // 导出的数据
                        List<TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem> testItemIncomeSummaryItems = e.getItemList();
                        // 添加合计行
                        TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem testItemIncomeSummaryItem = new TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem();
                        testItemIncomeSummaryItem.setCustomerName("合计");
                        // 数量
                        testItemIncomeSummaryItem.setCount(e.getCountSum());
                        // 合计金额
                        testItemIncomeSummaryItem.setFeePriceSum(e.getFeePriceSum());
                        // 结算金额
                        testItemIncomeSummaryItem.setPayAmount(e.getPayAmountSum());

                        // 总计行合并
                        mergeRows.add(headerRowCount + testItemIncomeSummaryItems.size());
                        strategyMap.put(headerRowCount + testItemIncomeSummaryItems.size(), List.of(new RowRangeDto(0, 3)));
                        testItemIncomeSummaryItems.add(testItemIncomeSummaryItem);
                        // 公司抬头行合并
                        mergeRows.add(headerRowCount + testItemIncomeSummaryItems.size());
                        strategyMap.put(headerRowCount + testItemIncomeSummaryItems.size(), List.of(new RowRangeDto(0, TESTITEM_INCOME_SUMMARY_HEADERS.size() - 1)));

                        CellStyleMergeStrategy mergeStrategy = new CellStyleMergeStrategy(mergeRows, strategyMap, 4, TESTITEM_INCOME_SUMMARY_HEADERS.size(), headerRowCount + testItemIncomeSummaryItems.size());
                        mergeStrategy.addNumberColumnIndex(4, 5, 6, 8);
                        testItemIncomeSummaryExport(e, zos, mergeStrategy);

                        zos.closeEntry();
                    }

                    /*{
                        // 获取模板文件
                        String templatePath = "template/销售项目收入明细查询模版.xlsx";
                        if (envDetector.isChangzhou()) {
                            templatePath = "template/cz/销售项目收入明细查询模版.xlsx";
                        }
                        try (InputStream is = new ClassPathResource(templatePath).getInputStream();) {
                            final ByteArrayOutputStream out = new ByteArrayOutputStream();
                            final XSSFWorkbook workbook = new XSSFWorkbook(is);
                            workbook.setSheetName(0, StringUtils.defaultIfBlank(StringUtils.substring(e.getCustomerName(), 0, 31),
                                    "Sheet"));
                            workbook.write(out);

                            final String filename = index.incrementAndGet() + "-" + e.getCustomerName()
                                    + "-" + range.getStartDate() + "至" + range.getEndDate() + "-销售项目收入统计-明细数据.xlsx";
                            zos.putNextEntry(new ZipEntry(filename));
                            try (ExcelWriter excelWriter = EasyExcelFactory.write(zos).autoCloseStream(false).excelType(ExcelTypeEnum.XLSX)
                                    .withTemplate(new ByteArrayInputStream(out.toByteArray())).build()) {
                                final FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                                // 填充第 i+1 个 Sheet 的数据
                                WriteSheet writeSheet = EasyExcelFactory.writerSheet(0).build();
                                // 列表数据
                                final Optional<TestItemIncomeDetailResponseDto> detail = Optional.ofNullable(e.getDetail());
                                List<TestItemIncomeDetailItemDto> dataList = detail.map(TestItemIncomeDetailResponseDto::getItemList).orElse(Lists.newArrayList());
                                for (List<TestItemIncomeDetailItemDto> itemList : Lists.partition(dataList, 1000)) {
                                    excelWriter.fill(itemList, fillConfig, writeSheet);
                                }
                                // 其他数据
                                TestItemIncomeDetailResponseDto data = detail.orElse(new TestItemIncomeDetailResponseDto());
                                data.setCustomerName(StringUtils.isNotBlank(data.getCustomerName()) ? ("客户名称：" + data.getCustomerName()) : StringUtils.EMPTY);
                                excelWriter.fill(data, writeSheet);
                                excelWriter.finish();
                            }
                            zos.closeEntry();
                        }
                    }*/

                    {
                        // 导出的数据
                        final Optional<TestItemIncomeDetailResponseDto> detail = Optional.ofNullable(e.getDetail());
                        final String filename = index.incrementAndGet() + "-" + e.getCustomerName()
                                + "-" + range.getStartDate() + "至" + range.getEndDate() + "-销售项目收入统计-明细数据.xlsx";
                        zos.putNextEntry(new ZipEntry(filename));

                        List<Integer> mergeRows = new ArrayList<>();
                        Map<Integer, List<RowRangeDto>> strategyMap = new HashMap<>();
                        mergeRows.add(0);
                        strategyMap.put(0, List.of(new RowRangeDto(0, TESTITEM_INCOME_DETAIL_HEADS.size() - 1)));
                        mergeRows.add(1);
                        strategyMap.put(1, List.of(new RowRangeDto(0, TESTITEM_INCOME_DETAIL_HEADS.size() - 1)));

                        List<TestItemIncomeDetailItemDto> dataList = detail.map(TestItemIncomeDetailResponseDto::getItemList).orElse(Lists.newArrayList());
                        // 添加合计行
                        TestItemIncomeDetailItemDto testItemIncomeDetailItemDto = new TestItemIncomeDetailItemDto();
                        testItemIncomeDetailItemDto.setCustomerName("合计");
                        testItemIncomeDetailItemDto.setCount(detail.map(TestItemIncomeDetailResponseDto::getCountSum).orElse(NumberUtils.INTEGER_ZERO));
                        testItemIncomeDetailItemDto.setFeePrice(detail.map(TestItemIncomeDetailResponseDto::getFeePriceSum).orElse(BigDecimal.ZERO));
                        testItemIncomeDetailItemDto.setDiscountFeePriceLabel(detail.map(TestItemIncomeDetailResponseDto::getDiscountFeePriceSum).orElse(BigDecimal.ZERO).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP) + StringPool.EMPTY);

                        // 总计行合并
                        mergeRows.add(headerRowCount + dataList.size());
                        strategyMap.put(headerRowCount + dataList.size(), List.of(new RowRangeDto(0, 6)));
                        dataList.add(testItemIncomeDetailItemDto);
                        // 公司抬头行合并
                        mergeRows.add(headerRowCount + dataList.size());
                        strategyMap.put(headerRowCount + dataList.size(), List.of(new RowRangeDto(0, TESTITEM_INCOME_SUMMARY_HEADERS.size() - 1)));

                        CellStyleMergeStrategy mergeStrategy = new CellStyleMergeStrategy(mergeRows, strategyMap, 7, TESTITEM_INCOME_DETAIL_HEADS.size(), headerRowCount + dataList.size());
                        mergeStrategy.addNumberColumnIndex(7, 9, 10);
                        TestItemIncomeDetailResponseDto target = detail.orElseGet(() -> {
                            TestItemIncomeDetailResponseDto detailResponseDto = new TestItemIncomeDetailResponseDto();
                            detailResponseDto.setItemList(dataList);
                            detailResponseDto.setCustomerName(e.getCustomerName());
                            detailResponseDto.setStartDate(e.getStartDate());
                            detailResponseDto.setEndDate(e.getEndDate());
                            return detailResponseDto;
                        });
                        testItemIncomeDetailExport(target, zos, mergeStrategy);

                        zos.closeEntry();
                    }
                }
            } catch (Exception e) {
                log.error("导出失败", e);
                exportRecordDto.setEx(e);
            }

            if (Objects.isNull(exportRecordDto.getEx())) {
                String upload = huaweiObsUtils.upload(new ByteArrayInputStream(os.toByteArray()), "application/zip");
                exportRecordDto.setUrl(upload);
            }

            return exportRecordDto;
        });

        return ResponseEntity.ok(new ExportResultVo());
    }

    public void testItemIncomeSummaryExport(TestItemIncomeSummaryResponseDto target, OutputStream out, DetectionMergeStrategy mergeStrategy) {
        try (ExcelWriter excelWriter =
                     EasyExcelFactory.write(out).autoCloseStream(false)
                             .excelType(ExcelTypeEnum.XLSX)
                             .registerWriteHandler(mergeStrategy).build()) {

            List<List<Object>> dataList = Lists.newArrayList();
            List<List<String>> headerList = Lists.newArrayList();

            // 设置表头
            List<String> headers = TESTITEM_INCOME_SUMMARY_HEADERS.stream().map(FieldData::getFieldName).collect(Collectors.toList());
            for (String item : headers) {
                headerList.add(List.of(item));
            }

            // 设置表头
            dataList.add(List.of(String.format("%s%s至%s对账报表(项目汇总)", target.getCustomerName(), target.getStartDate(), target.getEndDate())));
            if (StringUtils.isNotBlank(target.getCustomerName())) {
                dataList.add(List.of(String.format("客户名称：%s   日期范围：%s至%s", target.getCustomerName(), target.getStartDate(), target.getEndDate())));
            } else {
                dataList.add(List.of(String.format("日期范围：%s至%s", target.getStartDate(), target.getEndDate())));
            }

            // 设置表格数据
            fillExcelContent(dataList, headers, TESTITEM_INCOME_SUMMARY_HEADERS, target.getItemList().stream().map(e -> (Object) e).collect(Collectors.toList()));

            // 设置底部内容
            dataList.add(List.of(companyContentRow));

            // 获取sheet对象
            WriteSheet sheet0 = EasyExcelFactory.writerSheet(0, "Sheet1")/*.head(headerList).needHead(Boolean.TRUE)*/.build();
            excelWriter.write(dataList, sheet0);

            excelWriter.finish();
        }
    }
    public void testItemIncomeDetailExport(TestItemIncomeDetailResponseDto target, OutputStream out, DetectionMergeStrategy mergeStrategy) {
        try (ExcelWriter excelWriter =
                     EasyExcelFactory.write(out).autoCloseStream(false)
                             .excelType(ExcelTypeEnum.XLSX)
                             .registerWriteHandler(mergeStrategy).build()) {

            List<List<Object>> dataList = Lists.newArrayList();
            List<List<String>> headerList = Lists.newArrayList();

            // 设置表头
            List<String> headers = TESTITEM_INCOME_DETAIL_HEADS.stream().map(FieldData::getFieldName).collect(Collectors.toList());
            for (String item : headers) {
                headerList.add(List.of(item));
            }

            // 设置表头
            dataList.add(List.of(String.format("%s%s至%s对账报表(明细表)", target.getCustomerName(), target.getStartDate(), target.getEndDate())));
            if (StringUtils.isNotBlank(target.getCustomerName())) {
                dataList.add(List.of(String.format("客户名称：%s   日期范围：%s至%s", target.getCustomerName(), target.getStartDate(), target.getEndDate())));
            } else {
                dataList.add(List.of(String.format("日期范围：%s至%s", target.getStartDate(), target.getEndDate())));
            }

            // 设置表格数据
            fillExcelContent(dataList, headers, TESTITEM_INCOME_DETAIL_HEADS, target.getItemList().stream().map(e -> (Object) e).collect(Collectors.toList()));

            // 设置底部内容
            dataList.add(List.of(companyContentRow));

            // 获取sheet对象
            WriteSheet sheet0 = EasyExcelFactory.writerSheet(0, "Sheet1")/*.head(headerList).needHead(Boolean.TRUE)*/.build();
            excelWriter.write(dataList, sheet0);

            excelWriter.finish();
        }
    }

    /**
     * 销售项目收入查询--汇总 pdf
     */
    @PostMapping("/test-item/income-summary-pdf")
    public Object testItemIncomeSummaryPdf(@RequestBody TestItemIncomeRequestVo vo) {

        // 将查询条件 转换为es查询条件
        SampleEsQuery esDto = getSampleEsQueryFromTestItemIncomeSummaryRequestVo(vo);

        // 所有es 结构数据
        List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(esDto);

        // 展示数据结构
        List<TestItemIncomeSummaryResponseDto> targetList =
                financialManagementService.getTestItemIncomeSummaryResponseDtos(esDto, vo, baseSampleEsModelDtos);

        // 避免 展示空白 页
        if (CollectionUtils.isEmpty(targetList)) {
            targetList.add(new TestItemIncomeSummaryResponseDto());
        }
        ThreadPoolExecutor pool = threadPoolConfig.getPool();

        List<CompletableFuture<String>> taskList = Lists.newArrayList();
        targetList.forEach(item -> {
            CompletableFuture<String> task = CompletableFuture.supplyAsync(() -> {
                final PdfReportParamDto param = new PdfReportParamDto();
                param.put("targetList", Lists.newArrayList(item));
                // 生成的汇总保存 180 天
                return pdfReportService.build2Url(PdfTemplateTypeEnum.FINANCIAL_TEST_ITEM_INCOME_SUMMARY.getCode(),
                        param, 3);
            }, pool);
            taskList.add(task);
        });
        // 等待 future 全部完成
        CompletableFuture.allOf(taskList.toArray(new CompletableFuture[0])).join();
        List<String> urls = taskList.stream().map(obj -> {
            try {
                return obj.get();
            } catch (Exception e) {
                log.info("报错：{}", e.getMessage());
            }
            return null;
        }).filter(StringUtils::isNotBlank).collect(Collectors.toList());

        return Map.of("url", urls);
    }

    /**
     * 销售项目收入查询--汇总 导出
     */
    @PostMapping("/test-item/income-summary-export")
    public Object testItemIncomeSummaryExport(@RequestBody TestItemIncomeRequestVo vo) {
        // 将查询条件 转换为es查询条件
        SampleEsQuery esDto = getSampleEsQueryFromTestItemIncomeSummaryRequestVo(vo);

        // 所有es 结构数据
        List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(esDto);

        // 展示数据结构
        List<TestItemIncomeSummaryResponseDto> targetList =
                financialManagementService.getTestItemIncomeSummaryResponseDtos(esDto, vo, baseSampleEsModelDtos);

        // 获取模板文件
        final InputStream templateInputStream;
        try {

            InputStream inputStream = new ClassPathResource("template/销售项目收入汇总查询模版.xlsx").getInputStream();
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            int templateSheetIndex = 0;
            for (int i = 1; i < targetList.size(); i++) {
                String sheetName = String.format("%s%s", "Sheet", i + 1);
                workbook.cloneSheet(templateSheetIndex, sheetName);
            }
            List<String> temp = Lists.newArrayList();
            for (int i = 0; i < targetList.size(); i++) {
                String customerName = targetList.get(i).getCustomerName();
                if (StringUtils.isNotBlank(customerName)) {
                    if (temp.contains(customerName)) {
                        customerName = customerName + (i + 1);
                    }
                    // 如果字符超过 excel sheet 限制截取下
                    if (customerName.length() > 31) {
                        customerName = customerName.substring(0, 31);
                    }
                    if (!temp.contains(customerName)) {
                        workbook.setSheetName(i, customerName);
                        temp.add(customerName);
                    }
                }
            }

            workbook.write(out);
            byte[] bArray = out.toByteArray();
            templateInputStream = new ByteArrayInputStream(bArray);
        } catch (Exception e) {
            throw new IllegalStateException("模板文件获取失败", e);
        }

        ByteArrayOutputStream out = new ByteArrayOutputStream();

        try (ExcelWriter excelWriter =
                     EasyExcelFactory.write(out).excelType(ExcelTypeEnum.XLSX).withTemplate(templateInputStream).build()) {

            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();

            // 未查到任何数据 为避免导出包含变量的模版 传入空数据
            if (CollectionUtils.isEmpty(targetList)) {
                TestItemIncomeSummaryResponseDto temp = new TestItemIncomeSummaryResponseDto();
                targetList.add(temp);
            }

            for (int i = 0; i < targetList.size(); i++) {
                TestItemIncomeSummaryResponseDto target = targetList.get(i);

                // 填充第 i+1 个 Sheet 的数据
                WriteSheet writeSheet = EasyExcelFactory.writerSheet(i).build();

                // 列表数据
                for (List<TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem> itemList : Lists.partition(target.getItemList(), 1000)) {
                    excelWriter.fill(itemList, fillConfig, writeSheet);
                }

                // 其他数据
                excelWriter.fill(target, writeSheet);

            }
            excelWriter.finish();
        } catch (Exception e) {
            log.error("下载模板错误", e);
            throw new IllegalStateException(e.getMessage(), e);
        }

        byte[] data = out.toByteArray();

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("销售项目收入汇总查询.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body(data);
    }

    /**
     * 销售项目收入查询--明细
     */
    @PostMapping("/test-item/income-details")
    public Object testItemIncomeDetails(@RequestBody TestItemIncomeRequestVo vo) {

        // 将查询条件 转换为es查询条件
        SampleEsQuery dto = getSampleEsQueryFromTestItemIncomeDetailsRequestVo(vo);

        // 所有es 结构数据
        List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(dto);

        // 展示数据结构
        return financialManagementService.getTestItemIncomeDetailResponseDto2(dto, vo, baseSampleEsModelDtos);

    }

    /**
     * 销售项目收入查询--明细
     */
    @PostMapping("/test-item/income-details-pdf")
    public Object testItemIncomeDetailsPdf(@RequestBody TestItemIncomeRequestVo vo) {

        // 将查询条件 转换为es查询条件
        SampleEsQuery dto = getSampleEsQueryFromTestItemIncomeDetailsRequestVo(vo);

        // 所有es 结构数据
        List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(dto);

        // 展示数据结构
        List<TestItemIncomeDetailResponseDto> targetList =
                financialManagementService.getTestItemIncomeDetailResponseDto(dto, vo, baseSampleEsModelDtos);

        // 避免 展示空白 页
        if (CollectionUtils.isEmpty(targetList)) {
            targetList.add(new TestItemIncomeDetailResponseDto());
        }
        ThreadPoolExecutor pool = threadPoolConfig.getPool();
        List<CompletableFuture<String>> taskList = Lists.newArrayList();
        targetList.forEach(item -> {
            CompletableFuture<String> task = CompletableFuture.supplyAsync(() -> {
                final PdfReportParamDto param = new PdfReportParamDto();
                param.put("targetList", Lists.newArrayList(item));
                // 生成的汇总保存 180 天
                return pdfReportService.build2Url(PdfTemplateTypeEnum.FINANCIAL_TEST_ITEM_INCOME_DETAIL.getCode(),
                        param, 3);
            }, pool);
            taskList.add(task);
        });
        // 等待 future 全部完成
        CompletableFuture.allOf(taskList.toArray(new CompletableFuture[0])).join();
        List<String> urls = taskList.stream().map(obj -> {
            try {
                return obj.get();
            } catch (Exception e) {
                log.info("报错：{}", e.getMessage());
            }
            return null;
        }).filter(StringUtils::isNotBlank).collect(Collectors.toList());

        return Map.of("url", urls);
    }

    /**
     * 销售项目收入查询--明细 导出
     */
    @PostMapping("/test-item/income-details-export")
    public Object testItemIncomeDetailsExport(@RequestBody TestItemIncomeRequestVo vo) {
        // 将查询条件 转换为es查询条件
        SampleEsQuery dto = getSampleEsQueryFromTestItemIncomeDetailsRequestVo(vo);

        // 所有es 结构数据
        List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(dto);

        // 展示数据结构
        List<TestItemIncomeDetailResponseDto> targetList =
            financialManagementService.getTestItemIncomeDetailResponseDto2(dto, vo, baseSampleEsModelDtos);
        // 获取模板文件
        final InputStream templateInputStream;
        try {
            InputStream inputStream = new ClassPathResource("template/cz/销售项目收入明细查询模版.xlsx").getInputStream();
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            int templateSheetIndex = 0;
            for (int i = 1; i < targetList.size(); i++) {
                String sheetName = String.format("%s%s", "Sheet", i + 1);
                workbook.cloneSheet(templateSheetIndex, sheetName);
            }
            List<String> temp = Lists.newArrayList();
            for (int i = 0; i < targetList.size(); i++) {
                String customerName = targetList.get(i).getCustomerName();
                if (StringUtils.isNotBlank(customerName)) {
                    if (temp.contains(customerName)) {
                        customerName = customerName + (i + 1);
                    }
                    // 如果字符超过 excel sheet 限制截取下
                    if (customerName.length() > 31) {
                        customerName = customerName.substring(0, 31);
                    }
                    if (!temp.contains(customerName)) {
                        workbook.setSheetName(i, customerName);
                        temp.add(customerName);
                    }
                }
            }

            workbook.write(out);
            byte[] bArray = out.toByteArray();
            templateInputStream = new ByteArrayInputStream(bArray);
        } catch (Exception e) {
            throw new IllegalStateException("模板文件获取失败", e);
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        try (ExcelWriter excelWriter =
                     EasyExcelFactory.write(out).excelType(ExcelTypeEnum.XLSX).withTemplate(templateInputStream).build()) {

            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            // 未查到任何数据 为避免导出包含变量的模版 传入空数据
            if (CollectionUtils.isEmpty(targetList)) {
                TestItemIncomeDetailResponseDto temp = new TestItemIncomeDetailResponseDto();
                targetList.add(temp);
            }

            for (int i = 0; i < targetList.size(); i++) {
                TestItemIncomeDetailResponseDto target = targetList.get(i);

                // 填充第 i+1 个 Sheet 的数据
                WriteSheet writeSheet = EasyExcelFactory.writerSheet(i).build();

                // 列表数据
                for (List<TestItemIncomeDetailItemDto> itemList : Lists.partition(target.getItemList(), 1000)) {
                    excelWriter.fill(itemList, fillConfig, writeSheet);
                }

                // 其他数据
                excelWriter.fill(target, writeSheet);

            }
            excelWriter.finish();
        } catch (Exception e) {
            log.error("下载模板错误", e);
            throw new IllegalStateException(e.getMessage(), e);
        }

        byte[] data = out.toByteArray();

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("销售项目收入明细.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body(data);
    }

    /**
     * 机构送检医生--统计
     */
    @PostMapping("/hsp-org/send-doctor-statistics")
    public Object hspOrgSendDoctorStatistics(@RequestBody HspOrgSendDoctorStatisticsRequestVo vo) {
        // 将查询条件 转换为es查询条件
        SampleEsQuery dto = getSampleEsQueryFromHspOrgSendDoctorStatisticsRequestVo(vo);

        // 所有es 结构数据
        List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(dto);

        // 展示数据结构
        return financialManagementService.getHspOrgSendDoctorStatisticsResponseDto(dto, vo, baseSampleEsModelDtos);
    }

    /**
     * 机构送检医生--统计
     */
    @PostMapping("/hsp-org/send-doctor-statistics-pdf")
    public Object hspOrgSendDoctorStatisticsPdf(@RequestBody HspOrgSendDoctorStatisticsRequestVo vo) {
        //
        // // 将查询条件 转换为es查询条件
        // SampleEsQuery dto = getSampleEsQueryFromHspOrgSendDoctorStatisticsRequestVo(vo);
        //
        // // 所有es 结构数据
        // List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(dto);
        //
        // // 展示数据结构
        // List<HspOrgSendDoctorStatisticsResponseDto> targetList =
        // financialManagementService.getHspOrgSendDoctorStatisticsResponseDto(dto, vo, baseSampleEsModelDtos);
        //
        // // 避免 展示空白 页
        // if (CollectionUtils.isEmpty(targetList)) {
        // targetList.add(new HspOrgSendDoctorStatisticsResponseDto());
        // }
        // ThreadPoolExecutor pool = threadPoolConfig.getPool();
        //
        // List<CompletableFuture<String>> taskList = Lists.newArrayList();
        // targetList.forEach(item -> {
        // CompletableFuture<String> task = CompletableFuture.supplyAsync(() -> {
        // final PdfReportParamDto param = new PdfReportParamDto();
        // param.put("targetList", Lists.newArrayList(item));
        // // 生成的汇总保存 180 天
        // return pdfReportService
        // .build2Url(PdfTemplateTypeEnum.FINANCIAL_HSP_ORG_SEND_DOCTOR_STATISTICS.getCode(), param, 3);
        // }, pool);
        // taskList.add(task);
        // });
        // // 等待 future 全部完成
        // CompletableFuture.allOf(taskList.toArray(new CompletableFuture[0])).join();
        // List<String> urls = taskList.stream().map(obj -> {
        // try {
        // return obj.get();
        // } catch (Exception e) {
        // log.info("报错：{}", e.getMessage());
        // }
        // return null;
        // }).filter(StringUtils::isNotBlank).collect(Collectors.toList());

        ResponseEntity<?> responseEntity = this.hspOrgSendDoctorStatisticsExport(vo);
        List<String> urls = Lists.newArrayList();
        String upload = huaweiObsUtils.upload(new ByteArrayInputStream((byte[]) responseEntity.getBody()),
                "application/vnd.ms-excel", 3);
        urls.add(upload);
        return Map.of("url", urls);

    }

    /**
     * 机构送检医生--统计
     */
    @PostMapping("/hsp-org/by-send-doctor")
    public HspOrgSendDoctorStatisticsResponseDto hspOrgBySendDoctor(@RequestBody HspOrgSendDoctorStatisticsRequestVo vo) {
        return getHspOrgBySendDoctorService().hspOrgBySendDoctorStatistics(vo);
    }

    /**
     * 机构送检医生--统计
     */
    @Deprecated
    @PostMapping("/hsp-org/by-send-doctor-export")
    public ResponseEntity<StreamingResponseBody> hspOrgBySendDoctorExport(@RequestBody HspOrgSendDoctorStatisticsRequestVo vo) throws Exception {
        HspOrgSendDoctorStatisticsResponseDto target = this.hspOrgBySendDoctor(vo);

        switch (DisplayOrgType.getByType(vo.getDisplayOrgType())) {
            case SINGLE:// 单个机构
                if (target.getOrgList().size() == 1) {
                    return byPlatformExportCurrent(target.getOrgList().get(0));
                }
                // 导出zip文件名
                final String zipFileName = String.format("%s至%s机构送检医生统计.zip",
                        target.getOrgList().get(0).getStartDate().substring(0, 10),
                        target.getOrgList().get(0).getEndDate().substring(0, 10));

                return ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_DISPOSITION,
                                String.format("attachment; filename=%s", URLEncoder.encode(zipFileName, StandardCharsets.UTF_8)))
                        .header(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION, "filename")
                        .header("filename", URLEncoder.encode(zipFileName, StandardCharsets.UTF_8))
                        .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "zip"))
                        .body((StreamingResponseBody) os -> {
                            try (ZipOutputStream zipOutputStream = ZipUtil.getZipOutputStream(os, StandardCharsets.UTF_8)) {
                                Set<String> exist = new HashSet<>();
                                int i = 1;

                                for (HspOrgSendDoctorStatisticsResponseDto responseDto : target.getOrgList()) {
                                    String filename = String.format("%s%s至%s机构送检医生统计.xlsx",
                                            responseDto.getCustomerName(), responseDto.getStartDate().substring(0, 10), responseDto.getEndDate().substring(0, 10));

                                    if (!exist.add(filename)) {
                                        filename = String.format("(%s)", i++) + filename;
                                    }
                                    ByteArrayOutputStream out = new ByteArrayOutputStream();

                                    // 添加 Excel 文件到 ZIP
                                    zipOutputStream.putNextEntry(new ZipEntry(URLDecoder.decode(filename, StandardCharsets.UTF_8)));
                                    try (InputStream templateInputStream = new ClassPathResource("template/机构送检医生统计模版.xlsx").getInputStream();
                                         ExcelWriter excelWriter = EasyExcelFactory.write(zipOutputStream).autoCloseStream(false).excelType(ExcelTypeEnum.XLSX).withTemplate(templateInputStream).build()) {
                                        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                                        // 填充 Sheet 的数据
                                        WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
                                        // 列表数据
                                        for (List<HspOrgSendDoctorStatisticsItemDto> itemList : Lists.partition(responseDto.getItemList(), 1000)) {
                                            excelWriter.fill(itemList, fillConfig, writeSheet);
                                        }
                                        // 其他数据
                                        excelWriter.fill(responseDto, writeSheet);
                                        excelWriter.finish();
                                    } catch (Exception e) {
                                        log.error("下载模板错误", e);
                                        throw new LimsException(e.getMessage(), e);
                                    }
                                    zipOutputStream.closeEntry();
                                }
                            }
                        });
            case MERGE:// 机构合并
                List<Integer> mergeRows = new ArrayList<>();
                int headerRow = 6;
                AtomicReference<BigDecimal> countSum = new AtomicReference<>(BigDecimal.ZERO);
                AtomicReference<BigDecimal> feePriceSum = new AtomicReference<>(BigDecimal.ZERO);
                AtomicReference<BigDecimal> totalFeePriceSum = new AtomicReference<>(BigDecimal.ZERO);
                AtomicReference<BigDecimal> payAmountSum = new AtomicReference<>(BigDecimal.ZERO);
                List<HspOrgSendDoctorStatisticsItemDto> outOrgStatisticsItems = new ArrayList<>();
                // 只导出包含数据的机构
                target.getOrgList().stream().filter(org -> CollectionUtils.isNotEmpty(org.getItemList())).forEach(org -> {
                    // 机构下的所有数据
                    outOrgStatisticsItems.addAll(org.getItemList());
                    mergeRows.add(headerRow + outOrgStatisticsItems.size());
                    // 添加小计行
                    HspOrgSendDoctorStatisticsItemDto summaryDto = new HspOrgSendDoctorStatisticsItemDto();
                    summaryDto.setHspOrgName("客户小计");
                    summaryDto.setCount(org.getCountSum());
                    summaryDto.setFeePrice(org.getFeePriceSum());
                    summaryDto.setTotalFeePrice(org.getTotalFeePriceSum());
                    summaryDto.setPayAmount(org.getPayAmountSum());
                    outOrgStatisticsItems.add(summaryDto);
                    countSum.set(countSum.get().add(BigDecimal.valueOf(org.getCountSum())));
                    feePriceSum.set(feePriceSum.get().add(org.getFeePriceSum()));
                    totalFeePriceSum.set(totalFeePriceSum.get().add(org.getTotalFeePriceSum()));
                    payAmountSum.set(payAmountSum.get().add(org.getPayAmountSum()));
                });
                target.setItemList(outOrgStatisticsItems);
                target.setCustomerName("");
                target.setStartDate(target.getOrgList().get(0).getStartDate());
                target.setEndDate(target.getOrgList().get(0).getEndDate());
                target.setCountSum(countSum.get().intValue());
                target.setFeePriceSum(feePriceSum.get());
                target.setTotalFeePriceSum(totalFeePriceSum.get());
                target.setPayAmountSum(payAmountSum.get());
                return bySendDoctorExportMerge(target, new DetectionMergeStrategy(mergeRows, null, 10));
            default:
                // DO NOTHING
        }

        return null;
    }

    /**
     * 机构送检医生--统计 异步
     * @since 1.1.0.7 不再使用模板进行导出，直接画Excel
     */
    @PostMapping("/hsp-org/by-send-doctor-export-async")
    public ResponseEntity<ExportResultVo> hspOrgBySendDoctorExportAsync(@RequestBody HspOrgSendDoctorStatisticsRequestVo vo) throws Exception {
        Assert.notNull(vo.getSource(), "页面来源不能为空");

        // 将查询条件 转换为es查询条件
        SampleEsQuery dto = getSampleEsQueryFromHspOrgSendDoctorStatisticsRequestVo(vo);

        String exportFileName = "";
        String exportFileType = "";
        {
            HspOrgSendDoctorStatisticsResponseDto target = new HspOrgSendDoctorStatisticsResponseDto();
            target.setStartDate(FinancialManagementServiceImpl.getDateRange(dto).getStartDate());
            target.setEndDate(FinancialManagementServiceImpl.getDateRange(dto).getEndDate());
            switch (DisplayOrgType.getByType(vo.getDisplayOrgType())) {
                // 单个机构
                case SINGLE:
                    // 导出zip文件名
                    exportFileName = String.format("%s至%s机构送检医生统计.zip",
                            target.getStartDate().substring(0, 10),
                            target.getEndDate().substring(0, 10));
                    exportFileType = ExportFileType.ZIP.getValue();
                    break;
                // 机构合并
                case MERGE:
                    exportFileName = String.format("%s至%s机构送检医生统计合并导出.xlsx",
                            target.getStartDate().substring(0, 10), target.getEndDate().substring(0, 10));
                    exportFileType = ExportFileType.XLSX.getValue();
                    break;
                default:
                    // DO NOTHING
            }
        }

        LoginUserHandler.User user = LoginUserHandler.get();
        final ExportRecordDto exportRecordDto = new ExportRecordDto();
        exportRecordDto.setFileName(exportFileName);
        exportRecordDto.setFileType(exportFileType);
        exportRecordDto.setStatus(ExportStatus.RUNNING.getCode());
        exportRecordDto.setSource(vo.getSource());

        exportService.submitExportTask(exportRecordDto, () -> {
            LoginUserHandler.set(user);

            StopWatch stopWatch = new StopWatch();
            stopWatch.start("查询机构送检医生数据");
            // 查询数据
            HspOrgSendDoctorStatisticsResponseDto target = this.hspOrgBySendDoctor(vo);
            stopWatch.stop();
            log.info("查询机构送检医生数据 耗时 {} s", stopWatch.getTotalTimeSeconds());

            final int headerRowCount = 3;
            try {
                switch (DisplayOrgType.getByType(vo.getDisplayOrgType())) {
                    case SINGLE:// 单个机构
                        if (CollectionUtils.isEmpty(target.getOrgList())) {
                            throw new IllegalArgumentException("没有数据");
                        }
                        /*if (target.getOrgList().size() == 1) {
                            return byPlatformExportCurrentAsync(vo, target.getOrgList().get(0));
                        }*/

                        ByteArrayOutputStream os = new ByteArrayOutputStream();

                        try (ZipOutputStream zipOutputStream = ZipUtil.getZipOutputStream(os, StandardCharsets.UTF_8)) {
                            Set<String> exist = new HashSet<>();
                            int i = 1;

                            for (HspOrgSendDoctorStatisticsResponseDto responseDto : target.getOrgList()) {
                                String filename = String.format("%s%s至%s机构送检医生统计.xlsx",
                                        responseDto.getCustomerName(), responseDto.getStartDate().substring(0, 10), responseDto.getEndDate().substring(0, 10));
                                List<Integer> mergeRows = new ArrayList<>();
                                Map<Integer, List<RowRangeDto>> strategyMap = new HashMap<>();
                                mergeRows.add(0);
                                strategyMap.put(0, List.of(new RowRangeDto(0, HSP_SEND_DOCTOR_HEADS.size() - 1)));
                                mergeRows.add(1);
                                strategyMap.put(1, List.of(new RowRangeDto(0, HSP_SEND_DOCTOR_HEADS.size() - 1)));

                                if (!exist.add(filename)) {
                                    filename = String.format("(%s)", i++) + filename;
                                }
                                // 添加 Excel 文件到 ZIP
                                zipOutputStream.putNextEntry(new ZipEntry(URLDecoder.decode(filename, StandardCharsets.UTF_8)));

                                // 添加总计行
                                if (CollectionUtils.isNotEmpty(responseDto.getItemList())) {
                                    HspOrgSendDoctorStatisticsItemDto summaryDto = new HspOrgSendDoctorStatisticsItemDto();
                                    summaryDto.setHspOrgName("合计");
                                    summaryDto.setCount(responseDto.getCountSum());
                                    summaryDto.setFeePrice(responseDto.getFeePriceSum());
                                    summaryDto.setTotalFeePrice(responseDto.getTotalFeePriceSum());
                                    summaryDto.setPayAmount(responseDto.getPayAmountSum());
                                    // 总计行合并
                                    mergeRows.add(headerRowCount + responseDto.getItemList().size());
                                    strategyMap.put(headerRowCount + responseDto.getItemList().size(), List.of(new RowRangeDto(0, 9)));
                                    responseDto.getItemList().add(summaryDto);
                                }
                                // 公司抬头行合并
                                mergeRows.add(headerRowCount + responseDto.getItemList().size());
                                strategyMap.put(headerRowCount + responseDto.getItemList().size(), List.of(new RowRangeDto(0, HSP_SEND_DOCTOR_HEADS.size() - 1)));

                                CellStyleMergeStrategy mergeStrategy = new CellStyleMergeStrategy(mergeRows, strategyMap, 10, HSP_SEND_DOCTOR_HEADS.size(), headerRowCount + responseDto.getItemList().size());
                                mergeStrategy.addNumberColumnIndex(10, 11, 12, 14);
                                bySendDoctorExportExcel(responseDto, zipOutputStream, mergeStrategy, exportRecordDto);

                                zipOutputStream.closeEntry();
                            }
                        } catch (Exception e) {
                            log.error("导出文件错误", e);
                            exportRecordDto.setEx(e);
                        }

                        if (Objects.isNull(exportRecordDto.getEx())) {
                            String upload = huaweiObsUtils.upload(new ByteArrayInputStream(os.toByteArray()), ExportFileType.ZIP.getMediaType());
                            exportRecordDto.setUrl(upload);
                        }

                        break;
                    case MERGE:// 机构合并
                        List<Integer> mergeRows = new ArrayList<>();
                        Map<Integer, List<RowRangeDto>> strategyMap = new HashMap<>();
                        mergeRows.add(0);
                        strategyMap.put(0, List.of(new RowRangeDto(0, HSP_SEND_DOCTOR_HEADS.size() - 1)));
                        mergeRows.add(1);
                        strategyMap.put(1, List.of(new RowRangeDto(0, HSP_SEND_DOCTOR_HEADS.size() - 1)));

                        AtomicReference<BigDecimal> countSum = new AtomicReference<>(BigDecimal.ZERO);
                        AtomicReference<BigDecimal> feePriceSum = new AtomicReference<>(BigDecimal.ZERO);
                        AtomicReference<BigDecimal> totalFeePriceSum = new AtomicReference<>(BigDecimal.ZERO);
                        AtomicReference<BigDecimal> payAmountSum = new AtomicReference<>(BigDecimal.ZERO);
                        List<HspOrgSendDoctorStatisticsItemDto> outOrgStatisticsItems = new ArrayList<>();
                        // 只导出包含数据的机构
                        target.getOrgList().stream().filter(org -> CollectionUtils.isNotEmpty(org.getItemList())).forEach(org -> {
                            // 机构下的所有数据
                            outOrgStatisticsItems.addAll(org.getItemList());
                            mergeRows.add(headerRowCount + outOrgStatisticsItems.size());
                            // 添加小计行
                            HspOrgSendDoctorStatisticsItemDto summaryDto = new HspOrgSendDoctorStatisticsItemDto();
                            summaryDto.setHspOrgName("客户小计");
                            summaryDto.setCount(org.getCountSum());
                            summaryDto.setFeePrice(org.getFeePriceSum());
                            summaryDto.setTotalFeePrice(org.getTotalFeePriceSum());
                            summaryDto.setPayAmount(org.getPayAmountSum());
                            outOrgStatisticsItems.add(summaryDto);
                            countSum.set(countSum.get().add(BigDecimal.valueOf(org.getCountSum())));
                            feePriceSum.set(feePriceSum.get().add(org.getFeePriceSum()));
                            totalFeePriceSum.set(totalFeePriceSum.get().add(org.getTotalFeePriceSum()));
                            payAmountSum.set(payAmountSum.get().add(org.getPayAmountSum()));
                        });
                        // 添加总计
                        HspOrgSendDoctorStatisticsItemDto summaryDto = new HspOrgSendDoctorStatisticsItemDto();
                        summaryDto.setHspOrgName("合计");
                        summaryDto.setCount(countSum.get().intValue());
                        summaryDto.setFeePrice(feePriceSum.get());
                        summaryDto.setTotalFeePrice(totalFeePriceSum.get());
                        summaryDto.setPayAmount(payAmountSum.get());
                        // 总计行合并
                        mergeRows.add(headerRowCount + outOrgStatisticsItems.size());
                        strategyMap.put(headerRowCount + outOrgStatisticsItems.size(), List.of(new RowRangeDto(0, 9)));
                        outOrgStatisticsItems.add(summaryDto);
                        // 公司抬头行合并
                        mergeRows.add(headerRowCount + outOrgStatisticsItems.size());
                        strategyMap.put(headerRowCount + outOrgStatisticsItems.size(), List.of(new RowRangeDto(0, HSP_SEND_DOCTOR_HEADS.size() - 1)));

                        target.setItemList(outOrgStatisticsItems);
                        target.setCustomerName("");
                        target.setStartDate(target.getOrgList().get(0).getStartDate());
                        target.setEndDate(target.getOrgList().get(0).getEndDate());
                        target.setCountSum(countSum.get().intValue());
                        target.setFeePriceSum(feePriceSum.get());
                        target.setTotalFeePriceSum(totalFeePriceSum.get());
                        target.setPayAmountSum(payAmountSum.get());

                        ByteArrayOutputStream out = new ByteArrayOutputStream();
                        try {
                            CellStyleMergeStrategy mergeStrategy = new CellStyleMergeStrategy(mergeRows, strategyMap, 10, HSP_SEND_DOCTOR_HEADS.size(), headerRowCount + outOrgStatisticsItems.size());
                            mergeStrategy.addNumberColumnIndex(10, 11, 12, 14);
                            bySendDoctorExportExcel(target, out, mergeStrategy, exportRecordDto);
                        } catch (Exception e) {
                            log.error("导出文件错误", e);
                            exportRecordDto.setEx(e);
                        }

                        if (Objects.isNull(exportRecordDto.getEx())) {
                            String upload = huaweiObsUtils.upload(new ByteArrayInputStream(out.toByteArray()), ExportFileType.XLSX.getMediaType());
                            exportRecordDto.setUrl(upload);
                        }

                        break;
                    default:
                        // DO NOTHING
                }
            } catch (Exception e) {
                log.error("导出文件错误", e);
                exportRecordDto.setEx(e);
            }

            return exportRecordDto;
        });

        return ResponseEntity.ok(new ExportResultVo());
    }

    /**
     * 使用excel模板导出
     * @deprecated
     * @since  1.1.0.7 弃用
     */
    @Deprecated(since = "1.1.0.7", forRemoval = true)
    private static ResponseEntity<StreamingResponseBody> byPlatformExportCurrent(HspOrgSendDoctorStatisticsResponseDto target) {
        // 获取模板文件
        final InputStream templateInputStream;
        try {
            templateInputStream = new ClassPathResource("template/机构送检医生统计模版.xlsx").getInputStream();
        } catch (Exception e) {
            throw new IllegalStateException("模板文件获取失败");
        }

        final String filename = String.format("%s%s至%s机构送检医生统计.xlsx",
                target.getCustomerName(), target.getStartDate().substring(0, 10), target.getEndDate().substring(0, 10));

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode(filename, StandardCharsets.UTF_8)))
                .header(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION, "filename")
                .header("filename", URLEncoder.encode(filename, StandardCharsets.UTF_8))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body((StreamingResponseBody) out -> {
                    try (ExcelWriter excelWriter =
                                 EasyExcelFactory.write(out).autoCloseStream(false).excelType(ExcelTypeEnum.XLSX).withTemplate(templateInputStream).build()) {
                        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                        // 填充 Sheet 的数据
                        WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
                        // 列表数据
                        for (List<HspOrgSendDoctorStatisticsItemDto> itemList : Lists.partition(target.getItemList(), 1000)) {
                            excelWriter.fill(itemList, fillConfig, writeSheet);
                        }
                        // 其他数据
                        excelWriter.fill(target, writeSheet);
                        excelWriter.finish();
                    } catch (Exception e) {
                        log.error("下载模板错误", e);
                        throw new LimsException(e.getMessage(), e);
                    }
                });
    }


    /**
     * 导出，机构合并
     *
     * @param target
     * @param mergeStrategy
     * @return
     */
    @Deprecated
    private static ResponseEntity<StreamingResponseBody> bySendDoctorExportMerge(
            HspOrgSendDoctorStatisticsResponseDto target, DetectionMergeStrategy mergeStrategy) {
        // 获取模板文件
        final InputStream templateInputStream;
        try {
            templateInputStream = new ClassPathResource("template/机构送检医生统计模版.xlsx").getInputStream();
        } catch (Exception e) {
            throw new IllegalStateException("模板文件获取失败");
        }

        final String filename = String.format("%s%s至%s机构送检医生统计合并导出.xlsx",
                target.getCustomerName(), target.getStartDate().substring(0, 10), target.getEndDate().substring(0, 10));
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode(filename, StandardCharsets.UTF_8)))
                .header(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION, "filename")
                .header("filename", URLEncoder.encode(filename, StandardCharsets.UTF_8))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body((StreamingResponseBody) out -> {
                    try (ExcelWriter excelWriter =
                                 EasyExcelFactory.write(out).autoCloseStream(false)
                                         .excelType(ExcelTypeEnum.XLSX).withTemplate(templateInputStream)
                                         .registerWriteHandler(mergeStrategy).build()) {
                        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                        // 填充 Sheet 的数据
                        WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
                        // 列表数据 - 每次写1000条
                        for (List<HspOrgSendDoctorStatisticsItemDto> itemList : Lists.partition(target.getItemList(), 1000)) {
                            excelWriter.fill(itemList, fillConfig, writeSheet);
                        }
                        // 其他数据
                        excelWriter.fill(target, writeSheet);
                        excelWriter.finish();
                    } catch (Exception e) {
                        log.error("下载模板错误", e);
                        throw new LimsException(e.getMessage(), e);
                    }
                });
    }

    @Deprecated
    private ResponseEntity<ExportResultVo> byPlatformExportCurrentAsync(HspOrgSendDoctorStatisticsRequestVo vo, HspOrgSendDoctorStatisticsResponseDto target) {
        // 获取模板文件
        final InputStream templateInputStream;
        try {
            templateInputStream = new ClassPathResource("template/机构送检医生统计模版.xlsx").getInputStream();
        } catch (Exception e) {
            throw new IllegalStateException("模板文件获取失败");
        }

        final String filename = String.format("%s%s至%s机构送检医生统计.xlsx",
                target.getCustomerName(), target.getStartDate().substring(0, 10), target.getEndDate().substring(0, 10));

        LoginUserHandler.User user = LoginUserHandler.get();
        final ExportRecordDto exportRecordDto = new ExportRecordDto();
        exportRecordDto.setFileName(filename);
        exportRecordDto.setFileType(ExportFileType.XLSX.getValue());
        exportRecordDto.setStatus(ExportStatus.RUNNING.getCode());
        exportRecordDto.setSource(vo.getSource());

        exportService.submitExportTask(exportRecordDto, () -> {
            LoginUserHandler.set(user);
            ByteArrayOutputStream out = new ByteArrayOutputStream();

            try (ExcelWriter excelWriter =
                         EasyExcelFactory.write(out).autoCloseStream(false).excelType(ExcelTypeEnum.XLSX).withTemplate(templateInputStream).build()) {
                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                // 填充 Sheet 的数据
                WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
                // 列表数据
                for (List<HspOrgSendDoctorStatisticsItemDto> itemList : Lists.partition(target.getItemList(), 1000)) {
                    excelWriter.fill(itemList, fillConfig, writeSheet);
                }
                // 其他数据
                excelWriter.fill(target, writeSheet);
                excelWriter.finish();
            } catch (Exception e) {
                log.error("导出文件错误", e);
                exportRecordDto.setEx(e);
            }

            if (Objects.isNull(exportRecordDto.getEx())) {
                String upload = huaweiObsUtils.upload(new ByteArrayInputStream(out.toByteArray()), ExportFileType.XLSX.getMediaType());
                exportRecordDto.setUrl(upload);
            }

            return exportRecordDto;
        });

        return ResponseEntity.ok(new ExportResultVo());
    }
    @Deprecated
    private ResponseEntity<ExportResultVo> bySendDoctorExportMergeAsync(
            HspOrgSendDoctorStatisticsRequestVo vo, HspOrgSendDoctorStatisticsResponseDto target, DetectionMergeStrategy mergeStrategy) {
        // 获取模板文件
        final InputStream templateInputStream;
        try {
            templateInputStream = new ClassPathResource("template/机构送检医生统计模版.xlsx").getInputStream();
        } catch (Exception e) {
            throw new IllegalStateException("模板文件获取失败");
        }

        final String filename = String.format("%s%s至%s机构送检医生统计合并导出.xlsx",
                target.getCustomerName(), target.getStartDate().substring(0, 10), target.getEndDate().substring(0, 10));

        LoginUserHandler.User user = LoginUserHandler.get();
        final ExportRecordDto exportRecordDto = new ExportRecordDto();
        exportRecordDto.setFileName(filename);
        exportRecordDto.setFileType(ExportFileType.XLSX.getValue());
        exportRecordDto.setStatus(ExportStatus.RUNNING.getCode());
        exportRecordDto.setSource(vo.getSource());

        exportService.submitExportTask(exportRecordDto, () -> {
            LoginUserHandler.set(user);
            ByteArrayOutputStream out = new ByteArrayOutputStream();

            try (ExcelWriter excelWriter =
                         EasyExcelFactory.write(out).autoCloseStream(false)
                                 .excelType(ExcelTypeEnum.XLSX).withTemplate(templateInputStream)
                                 .registerWriteHandler(mergeStrategy).build()) {
                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                // 填充 Sheet 的数据
                WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
                // 列表数据
                for (List<HspOrgSendDoctorStatisticsItemDto> itemList : Lists.partition(target.getItemList(), 1000)) {
                    excelWriter.fill(itemList, fillConfig, writeSheet);
                }
                // 其他数据
                excelWriter.fill(target, writeSheet);
                excelWriter.finish();
            } catch (Exception e) {
                log.error("导出文件错误", e);
                exportRecordDto.setEx(e);
            }

            if (Objects.isNull(exportRecordDto.getEx())) {
                String upload = huaweiObsUtils.upload(new ByteArrayInputStream(out.toByteArray()), ExportFileType.XLSX.getMediaType());
                exportRecordDto.setUrl(upload);
            }

            return exportRecordDto;
        });

        return ResponseEntity.ok(new ExportResultVo());
    }

    // 机构送检医生统计 excel表头
    private static final List<FieldData> HSP_SEND_DOCTOR_HEADS = new ArrayList<>();
    static {
        HSP_SEND_DOCTOR_HEADS.add(new FieldData("hspOrgName", "送检机构"));
        HSP_SEND_DOCTOR_HEADS.add(new FieldData("sendDate", "送检时间"));
        HSP_SEND_DOCTOR_HEADS.add(new FieldData("patientName", "姓名"));
        HSP_SEND_DOCTOR_HEADS.add(new FieldData("patientSex", "性别"));
        HSP_SEND_DOCTOR_HEADS.add(new FieldData("patientAge", "年龄"));
        HSP_SEND_DOCTOR_HEADS.add(new FieldData("dept", "科室"));
        HSP_SEND_DOCTOR_HEADS.add(new FieldData("sendDoctorName", "医生"));
        HSP_SEND_DOCTOR_HEADS.add(new FieldData("applyTypeName", "患者类别"));
        HSP_SEND_DOCTOR_HEADS.add(new FieldData("testItemCode", "项目编码"));
        HSP_SEND_DOCTOR_HEADS.add(new FieldData("testItem", "项目名称"));
        HSP_SEND_DOCTOR_HEADS.add(new FieldData("count", "数量"));
        HSP_SEND_DOCTOR_HEADS.add(new FieldData("feePrice", "标准单价"));
        HSP_SEND_DOCTOR_HEADS.add(new FieldData("totalFeePrice", "合计金额"));
        HSP_SEND_DOCTOR_HEADS.add(new FieldData("discount", "折扣"));
        HSP_SEND_DOCTOR_HEADS.add(new FieldData("payAmount", "结算金额"));
        HSP_SEND_DOCTOR_HEADS.add(new FieldData("finalCheckDate", "审核时间"));
        HSP_SEND_DOCTOR_HEADS.add(new FieldData("patientVisitCard", "病历号"));
        HSP_SEND_DOCTOR_HEADS.add(new FieldData("outBarcode", "外部条码号"));
    }
    // 售项目收入查询--汇总 excel表头
    private static final List<FieldData> TESTITEM_INCOME_SUMMARY_HEADERS = new ArrayList<>();
    static {
        TESTITEM_INCOME_SUMMARY_HEADERS.add(new FieldData("customerName", "送检机构"));
        TESTITEM_INCOME_SUMMARY_HEADERS.add(new FieldData("applyTypeName", "检验类型"));
        TESTITEM_INCOME_SUMMARY_HEADERS.add(new FieldData("testItemCode", "项目代码"));
        TESTITEM_INCOME_SUMMARY_HEADERS.add(new FieldData("testItemName", "项目名称"));
        TESTITEM_INCOME_SUMMARY_HEADERS.add(new FieldData("feeCode", "收费代码"));
        TESTITEM_INCOME_SUMMARY_HEADERS.add(new FieldData("feeName", "收费名称"));
        TESTITEM_INCOME_SUMMARY_HEADERS.add(new FieldData("count", "数量"));
        TESTITEM_INCOME_SUMMARY_HEADERS.add(new FieldData("feePrice", "标准收费"));
        TESTITEM_INCOME_SUMMARY_HEADERS.add(new FieldData("feePriceSum", "合计金额"));
        TESTITEM_INCOME_SUMMARY_HEADERS.add(new FieldData("discount", "折扣"));
        TESTITEM_INCOME_SUMMARY_HEADERS.add(new FieldData("payAmount", "结算金额"));
    }
    // 售项目收入查询--明细 excel表头
    private static final List<FieldData> TESTITEM_INCOME_DETAIL_HEADS = new ArrayList<>();
    static {
        TESTITEM_INCOME_DETAIL_HEADS.add(new FieldData("customerName", "送检机构"));
        TESTITEM_INCOME_DETAIL_HEADS.add(new FieldData("sendDate", "送检时间"));
        TESTITEM_INCOME_DETAIL_HEADS.add(new FieldData("patientVisitCard", "病历号"));
        TESTITEM_INCOME_DETAIL_HEADS.add(new FieldData("patientName", "姓名"));
        TESTITEM_INCOME_DETAIL_HEADS.add(new FieldData("sendDoctorName", "送检医生"));
        TESTITEM_INCOME_DETAIL_HEADS.add(new FieldData("applyTypeName", "就诊类型"));
        TESTITEM_INCOME_DETAIL_HEADS.add(new FieldData("dept", "科室"));
        TESTITEM_INCOME_DETAIL_HEADS.add(new FieldData("remark", "备注"));
        TESTITEM_INCOME_DETAIL_HEADS.add(new FieldData("testItems", "项目名称"));
        TESTITEM_INCOME_DETAIL_HEADS.add(new FieldData("outTestItemCode", "外部项目编码"));
        TESTITEM_INCOME_DETAIL_HEADS.add(new FieldData("outTestItemName", "外部项目名称"));
        TESTITEM_INCOME_DETAIL_HEADS.add(new FieldData("feeCode", "收费代码"));
        TESTITEM_INCOME_DETAIL_HEADS.add(new FieldData("feeName", "收费名称"));
        TESTITEM_INCOME_DETAIL_HEADS.add(new FieldData("count", "数量"));
        TESTITEM_INCOME_DETAIL_HEADS.add(new FieldData("discountLabel", "折扣"));
        TESTITEM_INCOME_DETAIL_HEADS.add(new FieldData("feePrice", "折前总额"));
        TESTITEM_INCOME_DETAIL_HEADS.add(new FieldData("discountFeePriceLabel", "折后总额"));

    }

    private void bySendDoctorExportExcel(HspOrgSendDoctorStatisticsResponseDto target, OutputStream out, DetectionMergeStrategy mergeStrategy, ExportRecordDto exportRecordDto) {
        try (ExcelWriter excelWriter =
                     EasyExcelFactory.write(out).autoCloseStream(false)
                             .excelType(ExcelTypeEnum.XLSX)
                             .registerWriteHandler(mergeStrategy).build()) {

            List<List<Object>> dataList = Lists.newArrayList();
            List<List<String>> headerList = Lists.newArrayList();

            // 设置表头
            List<String> heads = HSP_SEND_DOCTOR_HEADS.stream().map(FieldData::getFieldName).collect(Collectors.toList());
            for (String item : heads) {
                headerList.add(List.of(item));
            }

            // 设置表格title
            dataList.add(List.of(String.format("%s%s至%s科室医生统计表", target.getCustomerName(), target.getStartDate(), target.getEndDate())));
            // 设置表格副title
            if (StringUtils.isNotBlank(target.getCustomerName())) {
                dataList.add(List.of(String.format("客户名称：%s   日期范围：%s至%s", target.getCustomerName(), target.getStartDate(), target.getEndDate())));
            } else {
                dataList.add(List.of(String.format("日期范围：%s至%s", target.getStartDate(), target.getEndDate())));
            }

            // 设置表格数据
            fillExcelContent(dataList, heads, HSP_SEND_DOCTOR_HEADS, target.getItemList().stream().map(e -> (Object) e).collect(Collectors.toList()));

            // 设置底部内容
            dataList.add(List.of(companyContentRow));

            // 获取sheet对象
            WriteSheet sheet0 = EasyExcelFactory.writerSheet(0, "Sheet1")/*.head(headerList).needHead(Boolean.TRUE)*/.build();
            excelWriter.write(dataList, sheet0);

            excelWriter.finish();
        } catch (Exception e) {
            log.error("导出文件错误", e);
            exportRecordDto.setEx(e);
        }
    }

    private void fillExcelContent(List<List<Object>> dataList, List<String> heads, List<FieldData> headsDataMap, List<Object> itemList) {
        // 表头
        dataList.add(heads.stream().map(head -> (Object) head).collect(Collectors.toList()));
        // 表格内容
        itemList.forEach(item -> {
            List<Object> content = Lists.newArrayListWithCapacity(heads.size());

            Dict dict = Dict.parse(item);
            headsDataMap.forEach(fieldData -> content.add(dict.getStr(fieldData.getField())));

            dataList.add(content);
        });
    }

    /**
     * 机构送检医生--统计
     */
    @PostMapping("/hsp-org/send-doctor-statistics-export")
    public ResponseEntity<?> hspOrgSendDoctorStatisticsExport(@RequestBody HspOrgSendDoctorStatisticsRequestVo vo) {
        // 将查询条件 转换为es查询条件
        SampleEsQuery dto = getSampleEsQueryFromHspOrgSendDoctorStatisticsRequestVo(vo);

        // 所有es 结构数据
        List<BaseSampleEsModelDto> baseSampleEsModelDtos = elasticSearchSampleService.selectSamples(dto);

        // 展示数据结构
        List<HspOrgSendDoctorStatisticsResponseDto> targetList =
                financialManagementService.getHspOrgSendDoctorStatisticsResponseDto(dto, vo, baseSampleEsModelDtos);

        // 获取模板文件
        final InputStream templateInputStream;
        try {

            InputStream inputStream = new ClassPathResource("template/机构送检医生统计模版.xlsx").getInputStream();
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            int templateSheetIndex = 0;
            for (int i = 1; i < targetList.size(); i++) {
                String sheetName = String.format("%s%s", "Sheet", i + 1);
                workbook.cloneSheet(templateSheetIndex, sheetName);
            }

            List<String> temp = Lists.newArrayList();
            for (int i = 0; i < targetList.size(); i++) {
                String customerName = targetList.get(i).getCustomerName();
                if (StringUtils.isNotBlank(customerName)) {
                    if (temp.contains(customerName)) {
                        customerName = customerName + (i + 1);
                    }
                    // 如果字符超过 excel sheet 限制截取下
                    if (customerName.length() > 31) {
                        customerName = customerName.substring(0, 31);
                    }
                    if (!temp.contains(customerName)) {
                        workbook.setSheetName(i, customerName);
                        temp.add(customerName);
                    }
                }
            }

            workbook.write(out);
            byte[] bArray = out.toByteArray();
            templateInputStream = new ByteArrayInputStream(bArray);
        } catch (Exception e) {
            throw new IllegalStateException("模板文件获取失败", e);
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        try (ExcelWriter excelWriter =
                     EasyExcelFactory.write(out).excelType(ExcelTypeEnum.XLSX).withTemplate(templateInputStream).build()) {

            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            // 未查到任何数据 为避免导出包含变量的模版 传入空数据
            if (CollectionUtils.isEmpty(targetList)) {
                HspOrgSendDoctorStatisticsResponseDto temp = new HspOrgSendDoctorStatisticsResponseDto();
                targetList.add(temp);
            }

            for (int i = 0; i < targetList.size(); i++) {
                HspOrgSendDoctorStatisticsResponseDto target = targetList.get(i);

                // 填充第 i+1 个 Sheet 的数据
                WriteSheet writeSheet = EasyExcelFactory.writerSheet(i).build();

                // 列表数据
                for (List<HspOrgSendDoctorStatisticsItemDto> itemList : Lists.partition(target.getItemList(), 1000)) {
                    excelWriter.fill(itemList, fillConfig, writeSheet);
                }

                // 其他数据
                excelWriter.fill(target, writeSheet);

            }
            excelWriter.finish();
        } catch (Exception e) {
            log.error("下载模板错误", e);
            throw new IllegalStateException(e.getMessage(), e);
        }

        byte[] data = out.toByteArray();

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("机构送检医生统计.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body(data);
    }

    /**
     * 销售项目收入查询 参数 转为 es 查询 结构
     */
    private SampleEsQuery getSampleEsQueryFromTestItemIncomeSummaryRequestVo(TestItemIncomeRequestVo vo) {

        // 检查 送检时间 与财务月份
        checkDateRangRequest(vo.getFinancialMonth(), vo.getBeginDeliveryDate(), vo.getEndDeliveryDate());

        // 将查询条件 转换为es查询条件
        SampleEsQuery dto = new SampleEsQuery();
        dto.setPageSize(esConfig.getPageSize());
        dto.setPageNo(NumberUtils.INTEGER_ONE);
        // 1.0.6.6需求改为不管该样本是否已经审核都进行查询
        //dto.setIsAudit(YesOrNoEnum.YES.getCode());

        // 补充查询时间范围
        sampleEsQueryAddDateRang(dto, vo.getFinancialMonth(), vo.getBeginDeliveryDate(), vo.getEndDeliveryDate());

        if (CollectionUtils.isNotEmpty(vo.getHspOrgIds())) {
            dto.setHspOrgIds(vo.getHspOrgIds());
        }

        return dto;
    }

    /**
     * 销售项目收入查询明细 参数 转为 es 查询 结构
     */
    private SampleEsQuery getSampleEsQueryFromTestItemIncomeDetailsRequestVo(TestItemIncomeRequestVo vo) {
        SampleEsQuery dto = getSampleEsQueryFromTestItemIncomeSummaryRequestVo(vo);

        if (CollectionUtils.isNotEmpty(vo.getTestItemIds())) {
            Set<Long> filter = vo.getTestItemIds().stream().filter(Objects::nonNull).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(filter)) {
                dto.setTestItemIds(vo.getTestItemIds());
            }
        }

        // 是否选择了微生物
        if (Objects.equals(vo.getItemTypeCode(), ItemTypeEnum.MICROBIOLOGY.name())) {
            dto.setItemTypes(Set.of(ItemTypeEnum.MICROBIOLOGY.name()));
        }

        return dto;
    }

    /**
     * 机构送检医生统计 参数 转为 es 查询 结构
     */
    private SampleEsQuery
    getSampleEsQueryFromHspOrgSendDoctorStatisticsRequestVo(HspOrgSendDoctorStatisticsRequestVo vo) {
        // 检查 送检时间 与财务月份
        checkDateRangRequest(vo.getFinancialMonth(), vo.getBeginDeliveryDate(), vo.getEndDeliveryDate());

        SampleEsQuery dto = new SampleEsQuery();
        dto.setPageSize(esConfig.getPageSize());
        dto.setPageNo(NumberUtils.INTEGER_ONE);
        // 1.0.6.6需求改为不管该样本是否已经审核都进行查询
        //dto.setIsAudit(YesOrNoEnum.YES.getCode());

        // 补充查询时间范围
        sampleEsQueryAddDateRang(dto, vo.getFinancialMonth(), vo.getBeginDeliveryDate(), vo.getEndDeliveryDate());

        if (CollectionUtils.isNotEmpty(vo.getHspOrgIds())) {
            dto.setHspOrgIds(vo.getHspOrgIds());
        }

        if (Objects.nonNull(vo.getGroupId())) {
            dto.setGroupIds(Set.of(vo.getGroupId()));
        }
        if (CollectionUtils.isNotEmpty(vo.getGroupIds())) {
            dto.setGroupIds(vo.getGroupIds());
        }

        return dto;
    }

    /**
     * 添加查询 时间 范围
     *
     * @param dto               查询参数
     * @param financialMonth    财务月份
     * @param beginDeliveryDate 送检开始时间
     * @param endDeliveryDate   送检结束时间
     */
    private void sampleEsQueryAddDateRang(SampleEsQuery dto, String financialMonth, Date beginDeliveryDate,
                                          Date endDeliveryDate) {
        if (StringUtils.isNotBlank(financialMonth)) {
            YearMonth yearMonth = YearMonth.parse(financialMonth);
            LocalDateTime monthStartDateTime = yearMonth.atDay(1).atStartOfDay();
            LocalDateTime monthEndDateTime = yearMonth.atEndOfMonth().atTime(23, 59, 59);
            Date monthStartDate = Date.from(monthStartDateTime.atZone(ZoneId.systemDefault()).toInstant());
            Date monthEndDate = Date.from(monthEndDateTime.atZone(ZoneId.systemDefault()).toInstant());
            // 传入了财务月份 以终审时间查看
            // dto.setStartFinalCheckDate(monthStartDate);
            // dto.setEndFinalCheckDate(monthEndDate);

            dto.setStartFinalCheckOrCreateDate(monthStartDate);
            dto.setEndFinalCheckOrCreateDate(monthEndDate);
        }
        if (Objects.nonNull(beginDeliveryDate) && Objects.nonNull(endDeliveryDate)) {
            // 送检时间的就是样本创建时间
            dto.setStartCreateDate(beginDeliveryDate);
            dto.setEndCreateDate(endDeliveryDate);
//            dto.setStartFinalCheckOrCreateDate(beginDeliveryDate);
//            dto.setEndFinalCheckOrCreateDate(endDeliveryDate);

        }
    }

    /**
     * 检查 送检时间 与财务月份
     *
     * @param financialMonth    财务月份
     * @param beginDeliveryDate 送检时间开始
     * @param endDeliveryDate   送检时间结束
     */
    private void checkDateRangRequest(String financialMonth, Date beginDeliveryDate, Date endDeliveryDate) {
        if (StringUtils.isBlank(financialMonth)
                && (Objects.isNull(beginDeliveryDate) || Objects.isNull(endDeliveryDate))) {
            throw new IllegalStateException("送检时间与财务月份必须传一个");
        }
        if (StringUtils.isNotBlank(financialMonth) && Objects.nonNull(beginDeliveryDate)
                && Objects.nonNull(endDeliveryDate)) {
            throw new IllegalStateException("送检时间与财务月份只能传一个");
        }
        if (StringUtils.isNotBlank(financialMonth) && !isValidMonthFormat(financialMonth)) {
            throw new IllegalStateException("财务月份格式错误,请传入[yyyy-MM]格式");
        }
    }

    private HspOrgBySendDoctorService getHspOrgBySendDoctorService(){
        log.info("送检医生统计 version： {}", bySendDoctorVersion);

        final HspOrgBySendDoctorService hspOrgBySendDoctorService = HSPORG_BY_SEND_DOCTOR_SERVICE_MAP.get(bySendDoctorVersion);
        if(Objects.isNull(hspOrgBySendDoctorService)){
            throw new IllegalArgumentException("NACOS 配置错误");
        }
        return hspOrgBySendDoctorService;
    }
    private IncomeSummaryService getIncomeSummaryService(){
        log.info("销售项目统计查询 version： {}", incomeSummaryVersion);

        final IncomeSummaryService incomeSummaryService = INCOME_SUMMARY_SERVICE_MAP.get(incomeSummaryVersion);
        if(Objects.isNull(incomeSummaryService)){
            throw new IllegalArgumentException("NACOS 配置错误");
        }
        return incomeSummaryService;
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        // 送检机构医生查询
        final Map<String, HspOrgBySendDoctorService> senDoctorServiceList = applicationContext.getBeansOfType(HspOrgBySendDoctorService.class);
        senDoctorServiceList.values().forEach(bean -> {
            final String beanName = bean.version();
            HSPORG_BY_SEND_DOCTOR_SERVICE_MAP.put(beanName, bean);
        });

        // 销售项目统计查询
        final Map<String, IncomeSummaryService> incomeSummaryServiceList = applicationContext.getBeansOfType(IncomeSummaryService.class);
        incomeSummaryServiceList.values().forEach(bean -> {
            final String beanName = bean.version();
            INCOME_SUMMARY_SERVICE_MAP.put(beanName, bean);
        });

    }

    static class CellStyleMergeStrategy extends DetectionMergeStrategy {
        private final List<Integer> numberColumnIndexList = new ArrayList<>();

        private final Integer totalColumnCount;
        private final Integer lastRowIndex;

        private static Map<String, BigDecimal> NUMBER_CACHE = new HashMap<>(1024);

        public CellStyleMergeStrategy(List<Integer> mergeRows,
                                      Map<Integer, List<RowRangeDto>> strategyMap,
                                      Integer col, Integer totalColumnCount,
                                      Integer lastRowIndex) {
            super(mergeRows, strategyMap, col);
            this.totalColumnCount = totalColumnCount;
            this.lastRowIndex = lastRowIndex;
        }

        public void addNumberColumnIndex(Integer... columnIndex) {
            this.numberColumnIndexList.addAll(List.of(columnIndex));
        }

        public void setColumnWidth(CellWriteHandlerContext context) {
            Cell cell = context.getCell();
            Sheet sheet = cell.getSheet();
            sheet.setColumnWidth(0, 15 * 256);
            sheet.setColumnWidth(1, 25 * 256);
            sheet.setColumnWidth(2, 20 * 256);
            for (int i = 3; i < totalColumnCount; i++) {
                sheet.setColumnWidth(i, 15 * 256);
            }
            Row row = cell.getRow();
            if (cell.getRowIndex() < 3) {
                row.setHeightInPoints(30);
                if (cell.getRowIndex() > 0) {
                    row.setHeightInPoints(20);
                }
            } else {
                row.setHeightInPoints(15);
            }
            if (cell.getRowIndex() == lastRowIndex) {
                row.setHeightInPoints(240);
            }
        }

        @Override
        public void afterCellDispose(CellWriteHandlerContext context) {
            // 当前事件会在 数据设置到poi的cell里面才会回调
            // 判断不是头的情况 如果是fill 的情况 这里会==null 所以用not true
            if (BooleanUtils.isNotTrue(context.getHead())) {
                setColumnWidth(context);

                Cell cell = context.getCell();
                if (cell.getRowIndex() < 3) {
                    // 合并单元格
                    merge(context.getWriteSheetHolder().getSheet(), cell, context.getHeadData(), context.getRelativeRowIndex());

                    // 这里要把 WriteCellData的样式清空， 不然后面还有一个拦截器 FillStyleCellWriteHandler 默认会将 WriteCellStyle 设置到
                    // cell里面去 会导致自己设置的不一样
                    context.getFirstCellData().setWriteCellStyle(rowStyleMap.get(cell.getRowIndex()));

                    return;
                } else if (cell.getRowIndex() == lastRowIndex) {
                    merge(context.getWriteSheetHolder().getSheet(), cell, context.getHeadData(), context.getRelativeRowIndex());
                    context.getFirstCellData().setWriteCellStyle(lastRowCellStyle);

                    return;
                } else if (cell.getRowIndex() == lastRowIndex - 1) {
                    merge(context.getWriteSheetHolder().getSheet(), cell, context.getHeadData(), context.getRelativeRowIndex());
                    context.getFirstCellData().setWriteCellStyle(totalRowCellStyle);

                    return;
                }

                // 这里要把 WriteCellData的样式清空， 不然后面还有一个拦截器 FillStyleCellWriteHandler 默认会将 WriteCellStyle 设置到
                // cell里面去 会导致自己设置的不一样
                context.getFirstCellData().setWriteCellStyle(contentWriteCellStyle);
            }

            if (context.getHead()) {
                return;
            }

            super.afterCellDispose(context);
        }

        @Override
        public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, WriteCellData<?> cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
            String stringValue = cellData.getStringValue();
            Integer columnIndex = cell.getColumnIndex();
            // 数字
            if (numberColumnIndexList.contains(columnIndex) && NumberUtils.isCreatable(stringValue)) {
                cellData.setNumberValue(NUMBER_CACHE.compute(stringValue, (k, v) -> new BigDecimal(stringValue)));
                cellData.setType(CellDataTypeEnum.NUMBER);
                cellData.setWriteCellStyle(numberWriteCellStyle);
            }
        }

        // 数字单元格格式设置
        static WriteCellStyle numberWriteCellStyle = new WriteCellStyle();
        {
            DataFormatData dataFormatData = new DataFormatData();
            dataFormatData.setIndex(HSSFDataFormat.getBuiltinFormat("0.00"));
            numberWriteCellStyle.setDataFormatData(dataFormatData);
        }

        static Map<Integer, WriteCellStyle> rowStyleMap = new HashMap<>();
        {
            rowStyleMap.put(0, firstRowCellStyle);
            rowStyleMap.put(1, secondRowCellStyle);
            rowStyleMap.put(2, thirdRowCellStyle);
        }

        // 首行格式（标题行）
        static WriteCellStyle firstRowCellStyle = new WriteCellStyle();
        {
            // 字体大小
            WriteFont contentWriteFont = new WriteFont();
            contentWriteFont.setFontHeightInPoints((short) 16);
            contentWriteFont.setBold(true);
            firstRowCellStyle.setWriteFont(contentWriteFont);

            // 水平居中
            firstRowCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            // 垂直居中
            firstRowCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 设置自动换行，前提内容中需要加「\n」才有效
            firstRowCellStyle.setWrapped(true);
        }

        // 时段信息行
        static WriteCellStyle secondRowCellStyle = new WriteCellStyle();
        {
            // 字体大小
            WriteFont contentWriteFont = new WriteFont();
            contentWriteFont.setFontHeightInPoints((short) 12);
            contentWriteFont.setBold(true);
            secondRowCellStyle.setWriteFont(contentWriteFont);

            // 水平居中
            secondRowCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
            // 垂直居中
            secondRowCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 设置自动换行，前提内容中需要加「\n」才有效
            secondRowCellStyle.setWrapped(true);
        }

        // 表头行格式
        static WriteCellStyle thirdRowCellStyle = new WriteCellStyle();
        {
            // 字体大小
            WriteFont contentWriteFont = new WriteFont();
            contentWriteFont.setFontHeightInPoints((short) 12);
            contentWriteFont.setBold(true);
            thirdRowCellStyle.setWriteFont(contentWriteFont);

            // 水平居中
            thirdRowCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            // 垂直居中
            thirdRowCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 设置自动换行，前提内容中需要加「\n」才有效
            thirdRowCellStyle.setWrapped(true);
        }
        // 最后一行单位内容样式
        static WriteCellStyle lastRowCellStyle = new WriteCellStyle();
        {
            // 水平
            lastRowCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
            // 垂直
            lastRowCellStyle.setVerticalAlignment(VerticalAlignment.TOP);
            // 设置自动换行，前提内容中需要加「\n」才有效
            lastRowCellStyle.setWrapped(true);
        }
        // 总计行样式
        static WriteCellStyle totalRowCellStyle = new WriteCellStyle();
        {
            // 字体大小
            WriteFont contentWriteFont = new WriteFont();
            contentWriteFont.setColor(IndexedColors.BLUE.getIndex());
            contentWriteFont.setFontHeightInPoints((short) 11);
            // contentWriteFont.setBold(true);
            totalRowCellStyle.setWriteFont(contentWriteFont);

            // 水平居中
            totalRowCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            // 垂直居中
            totalRowCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 设置自动换行，前提内容中需要加「\n」才有效
            totalRowCellStyle.setWrapped(true);
        }

        static WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        {
            // 字体大小
            WriteFont contentWriteFont = new WriteFont();
            contentWriteFont.setFontHeightInPoints((short) 11);
            contentWriteCellStyle.setWriteFont(contentWriteFont);
            // 水平
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
            // 垂直
            contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        }

    }
}
