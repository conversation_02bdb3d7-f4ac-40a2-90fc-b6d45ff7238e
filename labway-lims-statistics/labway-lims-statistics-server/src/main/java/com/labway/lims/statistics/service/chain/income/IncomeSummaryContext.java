package com.labway.lims.statistics.service.chain.income;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.statistics.service.chain.byPlateform.PlatformStatisticsContext;
import com.labway.lims.statistics.vo.IncomeSummaryVo;
import com.labway.lims.statistics.vo.TestItemIncomeRequestVo;
import org.apache.commons.chain.Context;

public class IncomeSummaryContext extends PlatformStatisticsContext {

    /**
     * ES查询参数
     */
    public static final String SAMPLE_ES_QUERY = "SAMPLE_ES_QUERY_" + IdUtil.objectId();


    // 最终结果
    public IncomeSummaryVo getIncomeSummaryResult(){
        return (IncomeSummaryVo) get(RESULT);
    }

    // es查询参数
    public SampleEsQuery getSampleEsQuery() {
        return (SampleEsQuery) get(SAMPLE_ES_QUERY);
    }

    // 页面查询参数
    public TestItemIncomeRequestVo getQuery() {
        return (TestItemIncomeRequestVo) get(FINANCE_STATISTICS_QUERY);
    }

    public static IncomeSummaryContext from(Context context) {
        return (IncomeSummaryContext)context;
    }
}
