package com.labway.lims.statistics.service.bySendDoctor;

import com.labway.lims.api.config.EsConfig;
import com.labway.lims.apply.api.dto.HspOrgSendDoctorStatisticsResponseDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.statistics.service.FinancialManagementService;
import com.labway.lims.statistics.service.impl.FinancialManagementServiceImpl;
import com.labway.lims.statistics.vo.HspOrgSendDoctorStatisticsRequestVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.labway.lims.api.ConditionCheckUtils.isValidMonthFormat;

@Service
@Primary
@Slf4j
public class OldHspOrgBySendDoctorServiceImpl implements HspOrgBySendDoctorService {

    @Resource
    private FinancialManagementService financialManagementService;

    @Resource
    private EsConfig esConfig;

    @Override
    public String version() {
        return "1.0";
    }

    @Override
    public HspOrgSendDoctorStatisticsResponseDto hspOrgBySendDoctorStatistics(HspOrgSendDoctorStatisticsRequestVo vo) {

        // 将查询条件 转换为es查询条件
        SampleEsQuery dto = getSampleEsQueryFromHspOrgSendDoctorStatisticsRequestVo(vo);

        // 所有es 结构数据
        // 根据时间拆分，并行查询ES
        List<BaseSampleEsModelDto> baseSampleEsModelDtos = financialManagementService.selectSamples(dto, vo);

        // 展示数据结构
        List<HspOrgSendDoctorStatisticsResponseDto> targetList =
                financialManagementService.getHspOrgSendDoctorStatisticsResponseDto(dto, vo, baseSampleEsModelDtos);

        HspOrgSendDoctorStatisticsResponseDto target = new HspOrgSendDoctorStatisticsResponseDto();
        target.setStartDate(FinancialManagementServiceImpl.getDateRange(dto).getStartDate());
        target.setEndDate(FinancialManagementServiceImpl.getDateRange(dto).getEndDate());
        target.setOrgList(targetList);
        target.setCountSum(targetList.stream().mapToInt(HspOrgSendDoctorStatisticsResponseDto::getCountSum).sum());
        target.setFeePriceSum(targetList.stream().reduce(BigDecimal.ZERO, (a, b) -> a.add(b.getFeePriceSum()), BigDecimal::add));
        target.setTotalFeePriceSum(targetList.stream().reduce(BigDecimal.ZERO, (a, b) -> a.add(b.getTotalFeePriceSum()), BigDecimal::add));
        target.setPayAmountSum(targetList.stream().reduce(BigDecimal.ZERO, (a, b) -> a.add(b.getPayAmountSum()), BigDecimal::add));
        target.setCustomerName("");
        return target;
    }

    private SampleEsQuery
    getSampleEsQueryFromHspOrgSendDoctorStatisticsRequestVo(HspOrgSendDoctorStatisticsRequestVo vo) {
        // 检查 送检时间 与财务月份
        checkDateRangRequest(vo.getFinancialMonth(), vo.getBeginDeliveryDate(), vo.getEndDeliveryDate());

        SampleEsQuery dto = new SampleEsQuery();
        dto.setPageSize(esConfig.getPageSize());
        dto.setPageNo(NumberUtils.INTEGER_ONE);
        // 1.0.6.6需求改为不管该样本是否已经审核都进行查询
        //dto.setIsAudit(YesOrNoEnum.YES.getCode());

        // 补充查询时间范围
        sampleEsQueryAddDateRang(dto, vo.getFinancialMonth(), vo.getBeginDeliveryDate(), vo.getEndDeliveryDate());

        if (CollectionUtils.isNotEmpty(vo.getHspOrgIds())) {
            dto.setHspOrgIds(vo.getHspOrgIds());
        }

        if (Objects.nonNull(vo.getGroupId())) {
            dto.setGroupIds(Set.of(vo.getGroupId()));
        }
        if (CollectionUtils.isNotEmpty(vo.getGroupIds())) {
            dto.setGroupIds(vo.getGroupIds());
        }

        return dto;
    }

    /**
     * 检查 送检时间 与财务月份
     *
     * @param financialMonth    财务月份
     * @param beginDeliveryDate 送检时间开始
     * @param endDeliveryDate   送检时间结束
     */
    public static void checkDateRangRequest(String financialMonth, Date beginDeliveryDate, Date endDeliveryDate) {
        if (StringUtils.isBlank(financialMonth)
                && (Objects.isNull(beginDeliveryDate) || Objects.isNull(endDeliveryDate))) {
            throw new IllegalStateException("送检时间与财务月份必须传一个");
        }
        if (StringUtils.isNotBlank(financialMonth) && Objects.nonNull(beginDeliveryDate)
                && Objects.nonNull(endDeliveryDate)) {
            throw new IllegalStateException("送检时间与财务月份只能传一个");
        }
        if (StringUtils.isNotBlank(financialMonth) && !isValidMonthFormat(financialMonth)) {
            throw new IllegalStateException("财务月份格式错误,请传入[yyyy-MM]格式");
        }
    }

    /**
     * 添加查询 时间 范围
     *
     * @param dto               查询参数
     * @param financialMonth    财务月份
     * @param beginDeliveryDate 送检开始时间
     * @param endDeliveryDate   送检结束时间
     */
    public static void sampleEsQueryAddDateRang(SampleEsQuery dto, String financialMonth, Date beginDeliveryDate,
                                          Date endDeliveryDate) {
        if (StringUtils.isNotBlank(financialMonth)) {
            YearMonth yearMonth = YearMonth.parse(financialMonth);
            LocalDateTime monthStartDateTime = yearMonth.atDay(1).atStartOfDay();
            LocalDateTime monthEndDateTime = yearMonth.atEndOfMonth().atTime(23, 59, 59);
            Date monthStartDate = Date.from(monthStartDateTime.atZone(ZoneId.systemDefault()).toInstant());
            Date monthEndDate = Date.from(monthEndDateTime.atZone(ZoneId.systemDefault()).toInstant());
            // 传入了财务月份 以终审时间查看
            // dto.setStartFinalCheckDate(monthStartDate);
            // dto.setEndFinalCheckDate(monthEndDate);

            dto.setStartFinalCheckOrCreateDate(monthStartDate);
            dto.setEndFinalCheckOrCreateDate(monthEndDate);
        }
        if (Objects.nonNull(beginDeliveryDate) && Objects.nonNull(endDeliveryDate)) {
            // 送检时间的就是样本创建时间
            dto.setStartCreateDate(beginDeliveryDate);
            dto.setEndCreateDate(endDeliveryDate);
            //            dto.setStartFinalCheckOrCreateDate(beginDeliveryDate);
            //            dto.setEndFinalCheckOrCreateDate(endDeliveryDate);

        }
    }
}
