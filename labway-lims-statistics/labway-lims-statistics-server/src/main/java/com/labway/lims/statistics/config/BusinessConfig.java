package com.labway.lims.statistics.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * BusinessConfig
 * 配置信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/22 19:08
 */
@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "business")
public class BusinessConfig {

    /**
     * ES 高级查询，字段需要keyword转换
     */
    private List<String> esKeywordFields;

    /**
     * 业务中台中间库服务在拉取报告的时候，在审核开始，结束时间各加了30分钟，这里配置一下该buffer时间
     * 避免后续修改需要重新发服务
     */
    private Map<String, Long> hspDelayBufferMinutes =
            Map.of("Z000009182", 30L, "ZC00010038", 30L, "ZC00010037", 30L, "ZC00010036", 30L, "Z000027176", 30L);

}
