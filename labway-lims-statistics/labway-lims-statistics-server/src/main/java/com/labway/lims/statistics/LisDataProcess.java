package com.labway.lims.statistics;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractHeadColumnWidthStyleStrategy;
import lombok.Data;
import org.apache.poi.ss.usermodel.Cell;

import java.io.File;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * <pre>
 * LisDataProcess
 * 描述信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/7/28 16:23
 */
public class LisDataProcess {

    public static void main(String[] args) throws Exception {

        File exportFile = new File("北新泾血脂检查数据.xlsx");
        if (exportFile.exists()) {
            exportFile.delete();
        }
        exportFile.createNewFile();

        final List<Row> list = new ArrayList<Row>();

        Row row = new Row();
        row.setCreateTime(new Date());
        row.setPatientName("姓名");
        row.setPatientCardNo("210101199909095678");
        row.setPatientAddr("上海市东方明珠塔尖儿");
        row.setPatientTel("15312351235");
        row.setSendDoctor("花医生");
        row.setItemName("血脂四项");
        row.setTestResult("5.5");
        list.add(row);

        List<List<String>> headers = new ArrayList<>();
        headers.add(new ArrayList<>() {{add("时间");}});
        headers.add(new ArrayList<>() {{add("姓名");}});
        headers.add(new ArrayList<>() {{add("证件号");}});
        headers.add(new ArrayList<>() {{add("地址");}});
        headers.add(new ArrayList<>() {{add("电话");}});
        headers.add(new ArrayList<>() {{add("送检医生");}});
        headers.add(new ArrayList<>() {{add("项目名称");}});
        headers.add(new ArrayList<>() {{add("结果");}});
        // 这里 需要指定写用哪个class去写
        ExcelWriter excelWriter = EasyExcel.write(exportFile, Row.class).head(headers).build();

        list.stream()
                // .sorted(Comparator.comparing(Row::getCreateTime))
                .collect(Collectors.groupingBy(e -> DateUtil.format(e.getCreateTime(), "yyyy-MM"), TreeMap::new, Collectors.toList()))
                .forEach((month, datas) -> {
                    datas.sort(Comparator.comparing(Row::getCreateTime));
                    WriteSheet writeSheet = EasyExcel.writerSheet(StrUtil.format("{}({})", month, datas.size())).build();
                    writeSheet.setCustomWriteHandlerList(new ArrayList<>() {{
                        add(new MyHeadColumnWidthStyle(20));
                    }});
                    excelWriter.write(datas, writeSheet);
                });

        /// 千万别忘记finish 会帮忙关闭流
        excelWriter.finish();

    }

    @Data
    public static class Row {
        @ExcelProperty(index = 0)
        private Date createTime;
        @ExcelProperty(index = 1)
        private String patientName;
        @ExcelProperty(index = 2)
        private String patientCardNo;
        @ExcelProperty(index = 3)
        private String patientAddr;
        @ExcelProperty(index = 4)
        private String patientTel;
        @ExcelProperty(index = 5)
        private String sendDoctor;
        @ExcelProperty(index = 6)
        private String itemName;
        @ExcelProperty(index = 7)
        private String testResult;
    }

    static class MyHeadColumnWidthStyle extends AbstractHeadColumnWidthStyleStrategy {
        private final Integer columnWidth;

        public MyHeadColumnWidthStyle(Integer columnWidth) {
            this.columnWidth = columnWidth;
        }

        protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
            boolean needSetWidth = relativeRowIndex != null && (isHead || relativeRowIndex == 0);
            if (needSetWidth) {
                Integer width = this.columnWidth(head, cell.getColumnIndex());
                if (width != null) {
                    width = width * 256;
                    writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), width);
                }
            }
        }

        protected Integer columnWidth(Head head, Integer columnIndex) {
            return this.columnWidth;
        }
    }

}
