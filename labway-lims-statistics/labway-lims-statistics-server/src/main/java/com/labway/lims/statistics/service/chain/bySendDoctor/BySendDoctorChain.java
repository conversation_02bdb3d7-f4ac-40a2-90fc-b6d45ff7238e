package com.labway.lims.statistics.service.chain.bySendDoctor;

import com.labway.lims.statistics.service.chain.EsDataToSampleTestItemCommand;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class BySendDoctor<PERSON><PERSON>n extends ChainBase implements InitializingBean {

    @Resource
    private BySendDoctorPaddingDataCommand bySendDoctorPaddingDataCommand;

    @Resource
    private EsDataToSampleTestItemCommand esDataToSampleTestItemCommand;
    @Resource
    private BySendDoctorCalculateResultCommand bySendDoctorCalculateResultCommand;


    @Override
    public void afterPropertiesSet() {
        // 填充数据
        addCommand(bySendDoctorPaddingDataCommand);

        // 整理数据，处理财务套餐
        addCommand(esDataToSampleTestItemCommand);

        // 计算结果  组装数据返回前端
        addCommand(bySendDoctorCalculateResultCommand);

        addCommand(e -> PROCESSING_COMPLETE);
    }
}
