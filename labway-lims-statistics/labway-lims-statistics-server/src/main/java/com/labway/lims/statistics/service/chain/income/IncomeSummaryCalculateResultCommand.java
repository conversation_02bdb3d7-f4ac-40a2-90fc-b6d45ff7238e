package com.labway.lims.statistics.service.chain.income;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Lists;
import com.labway.lims.api.PatientAges;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.CustomerNameTypeEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.apply.api.dto.SampleTestItemDto;
import com.labway.lims.apply.api.dto.TestItemIncomeDetailItemDto;
import com.labway.lims.apply.api.dto.TestItemIncomeDetailResponseDto;
import com.labway.lims.apply.api.dto.TestItemIncomeSummaryKeyDto;
import com.labway.lims.apply.api.dto.TestItemIncomeSummaryResponseDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.statistics.service.chain.bySendDoctor.BySendDoctorCalculateResultCommand;
import com.labway.lims.statistics.service.impl.FinancialManagementServiceImpl;
import com.labway.lims.statistics.vo.IncomeSummaryVo;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.labway.lims.apply.api.dto.TestItemIncomeFilterDto.getIsFreeFlag;

@Component
public class IncomeSummaryCalculateResultCommand implements Command {
    @Override
    public boolean execute(Context context) throws Exception {
        final IncomeSummaryContext from = IncomeSummaryContext.from(context);

        // 时间范围
        final FinancialManagementServiceImpl.DateRange dateRange = BySendDoctorCalculateResultCommand.getDateRange(from.getSampleEsQuery());

        // 开票类型
        final CustomerNameTypeEnum customerNameType = from.getCustomerNameTypeEnum();

        // 基础数据， 根据送检机构分组
        final Map<Long, List<SampleTestItemDto>> baseDataByHspOrgIdMap = from.getSampleTestItemList()
                .stream().collect(Collectors.groupingBy(SampleTestItemDto::getHspOrgId));

        // 送检机构
        final Map<Long, HspOrganizationDto> hspOrganizationDtoByHspOrgIdMap = from.getHspOrgList().stream()
                .collect(Collectors.toMap(HspOrganizationDto::getHspOrgId, Function.identity(), (a, b) -> b));

        YesOrNoEnum isFreeFlag = getIsFreeFlag(from.getQuery().getIsFree());


        // 响应数据结构
        List<TestItemIncomeSummaryResponseDto> targetList = Lists.newArrayListWithCapacity(hspOrganizationDtoByHspOrgIdMap.size());
        DecimalFormat percentFormat = new DecimalFormat("0.00%");

        for (Map.Entry<Long, List<SampleTestItemDto>> entry : baseDataByHspOrgIdMap.entrySet()) {
            TestItemIncomeSummaryResponseDto target = new TestItemIncomeSummaryResponseDto();
            target.setStartDate(dateRange.getStartDate());
            target.setEndDate(dateRange.getEndDate());

            Long hspOrgId = entry.getKey();
            final List<SampleTestItemDto> sampleTestItemDtoList = entry.getValue();
            HspOrganizationDto hspOrganizationDto = hspOrganizationDtoByHspOrgIdMap.get(hspOrgId);
            String customerName = StringUtils.EMPTY;
            String hspOrgName = StringUtils.EMPTY;
            if (Objects.nonNull(hspOrganizationDto)) {
                customerName = this.getCustomerName(customerNameType, hspOrganizationDto);
                hspOrgName = hspOrganizationDto.getHspOrgName();
            }

            // 过滤是否免单的项目
            final List<SampleTestItemDto> filterIsFreeList = sampleTestItemDtoList.stream().filter(e -> Objects.equals(e.getIsFree(), isFreeFlag.getCode())).collect(Collectors.toList());
            // 汇总
            this.collectInfo(filterIsFreeList, target, hspOrgName, customerName, hspOrgId, percentFormat);

            // 明细
            this.detailInfo(filterIsFreeList, target, hspOrgName, customerName, dateRange);

            targetList.add(target);
        }


        // 填充没有数据的客商
        for (HspOrganizationDto e : hspOrganizationDtoByHspOrgIdMap.values()) {
            if (targetList.stream().noneMatch(k -> Objects.equals(k.getCustomerId(), e.getHspOrgId()))) {
                targetList.add(this.getTestItemIncomeSummaryResponseDto(e, customerNameType, from));
            }
        }

        final IncomeSummaryVo v = new IncomeSummaryVo();
        v.setTotalFeePriceSum(BigDecimal.ZERO);
        v.setTotalPayAmountSum(BigDecimal.ZERO);
        v.setTotalDetailFeePriceSum(BigDecimal.ZERO);
        v.setList(targetList);


        for (TestItemIncomeSummaryResponseDto e : targetList) {
            if (Objects.nonNull(e.getDetail())) {
                v.setTotalDetailFeePriceSum(v.getTotalDetailFeePriceSum().add(e.getDetail().getFeePriceSum()));
            }
            v.setTotalFeePriceSum(v.getTotalFeePriceSum().add(e.getFeePriceSum()));
            v.setTotalPayAmountSum(v.getTotalPayAmountSum().add(e.getPayAmountSum()));
        }

        from.put(IncomeSummaryContext.RESULT, v);

        return CONTINUE_PROCESSING;

    }

    private TestItemIncomeSummaryResponseDto getTestItemIncomeSummaryResponseDto(HspOrganizationDto e, CustomerNameTypeEnum customerNameType, IncomeSummaryContext from) {
        final TestItemIncomeSummaryResponseDto t = new TestItemIncomeSummaryResponseDto();
        if (Objects.equals(customerNameType, CustomerNameTypeEnum.INVOICE_NAME)) {
            t.setCustomerName(e.getInvoice());
        } else {
            t.setCustomerName(e.getHspOrgName());
        }

        final FinancialManagementServiceImpl.DateRange range = FinancialManagementServiceImpl.getDateRange(from.getSampleEsQuery());

        t.setCustomerId(e.getHspOrgId());
        t.setStartDate(range.getStartDate());
        t.setEndDate(range.getEndDate());
        t.setCountSum(0);
        t.setFeePriceSum(BigDecimal.ZERO);
        t.setPayAmountSum(BigDecimal.ZERO);
        t.setItemList(Lists.newArrayList());
        return t;
    }

    private void collectInfo(List<SampleTestItemDto> sampleTestItemDtoList, TestItemIncomeSummaryResponseDto target, String hspOrgName, String customerName, Long hspOrgId, DecimalFormat percentFormat) {
        target.setCustomerName(customerName);
        target.setCustomerId(hspOrgId);

        // 就诊类型 检验项目 标准收费 折扣率一样的 分组
        Map<TestItemIncomeSummaryKeyDto,
                List<SampleTestItemDto>> groupingByKeyDto = sampleTestItemDtoList.stream()
                .collect(Collectors.groupingBy(obj -> new TestItemIncomeSummaryKeyDto(obj.getApplyTypeCode(),
                        obj.getTestItemId(), obj.getPrice(), obj.getDiscount())));

        // 响应数据行
        List<TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem> itemList =
                Lists.newArrayListWithCapacity(groupingByKeyDto.size());

        // 计算金额结果
        final Result result = this.getResult(groupingByKeyDto, hspOrgName, percentFormat, itemList);
        List<TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem> sorted = itemList.stream()
                .sorted(Comparator.nullsLast(Comparator
                        .comparing(TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem::getApplyTypeCode)
                        .thenComparing(TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem::getTestItemCode)))
                .collect(Collectors.toList());

        target.setItemList(sorted);
        target.setCountSum(result.countSumAll);
        target.setFeePriceSum(result.feePriceSumAll.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
        target.setPayAmountSum(result.payAmountSumAll.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
    }

    private void detailInfo(List<SampleTestItemDto> value, TestItemIncomeSummaryResponseDto target, String hspOrgName, String customerName, FinancialManagementServiceImpl.DateRange dateRange) {

        // 相同申请单合并
        Map<Long, List<SampleTestItemDto>> groupingByApplyId =
                value.stream().collect(Collectors.groupingBy(SampleTestItemDto::getApplyId));

        // 拆分为行 一个申请单就是一行 数据
        List<TestItemIncomeDetailItemDto> itemDtoList = Lists.newArrayListWithCapacity(value.size()); ;

        final Result result = this.getResult(groupingByApplyId, hspOrgName, itemDtoList);

        List<TestItemIncomeDetailItemDto> sorted = itemDtoList.stream()
                .sorted(Comparator.comparing(TestItemIncomeDetailItemDto::getSendDate)).collect(Collectors.toList());
        sorted.forEach(item -> {
            if (Objects.equals(item.getSendDate(), DateUtil.formatDate(DefaultDateEnum.DEFAULT_DATE.getDate()))) {
                item.setSendDate(StringUtils.EMPTY);
            }
        });

        TestItemIncomeDetailResponseDto d = new TestItemIncomeDetailResponseDto();
        d.setCustomerName(customerName);
        d.setStartDate(dateRange.getStartDate());
        d.setEndDate(dateRange.getEndDate());
        d.setItemList(sorted);
        d.setCountSum(result.countSumAll);
        d.setFeePriceSum(result.feePriceSumAll.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
        d.setDiscountFeePriceSum(result.payAmountSumAll.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
        target.setDetail(d);
    }

    private Result getResult(Map<Long, List<SampleTestItemDto>> groupingByApplyId, String hspOrgName, List<TestItemIncomeDetailItemDto> itemDtoList) {
        // 数量合计 标准收费合计
        Integer countSumAll = NumberUtils.INTEGER_ZERO;
        BigDecimal feePriceSumAll = BigDecimal.ZERO;
        BigDecimal discountFeePriceSumAll = BigDecimal.ZERO;


        for (Map.Entry<Long, List<SampleTestItemDto>> line : groupingByApplyId.entrySet()) {
            List<SampleTestItemDto> summaryItemList = line.getValue();

            for (SampleTestItemDto e : summaryItemList) {
                TestItemIncomeDetailItemDto itemDto = new TestItemIncomeDetailItemDto();
                itemDto.setCustomerName(hspOrgName);
                itemDto.setDept(e.getDept());
                itemDto.setRemark(e.getRemark());
                itemDto.setSendDate(DateUtil.formatDate(e.getCreateDate()));
                itemDto.setSendDoctorName(e.getSendDoctorName());
                itemDto.setPatientVisitCard(e.getPatientVisitCard());
                itemDto.setPatientName(e.getPatientName());
                itemDto.setPatientSex(SexEnum.getByCode(e.getPatientSex()).getDesc());
                itemDto.setPatientAge(PatientAges.toText(e));
                itemDto.setApplyTypeName(e.getApplyTypeName());
                itemDto.setTestItems(e.getTestItemName());
                itemDto.setFeeCode(e.getFeeCode());
                itemDto.setFeeName(e.getFeeName());
                itemDto.setCount(ObjectUtils.defaultIfNull(e.getCount(), NumberUtils.INTEGER_ONE));
                itemDto.setFeePrice(e.getPrice().multiply(BigDecimal.valueOf(itemDto.getCount())).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                itemDto.setDiscount(e.getDiscount().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                itemDto.setDiscountLabel(itemDto.getDiscount().multiply(new BigDecimal(100)).setScale(NumberUtils.INTEGER_ZERO, RoundingMode.HALF_UP) + StringPool.PERCENT);
                itemDto.setDiscountFeePrice(e.getPrice().multiply(e.getDiscount()).multiply(BigDecimal.valueOf(itemDto.getCount())).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                itemDto.setDiscountFeePriceLabel(itemDto.getDiscountFeePrice().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP) + StringPool.EMPTY);
                itemDto.setOutTestItemCode(e.getOutTestItemCode());
                itemDto.setOutTestItemName(e.getOutTestItemName());
                itemDtoList.add(itemDto);

                countSumAll += itemDto.getCount();
                feePriceSumAll = feePriceSumAll.add(itemDto.getFeePrice());
                discountFeePriceSumAll = discountFeePriceSumAll.add(itemDto.getDiscountFeePrice());
            }
        }
        return new Result(countSumAll, feePriceSumAll, discountFeePriceSumAll);
    }

    private Result getResult(Map<TestItemIncomeSummaryKeyDto, List<SampleTestItemDto>> groupingByKeyDto, String hspOrgName, DecimalFormat percentFormat, List<TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem> itemList) {
        // 数量合计、 合计金额合计、结算金额合计
        Integer countSumAll = NumberUtils.INTEGER_ZERO;
        BigDecimal feePriceSumAll = BigDecimal.ZERO;
        BigDecimal payAmountSumAll = BigDecimal.ZERO;

        for (Map.Entry<TestItemIncomeSummaryKeyDto, List<SampleTestItemDto>> groupingByKey : groupingByKeyDto
                .entrySet()) {
            TestItemIncomeSummaryKeyDto keyDto = groupingByKey.getKey();
            List<SampleTestItemDto> summaryItemList = groupingByKey.getValue();

            int countSum = summaryItemList.stream()
                    .map(obj -> ObjectUtils.defaultIfNull(obj.getCount(), NumberUtils.INTEGER_ONE))
                    .mapToInt(Integer::intValue).sum();
            BigDecimal feePriceSum = keyDto.getPrice().multiply(BigDecimal.valueOf(countSum));
            // 合计金额
            BigDecimal priceSum = summaryItemList.stream()
                    .map(item -> item.getPrice()
                            .multiply(
                                    BigDecimal.valueOf(ObjectUtils.defaultIfNull(item.getCount(), NumberUtils.INTEGER_ONE)))
                            .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 未免单 金额合计 用于结算金额
            BigDecimal payAmount =
                    summaryItemList.stream().filter(obj -> !Objects.equals(obj.getIsFree(), YesOrNoEnum.YES.getCode())).map(item -> {
                        BigDecimal price = item.getPrice();
                        if (item.isSpecialOfferFlag()) {
                            // 该样本检验项目 参与了特价项目 结算金额为折后价格
                            price = ObjectUtils.defaultIfNull(item.getDiscountPrice(), BigDecimal.ZERO);
                            return price
                                    .multiply(BigDecimal
                                            .valueOf(ObjectUtils.defaultIfNull(item.getCount(), NumberUtils.INTEGER_ONE)))
                                    .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
                        }
                        return price
                                .multiply(
                                        BigDecimal.valueOf(ObjectUtils.defaultIfNull(item.getCount(), NumberUtils.INTEGER_ONE)))
                                .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).multiply(keyDto.getDiscount())
                                .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
                    }).reduce(BigDecimal.ZERO, BigDecimal::add);

            countSumAll = countSumAll + countSum;
            feePriceSumAll = feePriceSumAll.add(priceSum);
            payAmountSumAll = payAmountSumAll.add(payAmount);

            TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem temp =
                    new TestItemIncomeSummaryResponseDto.TestItemIncomeSummaryItem();

            temp.setCustomerName(hspOrgName);
            temp.setApplyTypeName(summaryItemList.get(NumberUtils.INTEGER_ZERO).getApplyTypeName());
            temp.setApplyTypeCode(summaryItemList.get(NumberUtils.INTEGER_ZERO).getApplyTypeCode());
            temp.setTestItemCode(summaryItemList.get(NumberUtils.INTEGER_ZERO).getTestItemCode());
            temp.setTestItemName(summaryItemList.get(NumberUtils.INTEGER_ZERO).getTestItemName());
            temp.setFeeCode(summaryItemList.get(NumberUtils.INTEGER_ZERO).getFeeCode());
            temp.setFeeName(summaryItemList.get(NumberUtils.INTEGER_ZERO).getFeeName());
            temp.setCount(countSum);
            temp.setFeePrice(keyDto.getPrice().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            temp.setFeePriceSum(feePriceSum.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            temp.setDiscount(percentFormat.format(keyDto.getDiscount()));
            temp.setPayAmount(payAmount.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));

            // 获取最新的项目对照项目编码和名称
            SampleTestItemDto sampleTestItemDto = summaryItemList.stream()
                    .sorted(Comparator.comparing(SampleTestItemDto::getCreateDate).reversed())
                    .filter(i -> StringUtils.isNotBlank(i.getOutTestItemCode()))
                    .findFirst()
                    .orElse(new SampleTestItemDto());
            temp.setOutTestItemCode(sampleTestItemDto.getOutTestItemCode());
            temp.setOutTestItemName(sampleTestItemDto.getOutTestItemName());

            itemList.add(temp);

        }
        return new Result(countSumAll, feePriceSumAll, payAmountSumAll);
    }

    private static class Result {
        public final Integer countSumAll;
        public final BigDecimal feePriceSumAll;
        public final BigDecimal payAmountSumAll;

        public Result(Integer countSumAll, BigDecimal feePriceSumAll, BigDecimal payAmountSumAll) {
            this.countSumAll = countSumAll;
            this.feePriceSumAll = feePriceSumAll;
            this.payAmountSumAll = payAmountSumAll;
        }
    }

    private String getCustomerName(CustomerNameTypeEnum customerNameType, HspOrganizationDto hspOrganizationDto) {
        String customerName;
        if (Objects.equals(customerNameType, CustomerNameTypeEnum.INVOICE_NAME)) {
            // 展示开票名称
            customerName = hspOrganizationDto.getInvoice();
        } else {
            customerName = hspOrganizationDto.getHspOrgName();
        }

        if (StringUtils.isBlank(customerName)) {
            customerName = hspOrganizationDto.getHspOrgName();
        }
        return customerName;
    }
}
