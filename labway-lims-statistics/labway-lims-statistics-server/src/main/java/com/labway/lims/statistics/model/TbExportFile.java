package com.labway.lims.statistics.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.statistics.enums.ExportFileType;
import com.labway.lims.statistics.enums.ExportStatus;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <pre>
 * ExportFile
 * 导出文件
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/4/9 14:22
 */
@Getter
@Setter
@TableName("tb_export_file")
public class TbExportFile implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long exportFileId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * @see ExportFileType
     */
    private String fileType;

    /**
     * obs地址
     */
    private String url;

    /**
     * @see ExportStatus
     */
    private Integer status;

    /**
     * 来源页面
     */
    private String source;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 1:删除，0:未删
     */
    private Integer isDelete;

}
