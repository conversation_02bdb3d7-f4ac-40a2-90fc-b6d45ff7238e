package com.labway.lims.statistics.service.income;

import com.labway.lims.statistics.service.chain.income.IncomeSummaryChain;
import com.labway.lims.statistics.service.chain.income.IncomeSummaryContext;
import com.labway.lims.statistics.vo.IncomeSummaryVo;
import com.labway.lims.statistics.vo.TestItemIncomeRequestVo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class NewIncomeSummaryServiceImpl implements IncomeSummaryService{

    @Resource
    private IncomeSummaryChain incomeSummaryChain;

    @Override
    public String version() {
        return "2.0";
    }

    @Override
    public IncomeSummaryVo incomeSummaryStatistics(TestItemIncomeRequestVo vo) {
        final IncomeSummaryContext incomeSummaryContext = new IncomeSummaryContext();
        incomeSummaryContext.put(IncomeSummaryContext.FINANCE_STATISTICS_QUERY, vo);
        incomeSummaryContext.setFilterDto(vo);

        try{
            if(!incomeSummaryChain.execute(incomeSummaryContext)){
                throw new IllegalStateException("销售项目收入查询计算失败");
            }
            return incomeSummaryContext.getIncomeSummaryResult();
        }catch (Exception e){
            throw new IllegalStateException(e);
        }
    }
}
