package com.labway.lims.statistics.model.es;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.List;

/**
 * 微生物细菌
 */
@Getter
@Setter
public final class MicrobiologyGerms {
    /**
     * 检验项目id
     */
    @Field(type = FieldType.Long)
    private Long testItemId;
    /**
     * 检验项目编码
     */
    @Field(type = FieldType.Keyword)
    private String testItemCode;
    /**
     * 检验项目名称
     */
    @Field(type = FieldType.Keyword)
    private String testItemName;
    /**
     * 细菌id
     */
    @Field(type = FieldType.Long)
    private Long germId;
    /**
     * 细菌编码
     */
    @Field(type = FieldType.Keyword)
    private String germCode;
    /**
     * 细菌名称
     */
    @Field(type = FieldType.Keyword)
    private String germName;
    /**
     * 细菌数量
     */
    @Field(type = FieldType.Integer)
    private Integer germCount;
    /**
     * 细菌备注
     */
    @Field(type = FieldType.Text)
    private String germRemark;
    /**
     * 检验方法
     */
    @Field(type = FieldType.Keyword)
    private String testMethod;

    /**
     * 药物
     */
    @Field(type = FieldType.Nested)
    private List<MicrobiologyMedicines> medicines;

}
