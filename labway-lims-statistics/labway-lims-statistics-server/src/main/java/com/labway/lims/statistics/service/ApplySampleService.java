package com.labway.lims.statistics.service;

import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.statistics.vo.ApplySamplePageVo;
import com.labway.lims.statistics.vo.ApplySampleTestItemVo;

import java.util.List;

/**
 * <p>
 * ApplySampleService
 * 样本信息查询
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/7 19:34
 */
public interface ApplySampleService {

    /**
     * 查询申请单样本信息
     */
    ApplySamplePageVo selectApplySample(SampleEsQuery query);
    /**
     * 查询申请单样本信息数量
     */
    long selectApplySampleCount(SampleEsQuery query);
    /**
     * 查询申请单样本信息数据
     */
    ApplySamplePageVo selectApplySampleData(SampleEsQuery query);

    /**
     * 查询检验项目
     */
    List<ApplySampleTestItemVo> selectTestItems(Long applySampleId);

    /**
     * 根据条码号获取取样日期
     */
    String selectTakeSampleTimeByBarcode(String barCode, String hspOrgCode);
}
