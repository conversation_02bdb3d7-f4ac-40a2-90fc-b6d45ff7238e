package com.labway.lims.statistics.controller;


import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.vo.InfectionStatisticsRequestVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

/**
 * 院感统计
 */
@RestController
@RequestMapping("/data-statistics-infection")
public class InfectionStatisticsController extends BaseController {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;

    @PostMapping("/statistics")
    public Object statistics(@RequestBody InfectionStatisticsRequestVo vo) {

        if (Objects.isNull(vo.getEndDate()) || Objects.isNull(vo.getBeginDate())) {
            throw new IllegalStateException("日期错误");
        }

        final SampleEsQuery.SampleEsQueryBuilder builder = SampleEsQuery.builder().isAudit(YesOrNoEnum.YES.getCode());
        if (Objects.equals(vo.getDateType(), InfectionStatisticsRequestVo.DATE_TYPE_TEST_DATE)) {
            builder.startTestDate(vo.getBeginDate());
            builder.endTestDate(vo.getEndDate());
        } else if (Objects.equals(vo.getDateType(), InfectionStatisticsRequestVo.DATE_TYPE_CHECK_DATE)) {
            builder.startFinalCheckDate(vo.getBeginDate());
            builder.endFinalCheckDate(vo.getEndDate());
        } else {
            throw new IllegalStateException("日期类型错误");
        }

        if (CollectionUtils.isNotEmpty(vo.getHspOrgIds())) {
            builder.hspOrgIds(vo.getHspOrgIds());
        }

        builder.isAudit(YesOrNoEnum.YES.getCode());

        // 院感
        builder.itemTypes(Set.of(ItemTypeEnum.INFECTION.name()));


        final List<? extends BaseSampleEsModelDto> data = elasticSearchSampleService.selectSamples(builder.build());
        final Map<String, Map<String, Integer>> map = new LinkedHashMap<>();

        if (CollectionUtils.isEmpty(data)) {
            return map;
        }

        final Set<String> columns = new TreeSet<>();

        // key: 项目 ，value_key: 年月
        for (BaseSampleEsModelDto e : data) {

            if (CollectionUtils.isEmpty(e.getTestItems())) {
                continue;
            }

            for (BaseSampleEsModelDto.TestItem testItem : e.getTestItems()) {
                final String date;
                if (Objects.equals(vo.getDateType(), InfectionStatisticsRequestVo.DATE_TYPE_TEST_DATE)) {
                    date = DateFormatUtils.format(e.getTestDate(), "yyyyMM");
                } else if (Objects.equals(vo.getDateType(), InfectionStatisticsRequestVo.DATE_TYPE_CHECK_DATE)) {
                    date = DateFormatUtils.format(e.getFinalCheckDate(), "yyyyMM");
                } else {
                    throw new IllegalStateException("日期类型错误");
                }

                // 根据项目来
                final Map<String, Integer> m = map.computeIfAbsent(testItem.getTestItemName(), k -> new LinkedHashMap<>());

                // 获取这个项目下的日期
                m.put(date, m.computeIfAbsent(date, s -> 0) + 1);

                columns.add(date);
            }

        }

        return Map.of("data", map, "columns", columns);
    }

}
