package com.labway.lims.statistics.model.es;

import com.labway.lims.api.enums.routine.TestJudgeEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.List;

/**
 * 院感
 */
@Getter
@Setter
public class InfectionInspection extends BaseSampleEsModel {

    /**
     * 报告项目
     */
    @Field(type = FieldType.Nested)
    private List<ReportItem> reportItems;


    /**
     * 报告项目
     */
    @Setter
    @Getter
    private static final class ReportItem {
        /**
         * 报告单项目编码
         */
        @Field(type = FieldType.Keyword)
        private String reportItemCode;
        /**
         * 报告项目名称
         */
        @Field(type = FieldType.Keyword)
        private String reportItemName;
        /**
         * 检验项目ID
         */
        @Field(type = FieldType.Long)
        private Long testItemId;
        /**
         * 检验项目编码
         */
        @Field(type = FieldType.Keyword)
        private String testItemCode;
        /**
         * 检验项目名称
         */
        @Field(type = FieldType.Keyword)
        private String testItemName;
        /**
         * 单位
         */
        @Field(type = FieldType.Keyword)
        private String unit;
        /**
         * 结果范围
         */
        @Field(type = FieldType.Keyword)
        private String range;
        /**
         * 结果 （经过一系列的计算 转换最终得到的结果值）
         */
        @Field(type = FieldType.Keyword)
        private String result;
        /**
         * 检验判定 UP  DOWN  NORMAL
         * @see TestJudgeEnum
         */
        @Field(type = FieldType.Keyword)
        private String judge;
    }

}
