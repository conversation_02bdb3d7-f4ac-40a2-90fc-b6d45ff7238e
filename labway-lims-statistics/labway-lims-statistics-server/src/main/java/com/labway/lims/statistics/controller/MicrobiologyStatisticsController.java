package com.labway.lims.statistics.controller;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyGermDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyInspectionDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyMedicineDto;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.vo.MicrobiologyPositiveStatisticsVo;
import com.labway.lims.statistics.vo.MicrobiologyPositiveStatisticsWithTotalVo;
import com.labway.lims.statistics.vo.MicrobiologyStatisticsRequestVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 微生物统计
 */
@RestController
@RequestMapping("/data-statistics-microbiology")
public class MicrobiologyStatisticsController extends BaseController {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;

    /**
     * 工作量统计
     */
    @PostMapping("/statistics")
    public Object statistics(@RequestBody MicrobiologyStatisticsRequestVo vo) {

        if (Objects.isNull(vo.getEndDate()) || Objects.isNull(vo.getBeginDate())) {
            throw new IllegalStateException("日期错误");
        }

        final SampleEsQuery.SampleEsQueryBuilder builder = SampleEsQuery.builder().isAudit(YesOrNoEnum.YES.getCode());
        if (Objects.equals(vo.getDateType(), MicrobiologyStatisticsRequestVo.DATE_TYPE_TEST_DATE)) {
            builder.startTestDate(vo.getBeginDate());
            builder.endTestDate(vo.getEndDate());
        } else if (Objects.equals(vo.getDateType(), MicrobiologyStatisticsRequestVo.DATE_TYPE_CHECK_DATE)) {
            builder.startFinalCheckDate(vo.getBeginDate());
            builder.endFinalCheckDate(vo.getEndDate());
        } else {
            throw new IllegalStateException("日期类型错误");
        }

        if (CollectionUtils.isNotEmpty(vo.getHspOrgIds())) {
            builder.hspOrgIds(vo.getHspOrgIds());
        }

        builder.isAudit(YesOrNoEnum.YES.getCode());
        // 过滤已终止的样本
        builder.excludeSampleStatus(List.of(SampleStatusEnum.STOP_TEST.getCode()));

        // 微生物
        builder.itemTypes(Set.of(ItemTypeEnum.MICROBIOLOGY.name()));

        builder.pageNo(1);
        builder.pageSize(Integer.MAX_VALUE);


        final List<? extends BaseSampleEsModelDto> data = elasticSearchSampleService.selectSamples(builder.build());
        // key: 样本类型 ，value_key: 年月
        final Map<String, Map<String, Integer>> map = new LinkedHashMap<>();

        if (CollectionUtils.isEmpty(data)) {
            return map;
        }


        final Set<String> columns = new TreeSet<>();


        for (BaseSampleEsModelDto e : data) {
            final String date;
            if (Objects.equals(vo.getDateType(), MicrobiologyStatisticsRequestVo.DATE_TYPE_TEST_DATE)) {
                date = DateFormatUtils.format(e.getTestDate(), "yyyyMM");
            } else if (Objects.equals(vo.getDateType(), MicrobiologyStatisticsRequestVo.DATE_TYPE_CHECK_DATE)) {
                date = DateFormatUtils.format(e.getFinalCheckDate(), "yyyyMM");
            } else {
                throw new IllegalStateException("日期类型错误");
            }

            // 获取样本类型
            final Map<String, Integer> m = map.computeIfAbsent(e.getSampleTypeName(), k -> new LinkedHashMap<>());

            // 获取这个样本类型下的日期
            m.put(date, m.computeIfAbsent(date, s -> 0) + 1);


            columns.add(date);
        }

        return Map.of("data", map, "columns", columns);

    }


    /**
     * 阳性统计
     */
    @PostMapping("/positive-statistics")
    public List<MicrobiologyPositiveStatisticsVo> positiveStatistics(@RequestBody MicrobiologyStatisticsRequestVo vo) {

        if (Objects.isNull(vo.getEndDate()) || Objects.isNull(vo.getBeginDate())) {
            throw new IllegalStateException("日期错误");
        }

        final SampleEsQuery.SampleEsQueryBuilder builder = SampleEsQuery.builder().isAudit(YesOrNoEnum.YES.getCode());
        if (Objects.equals(vo.getDateType(), MicrobiologyStatisticsRequestVo.DATE_TYPE_TEST_DATE)) {
            builder.startTestDate(vo.getBeginDate());
            builder.endTestDate(vo.getEndDate());
        } else if (Objects.equals(vo.getDateType(), MicrobiologyStatisticsRequestVo.DATE_TYPE_CHECK_DATE)) {
            builder.startFinalCheckDate(vo.getBeginDate());
            builder.endFinalCheckDate(vo.getEndDate());
        } else {
            throw new IllegalStateException("日期错误");
        }

        if (CollectionUtils.isNotEmpty(vo.getHspOrgIds())) {
            builder.hspOrgIds(vo.getHspOrgIds());
        }

        // 微生物
        builder.itemTypes(Set.of(ItemTypeEnum.MICROBIOLOGY.name()));

        final List<MicrobiologyInspectionDto> samples = elasticSearchSampleService.selectSamples(builder.build())
                // 只要微生物样本
                .stream().filter(MicrobiologyInspectionDto.class::isInstance)
                // 转成微生物样本
                .map(e -> (MicrobiologyInspectionDto) e)
                // 获取到细菌不为空的
                .filter(e -> CollectionUtils.isNotEmpty(e.getGerms()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        final List<MicrobiologyPositiveStatisticsVo> list = new LinkedList<>();


        for (MicrobiologyInspectionDto sample : samples) {
            if (CollectionUtils.isEmpty(sample.getGerms())) {
                continue;
            }
            for (MicrobiologyGermDto germ : sample.getGerms()) {
                if (CollectionUtils.isEmpty(germ.getMedicines())) {
                    continue;
                }
                for (MicrobiologyMedicineDto medicine : germ.getMedicines()) {
                    final MicrobiologyPositiveStatisticsVo v = new MicrobiologyPositiveStatisticsVo();
                    v.setId(IdUtil.simpleUUID());
                    BeanUtils.copyProperties(germ, v);
                    BeanUtils.copyProperties(medicine, v);
                    BeanUtils.copyProperties(sample, v);

                    v.setPatientSubage(sample.getPatientSubage());
                    v.setPatientSubageUnit(sample.getPatientSubageUnit());
                    list.add(v);
                }
            }
        }

        return list;
    }

    /**
     * 阳性统计
     */
    @PostMapping("/positive-statistics-with-total")
    public MicrobiologyPositiveStatisticsWithTotalVo positiveStatisticsWithTotal(@RequestBody MicrobiologyStatisticsRequestVo vo) {
        if (Objects.isNull(vo.getEndDate()) || Objects.isNull(vo.getBeginDate())) {
            throw new IllegalStateException("日期错误");
        }

        final SampleEsQuery.SampleEsQueryBuilder builder = SampleEsQuery.builder().isAudit(YesOrNoEnum.YES.getCode());
        if (Objects.equals(vo.getDateType(), MicrobiologyStatisticsRequestVo.DATE_TYPE_TEST_DATE)) {
            builder.startTestDate(vo.getBeginDate());
            builder.endTestDate(vo.getEndDate());
        } else if (Objects.equals(vo.getDateType(), MicrobiologyStatisticsRequestVo.DATE_TYPE_CHECK_DATE)) {
            builder.startFinalCheckDate(vo.getBeginDate());
            builder.endFinalCheckDate(vo.getEndDate());
        } else {
            throw new IllegalStateException("日期错误");
        }

        if (CollectionUtils.isNotEmpty(vo.getHspOrgIds())) {
            builder.hspOrgIds(vo.getHspOrgIds());
        }

        // 微生物
        builder.itemTypes(Set.of(ItemTypeEnum.MICROBIOLOGY.name()));
        // 过滤已终止的样本
        builder.excludeSampleStatus(List.of(SampleStatusEnum.STOP_TEST.getCode()));
        // 细菌备注编码
        builder.germRemarkCodes(vo.getGermRemarkCodes());

        MicrobiologyPositiveStatisticsWithTotalVo statistics = new MicrobiologyPositiveStatisticsWithTotalVo();

        final List<MicrobiologyInspectionDto> samples = elasticSearchSampleService.selectSamples(builder.build())
                // 只要微生物样本
                .stream().filter(MicrobiologyInspectionDto.class::isInstance)
                // 转成微生物样本
                .map(e -> (MicrobiologyInspectionDto) e)
                // 获取到细菌不为空的
                .filter(e -> CollectionUtils.isNotEmpty(e.getGerms()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(samples)) {
            return statistics;
        }

        final List<MicrobiologyPositiveStatisticsVo> list = new LinkedList<>();

        int total = 0, // 合计
                germTotalCount = 0, // 共有菌株数
                germNoMedicineCount = 0, // 未做药敏菌株数
                germMedicineCount = 0; // 已做药敏菌株数

        for (MicrobiologyInspectionDto sample : samples) {
            // 细菌
            List<MicrobiologyGermDto> germs = sample.getGerms();
            if (CollectionUtils.isEmpty(germs)) {
                continue;
            }
            for (MicrobiologyGermDto germ : germs) {
                if (CollectionUtils.isNotEmpty(vo.getGermRemarkCodes())
                        && !vo.getGermRemarkCodes().contains(germ.getGermRemarkCode())) {
                    continue;
                }

                final MicrobiologyPositiveStatisticsVo sv = new MicrobiologyPositiveStatisticsVo();
                sv.setId(IdUtil.simpleUUID());
                BeanUtils.copyProperties(germ, sv);
                BeanUtils.copyProperties(sample, sv);

                sv.setDept(sample.getDept());
                sv.setChecker(StringUtils.defaultString(sample.getTwoCheckerName(),
                        sample.getFinalCheckerName()));
                sv.setCheckDate(sample.getFinalCheckDate());
                sv.setSendDoctorName(sample.getSendDoctorName());
                sv.setSendDoctorCode(sample.getSendDoctorCode());
                sv.setSamplingDate(sample.getSamplingDate());

                // 药物
                List<MicrobiologyMedicineDto> medicines = germ.getMedicines();
                if (CollectionUtils.isEmpty(medicines)) {
                    list.add(sv);
                    germNoMedicineCount++;
                    total++;
                    continue;
                }

                germMedicineCount++;
                for (MicrobiologyMedicineDto medicine : medicines) {
                    final MicrobiologyPositiveStatisticsVo v = new MicrobiologyPositiveStatisticsVo();
                    BeanUtils.copyProperties(sv, v);
                    v.setId(IdUtil.simpleUUID());
                    BeanUtils.copyProperties(medicine, v);
                    list.add(v);
                    total++;
                }
            }
        }
        germTotalCount = germNoMedicineCount + germMedicineCount;

        statistics.setTotal(total);
        statistics.setGermTotalCount(germTotalCount);
        statistics.setGermNoMedicineCount(germNoMedicineCount);
        statistics.setGermMedicineCount(germMedicineCount);
        statistics.setDatas(list);

        return statistics;
    }

}
