package com.labway.lims.statistics.service.chain.bySendDoctor;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.labway.lims.api.PatientAges;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.CustomerNameTypeEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.routine.DefaultDateEnum;
import com.labway.lims.apply.api.dto.HspOrgSendDoctorStatisticsItemDto;
import com.labway.lims.apply.api.dto.HspOrgSendDoctorStatisticsResponseDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SampleTestItemDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.statistics.service.chain.byPlateform.ArithmeticalDiscountCommand;
import com.labway.lims.statistics.service.impl.FinancialManagementServiceImpl;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class BySendDoctorCalculateResultCommand implements Command {

    @Resource
    private ArithmeticalDiscountCommand arithmeticalDiscountCommand;

    @Override
    public boolean execute(Context context) throws Exception {
        final BySendDoctorContext from = BySendDoctorContext.from(context);

        // 时间范围
        final FinancialManagementServiceImpl.DateRange dateRange = BySendDoctorCalculateResultCommand.getDateRange(from.getSampleEsQuery());

        // 开票类型
        final CustomerNameTypeEnum customerNameType = from.getCustomerNameTypeEnum();

        // 基础数据， 根据送检机构分组
        final Map<Long, List<SampleTestItemDto>> baseDataByHspOrgIdMap = from.getSampleTestItemList()
                .stream().collect(Collectors.groupingBy(SampleTestItemDto::getHspOrgId));

        // 送检机构
        final Map<Long, HspOrganizationDto> hspOrganizationDtoByHspOrgIdMap = from.getHspOrgList().stream()
                .collect(Collectors.toMap(HspOrganizationDto::getHspOrgId, Function.identity(), (a, b) -> b));

        // 响应数据结构
        List<HspOrgSendDoctorStatisticsResponseDto> targetList = Lists.newArrayListWithCapacity(baseDataByHspOrgIdMap.size());
        DecimalFormat percentFormat = new DecimalFormat("0.00%");


        for (Map.Entry<Long, HspOrganizationDto> entry : hspOrganizationDtoByHspOrgIdMap.entrySet()) {
            Long hspOrgId = entry.getKey();
            HspOrganizationDto hspOrganizationDto = entry.getValue();

            final String customerName = arithmeticalDiscountCommand.getCustomer(customerNameType, hspOrganizationDto);

            List<SampleTestItemDto> sampleTestItemDtos = baseDataByHspOrgIdMap.get(hspOrgId);

            if (CollectionUtils.isEmpty(sampleTestItemDtos)) {
                targetList.add(this.getHspOrgSendDoctorStatisticsResponseDto(hspOrgId, customerName, dateRange));
                continue;
            }

            // 申请单 + 检验项目+相同单价+相同折扣率 分组
            Map<String, List<SampleTestItemDto>> groupingByLineKey = sampleTestItemDtos.stream()
                    .collect(Collectors.groupingBy(obj -> obj.getApplyId() + "-" + obj.getTestItemId() + "-"
                            + obj.getPrice().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP) + "-"
                            + obj.getDiscount().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP)));

            // 数量合计、 合计金额合计、结算金额合计
            // 拆分为行 一个申请单下相同检验项目、相同金额、相同折扣率 是一行 数据
            List<HspOrgSendDoctorStatisticsItemDto> itemDtoList = Lists.newArrayList();
            final Result result = this.getResult(groupingByLineKey, percentFormat, itemDtoList);

            List<HspOrgSendDoctorStatisticsItemDto> sorted = itemDtoList.stream()
                    .peek(item -> {
                        if (Objects.equals(item.getSendDate(), DateUtil.formatDate(DefaultDateEnum.DEFAULT_DATE.getDate()))) {
                            item.setSendDate(StringUtils.EMPTY);
                        }
                    })
                    .sorted(Comparator.nullsLast(
                            Comparator.comparing(HspOrgSendDoctorStatisticsItemDto::getSendDate)
                                    .thenComparing(HspOrgSendDoctorStatisticsItemDto::getPatientName)
                                    .thenComparing(HspOrgSendDoctorStatisticsItemDto::getApplyTypeName)))
                    .collect(Collectors.toList());

            HspOrgSendDoctorStatisticsResponseDto target = new HspOrgSendDoctorStatisticsResponseDto();
            target.setHspOrgId(hspOrgId);
            target.setCustomerName(customerName);
            target.setStartDate(dateRange.getStartDate());
            target.setEndDate(dateRange.getEndDate());
            target.setItemList(sorted);
            target.setCountSum(result.countSumAll);
            target.setFeePriceSum(result.feePriceSumAll.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            target.setPayAmountSum(result.payAmountSumAll.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            target.setTotalFeePriceSum(result.totalFeePriceSum.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            targetList.add(target);
        }

        // 收集结果
        final SampleEsQuery dto = from.getSampleEsQuery();
        HspOrgSendDoctorStatisticsResponseDto target = new HspOrgSendDoctorStatisticsResponseDto();
        target.setStartDate(FinancialManagementServiceImpl.getDateRange(dto).getStartDate());
        target.setEndDate(FinancialManagementServiceImpl.getDateRange(dto).getEndDate());
        target.setOrgList(targetList);
        target.setCountSum(targetList.stream().mapToInt(HspOrgSendDoctorStatisticsResponseDto::getCountSum).sum());
        target.setFeePriceSum(targetList.stream().reduce(BigDecimal.ZERO, (a, b) -> a.add(b.getFeePriceSum()), BigDecimal::add));
        target.setTotalFeePriceSum(targetList.stream().reduce(BigDecimal.ZERO, (a, b) -> a.add(b.getTotalFeePriceSum()), BigDecimal::add));
        target.setPayAmountSum(targetList.stream().reduce(BigDecimal.ZERO, (a, b) -> a.add(b.getPayAmountSum()), BigDecimal::add));
        target.setCustomerName("");
        target.setOrgId((Long)from.get(BySendDoctorContext.ORG_ID));

        from.put(BySendDoctorContext.RESULT, target);

        return CONTINUE_PROCESSING;
    }

    private Result getResult(Map<String, List<SampleTestItemDto>> groupingByLineKey, DecimalFormat percentFormat, List<HspOrgSendDoctorStatisticsItemDto> itemDtoList) {
        Integer countSumAll = NumberUtils.INTEGER_ZERO;
        BigDecimal feePriceSumAll = BigDecimal.ZERO;
        BigDecimal totalFeePriceSum = BigDecimal.ZERO;
        BigDecimal payAmountSumAll = BigDecimal.ZERO;


        for (Map.Entry<String, List<SampleTestItemDto>> line : groupingByLineKey.entrySet()) {
            List<SampleTestItemDto> summaryItemList = line.getValue();

            SampleTestItemDto model = summaryItemList.get(NumberUtils.INTEGER_ZERO);

            // 未免单 金额合计 用于结算金额
            BigDecimal payAmount = summaryItemList.stream()
                    .filter(obj -> !Objects.equals(obj.getIsFree(), YesOrNoEnum.YES.getCode())).map(item -> {
                        BigDecimal price = item.getPrice();
                        if (item.isSpecialOfferFlag()) {
                            // 该样本检验项目 参与了特价项目 结算金额为折后价格
                            price = ObjectUtils.defaultIfNull(item.getDiscountPrice(), BigDecimal.ZERO);
                            return price
                                    .multiply(BigDecimal
                                            .valueOf(ObjectUtils.defaultIfNull(item.getCount(), NumberUtils.INTEGER_ONE)))
                                    .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
                        }
                        return price
                                .multiply(
                                        BigDecimal.valueOf(ObjectUtils.defaultIfNull(item.getCount(), NumberUtils.INTEGER_ONE)))
                                .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).multiply(model.getDiscount())
                                .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
                    }).reduce(BigDecimal.ZERO, BigDecimal::add);

            int countSum = summaryItemList.stream()
                    .map(obj -> ObjectUtils.defaultIfNull(obj.getCount(), NumberUtils.INTEGER_ONE))
                    .mapToInt(Integer::intValue).sum();

            HspOrgSendDoctorStatisticsItemDto itemDto = new HspOrgSendDoctorStatisticsItemDto();
            itemDto.setHspOrgName(model.getHspOrgName());
            itemDto.setOutBarcode(model.getOutBarcode());
            itemDto.setPatientVisitCard(model.getPatientVisitCard());
            itemDto.setPatientName(model.getPatientName());
            itemDto.setPatientAge(PatientAges.toText(model));
            itemDto.setPatientSex(SexEnum.getByCode(model.getPatientSex()).getDesc());
            itemDto.setSendDate(DateUtil.formatDate(model.getSignDate()));
            if (Objects.nonNull(model.getFinalCheckDate())
                    && !Objects.equals(DefaultDateEnum.DEFAULT_DATE.getDate(), model.getFinalCheckDate())) {
                itemDto.setFinalCheckDate(DateUtil.formatDate(model.getFinalCheckDate()));
            }
            itemDto.setDept(model.getDept());
            itemDto.setSendDoctorName(model.getSendDoctorName());
            itemDto.setApplyTypeName(model.getApplyTypeName());
            itemDto.setTestItemCode(model.getTestItemCode());
            itemDto.setTestItem(model.getTestItemName());
            itemDto.setCount(countSum);
            itemDto.setFeePrice(model.getPrice().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            itemDto.setTotalFeePrice(itemDto.getFeePrice().multiply(BigDecimal.valueOf(itemDto.getCount()))
                    .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
            itemDto.setDiscount(percentFormat.format(model.getDiscount()));
            itemDto.setPayAmount(payAmount);

            // 填充统计样本所需字段信息
            itemDto.setBarcode(model.getBarcode());
            itemDto.setTestItemName(model.getTestItemName());
            itemDto.setOutTestItemCode(model.getOutTestItemCode());
            itemDto.setOutTestItemName(model.getOutTestItemName());
            itemDto.setHspOrgCode(model.getHspOrgCode());
            itemDto.setPatientBirthday(model.getPatientBirthday());
            itemDto.setPatientBed(model.getPatientBed());
            itemDto.setCreateDate(model.getCreateDate());
            itemDto.setApplyDate(model.getApplyDate());
//            itemDto.setSendDate(model.getSendDate());
            itemDto.setSendDoctorCode(model.getSendDoctorCode());
            itemDto.setTestDate(model.getTestDate());
            itemDto.setTesterId(model.getTesterId());
            itemDto.setTesterName(model.getTesterName());
            itemDto.setFinalCheckerId(model.getFinalCheckerId());
            itemDto.setFinalCheckerName(model.getFinalCheckerName());
            itemDto.setIsSplitBlood(model.getIsSplitBlood());
            itemDto.setApplySampleId(model.getApplySampleId());
            itemDto.setFinalCheckDateTime(model.getFinalCheckDateTime());


            itemDtoList.add(itemDto);

            countSumAll = countSumAll + itemDto.getCount();
            feePriceSumAll = feePriceSumAll.add(model.getPrice());
            payAmountSumAll = payAmountSumAll.add(itemDto.getPayAmount());
            totalFeePriceSum = totalFeePriceSum.add(itemDto.getTotalFeePrice());
        }
        return new Result(countSumAll, feePriceSumAll, totalFeePriceSum, payAmountSumAll);
    }

    private static class Result {
        public final Integer countSumAll;
        public final BigDecimal feePriceSumAll;
        public final BigDecimal totalFeePriceSum;
        public final BigDecimal payAmountSumAll;

        public Result(Integer countSumAll, BigDecimal feePriceSumAll, BigDecimal totalFeePriceSum, BigDecimal payAmountSumAll) {
            this.countSumAll = countSumAll;
            this.feePriceSumAll = feePriceSumAll;
            this.totalFeePriceSum = totalFeePriceSum;
            this.payAmountSumAll = payAmountSumAll;
        }
    }

    private HspOrgSendDoctorStatisticsResponseDto getHspOrgSendDoctorStatisticsResponseDto(Long hspOrgId, String customerName, FinancialManagementServiceImpl.DateRange dateRange) {
        HspOrgSendDoctorStatisticsResponseDto target = new HspOrgSendDoctorStatisticsResponseDto();
        target.setHspOrgId(hspOrgId);
        target.setCustomerName(customerName);
        target.setStartDate(dateRange.getStartDate());
        target.setEndDate(dateRange.getEndDate());
        target.setItemList(Collections.emptyList());
        target.setCountSum(NumberUtils.INTEGER_ZERO);
        target.setFeePriceSum(BigDecimal.ZERO);
        target.setPayAmountSum(BigDecimal.ZERO);
        target.setTotalFeePriceSum(BigDecimal.ZERO);
        return target;
    }


    public static FinancialManagementServiceImpl.DateRange getDateRange(SampleEsQuery dto) {
        FinancialManagementServiceImpl.DateRange dateRange = new FinancialManagementServiceImpl.DateRange();
        // 时间开始结束
        String startDate = StringUtils.EMPTY;
        String endDate = StringUtils.EMPTY;
        if (Objects.nonNull(dto.getStartFinalCheckOrCreateDate())
                && Objects.nonNull(dto.getEndFinalCheckOrCreateDate())) {
            startDate = DateUtil.formatDate(dto.getStartFinalCheckOrCreateDate());
            endDate = DateUtil.formatDate(dto.getEndFinalCheckOrCreateDate());
        }

        if (StringUtils.isAnyBlank(startDate, endDate)) {
            if (Objects.nonNull(dto.getStartCreateDate()) && Objects.nonNull(dto.getEndCreateDate())) {
                startDate = DateUtil.formatDate(dto.getStartCreateDate());
                endDate = DateUtil.formatDate(dto.getEndCreateDate());
            }
        }

        dateRange.setStartDate(startDate);
        dateRange.setEndDate(endDate);
        return dateRange;
    }

}