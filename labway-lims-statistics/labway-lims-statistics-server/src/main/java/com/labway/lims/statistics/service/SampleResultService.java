package com.labway.lims.statistics.service;

import com.labway.lims.statistics.vo.InspectionResultQueryVo;
import com.labway.lims.statistics.vo.InspectionResultSummaryVo;
import com.labway.lims.statistics.vo.InspectionResultVo;
import com.labway.lims.statistics.vo.ReportInfoVo;

import java.util.List;

/**
 * <pre>
 * SampleResultService
 * 检验结果信息查询
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/5/13 16:00
 */
public interface SampleResultService {

    InspectionResultVo inspectionResultsNew(InspectionResultQueryVo vo);

    InspectionResultSummaryVo inspectionResultSummary(InspectionResultQueryVo queryDto);

    void filterResultRecord(InspectionResultQueryVo vo, List<ReportInfoVo> hospitals);
}
