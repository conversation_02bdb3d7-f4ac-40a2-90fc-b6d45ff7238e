package com.labway.lims.statistics.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.EsConfig;
import com.labway.lims.api.enums.PdfTemplateTypeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.StopTestStatus;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ByPlatformStatisticsItemDto;
import com.labway.lims.apply.api.dto.ByPlatformStatisticsResponseDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SampleTestItemDto;
import com.labway.lims.apply.api.dto.TestItemIncomeFilterDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.ItemSendPriceDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.ItemSendPriceService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.dto.OutOrgStatisticsItemDTO;
import com.labway.lims.statistics.enums.DisplayOrgType;
import com.labway.lims.statistics.enums.ExportNameType;
import com.labway.lims.statistics.enums.ExportType;
import com.labway.lims.statistics.excel.DetectionMergeStrategy;
import com.labway.lims.statistics.service.FinancialManagementService;
import com.labway.lims.statistics.service.byPlatform.ByPlatformStatisticsService;
import com.labway.lims.statistics.vo.FinanceStatisticsQueryVo;
import com.labway.lims.statistics.vo.OutOrgStatisticsItemVo;
import com.labway.lims.statistics.vo.OutOrgStatisticsVo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@RequestMapping("/finance-statistics")
@RestController
@RefreshScope
public class FinanceStatisticsController extends BaseController implements ApplicationContextAware {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;

    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @DubboReference
    private ItemSendPriceService itemSendPriceService;

    @DubboReference
    private PdfReportService pdfReportService;

    @Resource
    private FinancialManagementService financialManagementService;

    @Resource
    private EsConfig esConfig;


    @Value("${statistics.by-plat.version:1.0}")
    private String byPlatVersion;

    private static final Map<String, ByPlatformStatisticsService> BY_PLATFORM_STATISTICS_SERVICE_MAP = new HashMap<>();

    /**
     * 按平台统计返回 pdf url
     */
    @PostMapping("/by-platform-statistics-to-pdf")
    public Object byPlatformStatisticsToPdf(@RequestBody FinanceStatisticsQueryVo query) throws IOException {

        ByPlatformStatisticsResponseDto target = byPlatformStatistics(query);

        final PdfReportParamDto param = new PdfReportParamDto();
        param.put("target", target);

        // 生成的汇总保存 180 天
        final String pdfUrl =
                pdfReportService.build2Url(PdfTemplateTypeEnum.BY_PLATFORM_STATISTICS.getCode(), param, 3);

        log.info("用户 [{}] 分平台统计模版 PDF [{}]", LoginUserHandler.get().getNickname(), pdfUrl);

        return Map.of("url", pdfUrl);

    }

    /**
     * 按平台统计 excel
     */
    @PostMapping("/by-platform-statistics-export")
    public ResponseEntity<StreamingResponseBody> byPlatformStatisticsExport(@RequestBody FinanceStatisticsQueryVo query) throws IOException {
        ByPlatformStatisticsResponseDto target = byPlatformStatistics(query);

        switch (DisplayOrgType.getByType(query.getDisplayOrgType())) {
            case SINGLE:// 单个机构
                if (target.getOrgList().size() == 1) {
                    return byPlatformExportCurrent(target.getOrgList().get(0));
                }

                // 导出zip文件名
                final String zipFileName = String.format("%s至%s分平台统计.zip",
                        DateUtil.format(query.getStartDate(), "yyyy-MM-dd"),
                        DateUtil.format(query.getEndDate(), "yyyy-MM-dd"));

                return ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_DISPOSITION,
                                String.format("attachment; filename=%s", URLEncoder.encode(zipFileName, StandardCharsets.UTF_8)))
                        .header(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION, "filename")
                        .header("filename", URLEncoder.encode(zipFileName, StandardCharsets.UTF_8))
                        .contentType(new MediaType("application", "zip"))
                        .body((StreamingResponseBody) os -> {
                            try (ZipOutputStream zipOutputStream = ZipUtil.getZipOutputStream(os, StandardCharsets.UTF_8)) {
                                Set<String> exist = new HashSet<>();
                                int i = 1;

                                for (ByPlatformStatisticsResponseDto responseDto : target.getOrgList()) {
                                    String filename = String.format("%s%s至%s分平台统计.xlsx",
                                            responseDto.getCustomerName(), responseDto.getStartDate().substring(0, 10), responseDto.getEndDate().substring(0, 10));

                                    if (!exist.add(filename)) {
                                        filename = String.format("(%s)", i++) + filename;
                                    }
                                    // 添加 Excel 文件到 ZIP
                                    zipOutputStream.putNextEntry(new ZipEntry(URLDecoder.decode(filename, StandardCharsets.UTF_8)));
                                    try (final InputStream templateInputStream = new ClassPathResource("template/分平台统计导出模板.xlsx").getInputStream();
                                            ExcelWriter excelWriter = EasyExcelFactory.write(zipOutputStream).autoCloseStream(false).excelType(ExcelTypeEnum.XLSX).withTemplate(templateInputStream).build()) {
                                        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                                        // 填充 Sheet 的数据
                                        WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
                                        // 列表数据
                                        excelWriter.fill(responseDto.getOutOrgStatisticsItems(), fillConfig, writeSheet);
                                        // 其他数据
                                        excelWriter.fill(responseDto, writeSheet);
                                        excelWriter.finish();
                                    } catch (Exception e) {
                                        log.error("下载模板错误", e);
                                        throw new LimsException(e.getMessage(), e);
                                    }

                                    zipOutputStream.closeEntry();
                                }
                            }
                        });
            case MERGE:// 机构合并
                List<Integer> mergeRows = new ArrayList<>();
                int headerRow = 6;
                List<ByPlatformStatisticsItemDto> outOrgStatisticsItems = new ArrayList<>();
                // 只导出包含数据的机构
                target.getOrgList().stream().filter(org -> CollectionUtils.isNotEmpty(org.getOutOrgStatisticsItems())).forEach(org -> {
                    // 机构下的所有数据
                    outOrgStatisticsItems.addAll(org.getOutOrgStatisticsItems());
                    mergeRows.add(headerRow + outOrgStatisticsItems.size());
                    // 添加小计行
                    ByPlatformStatisticsItemDto summaryDto = new ByPlatformStatisticsItemDto();
                    summaryDto.setHspOrgName("客户小计");
                    summaryDto.setCount(org.getCountSum());
                    summaryDto.setPayAmountBefore(org.getPayAmountBeforeSum());
                    summaryDto.setPayAmountAfter(org.getPayAmountAfterSum());
                    outOrgStatisticsItems.add(summaryDto);
                });
                target.setItemList(outOrgStatisticsItems);
                target.setCustomerName("");

                return byPlatformExportMerge(target, new DetectionMergeStrategy(mergeRows, null, 4));
        }

        return null;
    }

    /**
     * 导出，机构合并
     * @param target
     * @param mergeStrategy
     * @return
     */
    private static ResponseEntity<StreamingResponseBody> byPlatformExportMerge(ByPlatformStatisticsResponseDto target, DetectionMergeStrategy mergeStrategy) {
        // 获取模板文件
        final InputStream templateInputStream;
        try {
            templateInputStream = new ClassPathResource("template/分平台统计导出模板.xlsx").getInputStream();
        } catch (Exception e) {
            throw new IllegalStateException("模板文件获取失败");
        }

        final String filename = String.format("%s%s至%s分平台统计合并导出.xlsx",
                target.getCustomerName(), target.getStartDate().substring(0, 10), target.getEndDate().substring(0, 10));

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode(filename, StandardCharsets.UTF_8)))
                .header(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION, "filename")
                .header("filename", URLEncoder.encode(filename, StandardCharsets.UTF_8))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body((StreamingResponseBody) os -> {
                    try (ExcelWriter excelWriter =
                                 EasyExcelFactory.write(os).autoCloseStream(false).excelType(ExcelTypeEnum.XLSX).withTemplate(templateInputStream)
                                         .registerWriteHandler(mergeStrategy).build()) {
                        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                        // 填充 Sheet 的数据
                        WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
                        // 列表数据
                        excelWriter.fill(target.getItemList(), fillConfig, writeSheet);
                        // 其他数据
                        excelWriter.fill(target, writeSheet);
                        excelWriter.finish();
                    } catch (Exception e) {
                        log.error("下载模板错误", e);
                        throw new LimsException(e.getMessage(), e);
                    }
                });
    }

    private static ResponseEntity<StreamingResponseBody> byPlatformExportCurrent(ByPlatformStatisticsResponseDto target) {
        // 获取模板文件
        final InputStream templateInputStream;
        try {
            templateInputStream = new ClassPathResource("template/分平台统计导出模板.xlsx").getInputStream();
        } catch (Exception e) {
            throw new IllegalStateException("模板文件获取失败");
        }

        final String filename = String.format("%s%s至%s分平台统计.xlsx",
                target.getCustomerName(), target.getStartDate().substring(0, 10), target.getEndDate().substring(0, 10));

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode(filename, StandardCharsets.UTF_8)))
                .header(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION, "filename")
                .header("filename", URLEncoder.encode(filename, StandardCharsets.UTF_8))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body(out -> {
                    try (ExcelWriter excelWriter =
                                 EasyExcelFactory.write(out).autoCloseStream(false).excelType(ExcelTypeEnum.XLSX).withTemplate(templateInputStream).build()) {

                        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();

                        // 填充 Sheet 的数据
                        WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();

                        // 列表数据
                        excelWriter.fill(target.getOutOrgStatisticsItems(), fillConfig, writeSheet);

                        // 其他数据
                        excelWriter.fill(target, writeSheet);

                        excelWriter.finish();
                    } catch (Exception e) {
                        log.error("下载模板错误", e);
                        throw new LimsException(e.getMessage(), e);
                    }
                });
    }

    /**
     * 按平台统计
     */
    @PostMapping("/by-platform-statistics")
    public ByPlatformStatisticsResponseDto byPlatformStatistics(@RequestBody FinanceStatisticsQueryVo query) {
        ByPlatformStatisticsResponseDto vo = new ByPlatformStatisticsResponseDto();
        if (Objects.isNull(query.getStartDate()) || Objects.isNull(query.getEndDate())) {
            return vo;
        }
        return getByplatformStatisticsService().byPlatformStatistics(query);
    }

    /**
     * 外送机构统计 pdf url
     */
    @PostMapping("/out-org-statistics-to-pdf")
    public Object outOrgStatisticsToPdf(@RequestBody FinanceStatisticsQueryVo query) {
        String templateCode = "OUT_ORG_STATISTICS";
        final OutOrgStatisticsVo outOrgStatisticsVo = outOrgStatistics(query);
        outOrgStatisticsVo.setItems(ObjectUtils.defaultIfNull(outOrgStatisticsVo.getItems(), new HashMap<>()));
        final HashMap<Object, Object> defaultValue = new HashMap<>();

        final PdfReportParamDto param = new PdfReportParamDto();
        param.put("startDate", DateUtil.formatDate(query.getStartDate()));
        param.put("endDate", DateUtil.formatDate(query.getEndDate()));
        param.put("dataMap", ObjectUtils.defaultIfNull(outOrgStatisticsVo.getItems(), defaultValue));
        param.put("hspOrgMap", ObjectUtils.defaultIfNull(outOrgStatisticsVo.getHspOrganizationMap(), defaultValue));
        param.put("type", query.getInvoiceType());

        final String url = pdfReportService.build2Url(templateCode, param, 3);

        return Map.of("url", url);
    }

    /**
     * 单个外送机构统计导出 excel
     */
    @PostMapping("/out-only-org-statistics-export")
    public ResponseEntity<org.springframework.core.io.Resource> outOnlyOrgStatisticsExport(@RequestBody FinanceStatisticsQueryVo query, HttpServletResponse response) {

        // 创建一个临时目录来存储生成的Excel文件
        File tempDir = new File(System.getProperty("java.io.tmpdir") + "\\" + UUID.randomUUID());
        try {
            // 准备数据
            final OutOrgStatisticsVo outOrgStatisticsVo = outOrgStatistics(query);
            final List<OutOrgStatisticsItemDTO> itemsList =
                    ObjectUtils.defaultIfNull(outOrgStatisticsVo.getItemsList(), Collections.emptyList());

            boolean mkdir = tempDir.mkdir();

            // 使用模板的 InputStream 填充数据并生成 Excel 文件
            for (OutOrgStatisticsItemDTO outOrgStatisticsItemDTO : itemsList) {
                String customerName = outOrgStatisticsItemDTO.getExportOrgName();
                // 获取模板文件
                InputStream finalStream;
                try (InputStream inputStream = new ClassPathResource("template/外送机构统计导出模板.xlsx").getInputStream()) {
                    ByteArrayOutputStream out = new ByteArrayOutputStream();
                    XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
                    int index = 0;
                    workbook.setSheetName(index, customerName);
                    // set sheetName
                    workbook.setSheetName(index, customerName);
                    workbook.write(out);
                    byte[] bArray = out.toByteArray();
                    finalStream = new ByteArrayInputStream(bArray);

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    throw new IllegalStateException("模板文件获取失败");
                }

                final String fileName = outOrgStatisticsItemDTO.getExportOrgName() + DateUtil.formatDate(outOrgStatisticsVo.getStartDate()) + "至" + DateUtil.formatDate(outOrgStatisticsVo.getEndDate()) + "外送统计";
                // 创建临时文件夹
                try (OutputStream excelOutputStream = new FileOutputStream(tempDir + "/" + fileName + ".xlsx")) {

                    // 使用 EasyExcel 填充数据到 Excel 文件
                    ExcelWriter excelWriter = EasyExcel.write(excelOutputStream).withTemplate(finalStream).build();
                    WriteSheet writeSheet = EasyExcel.writerSheet().build();
                    FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                    // 列表数据
                    excelWriter.fill(outOrgStatisticsItemDTO.getOutOrgStatisticsItems(), fillConfig, writeSheet);
                    // 总计数据
                    Map<String, Object> totalRow = new HashMap<>();
                    totalRow.put("sumCount", outOrgStatisticsItemDTO.getCount());
                    excelWriter.fill(totalRow, writeSheet);
                    // 标题信息
                    Map<String, Object> fillMap = MapUtils.newHashMap();
                    fillMap.put("customerName", customerName);
                    fillMap.put("startDate", DateUtil.formatDate(outOrgStatisticsVo.getStartDate()));
                    fillMap.put("endDate", DateUtil.formatDate(outOrgStatisticsVo.getEndDate()));
                    excelWriter.fill(fillMap, writeSheet);
                    excelWriter.finish();

                    // 如果是导出部分,导出对应的部分excel
                    if ((CollectionUtils.isNotEmpty(query.getExportOrgIds()) && query.getExportOrgIds().size() == 1) ||
                            (Objects.equals(ExportType.CURRENT.getType(), query.getExportType()) && query.getExportOrgIds().contains(outOrgStatisticsItemDTO.getExportOrgId()))
                    ) {
                        return ResponseEntity.ok()
                                .header(HttpHeaders.CONTENT_DISPOSITION, String.format("attachment; filename=%s", URLEncoder.encode(fileName + ".xlsx", StandardCharsets.UTF_8)))
                                .header(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION, "filename")
                                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                                .body(new FileSystemResource(tempDir + "/" + fileName + ".xlsx"));
                    }
                }
            }


            // 创建 ZIP 文件并将所有 Excel 文件添加到 ZIP 中
            final String zipFileName = DateUtil.formatDate(outOrgStatisticsVo.getStartDate()) + "至" + DateUtil.formatDate(outOrgStatisticsVo.getEndDate()) + "外送统计.zip";
            File zipFileOutputStream = File.createTempFile(DateUtil.formatDate(outOrgStatisticsVo.getStartDate()) + "至" + DateUtil.formatDate(outOrgStatisticsVo.getEndDate()) + "外送统计", "zip");
            OutputStream bufferedOutputStream = new FileOutputStream(zipFileOutputStream);
            ZipOutputStream zipOutputStream = new ZipOutputStream(bufferedOutputStream);
            File[] excelFiles = tempDir.listFiles();

            if (excelFiles != null) {
                for (File excelFile : excelFiles) {
                    String fileName = excelFile.getName();
                    FileInputStream excelFileInputStream = new FileInputStream(excelFile);

                    // 添加 Excel 文件到 ZIP
                    zipOutputStream.putNextEntry(new ZipEntry(fileName));
                    IOUtils.copy(excelFileInputStream, zipOutputStream);
                    zipOutputStream.closeEntry();
                    // 关闭 Excel 文件输入流
                    excelFileInputStream.close();
                }
            }
            // 关闭 ZIP 输出流
            zipOutputStream.close();

            // 构建 ResponseEntity 返回 ZIP 文件给前端
            InputStream zipInputStream = new FileInputStream(zipFileOutputStream);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", URLEncoder.encode(zipFileName, StandardCharsets.UTF_8));
            org.springframework.core.io.Resource zipResource = new InputStreamResource(zipInputStream);
            return ResponseEntity.ok()
                    .headers(headers)
                    .header(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION, "filename")
                    .body(zipResource);
        } catch (IOException e) {
            log.info("导出文件失败{}", e.getMessage(), e);
            throw new IllegalStateException("导出文件失败");
        }finally {
            tempDir.delete();
        }
    }

    /**
     * 合并外送机构统计导出 excel
     */
    @PostMapping("/out-merge-org-statistics-export")
    public ResponseEntity<org.springframework.core.io.Resource> outMergeOrgStatisticsExport(@RequestBody FinanceStatisticsQueryVo query, HttpServletResponse response) throws IOException {
        final OutOrgStatisticsVo outOrgStatisticsVo = outOrgStatistics(query);
        final List<OutOrgStatisticsItemDTO> itemsList =
                ObjectUtils.defaultIfNull(outOrgStatisticsVo.getItemsList(), Collections.emptyList());
        // 查询一下全部送检机构
        final List<HspOrganizationDto> hspOrganizationDtoList= hspOrganizationService.selectAll().stream()
                .filter(f -> Objects.equals(f.getIsExport(), YesOrNoEnum.YES.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hspOrganizationDtoList)){
            throw new IllegalStateException("没有送检机构");
        }
        final String customerName = CollectionUtils.isEmpty(query.getExportOrgIds()) || Objects.equals(hspOrganizationDtoList.size(),query.getExportOrgIds()) ?
                ExportNameType.ALL.getValue() : ExportNameType.PARTIAL.getValue();

        InputStream finalStream;
        try (InputStream inputStream = new ClassPathResource("template/外送机构统计导出模板.xlsx").getInputStream()) {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            // set sheetName
            workbook.setSheetName(0, customerName);
            workbook.write(out);
            byte[] bArray = out.toByteArray();
            finalStream = new ByteArrayInputStream(bArray);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new IllegalStateException("模板文件获取失败");
        }
        final File tempFile = File.createTempFile("out-org-statistics-export-", null);

        // 要填充的数据
        List<OutOrgStatisticsItemVo> outOrgStatisticsItems = new ArrayList<>();
        List<Integer> mergeRow = new ArrayList<>();
        int headerRow = 6;
        for (OutOrgStatisticsItemDTO outOrgStatisticsItemDTO : itemsList) {
            List<OutOrgStatisticsItemVo> outOrgStatisticsItemsDtos = outOrgStatisticsItemDTO.getOutOrgStatisticsItems();
            if (CollectionUtils.isNotEmpty(outOrgStatisticsItemsDtos)) {
                outOrgStatisticsItems.addAll(outOrgStatisticsItemsDtos);
                mergeRow.add(headerRow + outOrgStatisticsItems.size());
                OutOrgStatisticsItemVo outOrgStatisticsItemVo = new OutOrgStatisticsItemVo();
                outOrgStatisticsItemVo.setExportOrgName("小计");
                outOrgStatisticsItemVo.setCount(outOrgStatisticsItemDTO.getCount());
                outOrgStatisticsItems.add(outOrgStatisticsItemVo);
            }
        }

        try (ExcelWriter excelWriter = EasyExcelFactory.write(tempFile).registerWriteHandler(new DetectionMergeStrategy(mergeRow, null, 8)).withTemplate(finalStream).build()) {
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(0).build();
            // 列表数据
            excelWriter.fill(outOrgStatisticsItems, fillConfig, writeSheet);
            // 总计数据
            Map<String, Object> totalRow = new HashMap<>();
            totalRow.put("sumCount", itemsList.stream().mapToInt(OutOrgStatisticsItemDTO::getCount).sum());
            excelWriter.fill(totalRow, writeSheet);
            // 标题信息
            Map<String, Object> fillMap = MapUtils.newHashMap();
            fillMap.put("customerName", customerName);
            fillMap.put("startDate", DateUtil.formatDate(outOrgStatisticsVo.getStartDate()));
            fillMap.put("endDate", DateUtil.formatDate(outOrgStatisticsVo.getEndDate()));
            excelWriter.fill(fillMap, writeSheet);
            excelWriter.finish();
        } finally {
            finalStream.close();
        }
        final String fileName = customerName + DateUtil.formatDate(outOrgStatisticsVo.getStartDate()) + "至" + DateUtil.formatDate(outOrgStatisticsVo.getEndDate()) + "外送统计.xlsx";
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode(fileName, StandardCharsets.UTF_8)))
                .header(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION, "filename")
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body(new FileSystemResource(tempFile));
    }

    /**
     * 外送机构统计
     */
    @PostMapping("/out-org-statistics")
    public OutOrgStatisticsVo outOrgStatistics(@RequestBody FinanceStatisticsQueryVo query) {
        final OutOrgStatisticsVo outOrgStatisticsVo = new OutOrgStatisticsVo();

        if (Objects.isNull(query.getStartDate()) || Objects.isNull(query.getEndDate())) {
            return outOrgStatisticsVo;
        }

        // 默认查询时间类型
        query.setDateType(ObjectUtils.defaultIfNull(query.getDateType(), DateTypeEnum.SEND_DATE.code));

        // 默认开票名称
        query.setInvoiceType(ObjectUtils.defaultIfNull(query.getInvoiceType(), InvoiceTypeEnum.INVOICE_NAME.code));

        // final SampleEsQuery build = SampleEsQuery.builder().isAudit(YesOrNoEnum.YES.getCode())
        // .itemTypes(Set.of(ItemTypeEnum.OUTSOURCING.name())).build();
        // 1.0.6.6需求改为不管该样本是否已经审核都进行查询
        final SampleEsQuery build =
                SampleEsQuery.builder().isAudit(null).itemTypes(Set.of(ItemTypeEnum.OUTSOURCING.name())).build();

        if (Objects.equals(query.getDateType(), DateTypeEnum.SEND_DATE.code)) {
            // 送检时间的就是样本创建时间
            build.setStartCreateDate(query.getStartDate());
            build.setEndCreateDate(query.getEndDate());
        } else {
            build.setStartFinalCheckOrCreateDate(query.getStartDate());
            build.setEndFinalCheckOrCreateDate(query.getEndDate());
        }
        Set<Long> exportOrgIds = query.getExportOrgIds();
        final Map<Long,
                HspOrganizationDto> hspOrganizationMap = hspOrganizationService.selectAll().stream()
                .filter(f -> Objects.equals(f.getIsExport(), YesOrNoEnum.YES.getCode())
                        && (CollectionUtils.isEmpty(exportOrgIds) || exportOrgIds.contains(f.getHspOrgId()))
                )
                .collect(Collectors.toMap(HspOrganizationDto::getHspOrgId, Function.identity(), (a, b) -> a));


        if (MapUtil.isEmpty(hspOrganizationMap)) {
            throw new IllegalStateException("没有外送机构");
        }

        // 最小 最大的检验项目时间
        var ref = new Object() {
            Date maxDate = null;
            Date minDate = null;
        };

        // 分页信息
        build.setPageSize(esConfig.getPageSize());

        final Set<Long> allSendOrgIds = query.getExportOrgIds();
        SampleEsQuery outsourcingSampleEsQuery = JSON.parseObject(JSON.toJSONString(build), SampleEsQuery.class);

        // 设置外送结构统计 - 外送机构、送检机构、检验项目 ES过滤条件
//        outsourcingSampleEsQuery.setExportOrgIds(query.getExportOrgIds());
        outsourcingSampleEsQuery.setTestItemIds(query.getTestItemIds());
        outsourcingSampleEsQuery.setHspOrgIds(query.getHspOrgIds());
        final List<BaseSampleEsModelDto> allSamples =
                elasticSearchSampleService.selectSamples(outsourcingSampleEsQuery);

        // 查询 终止收费的检验项目信息 不一定是审核的
        SampleEsQuery stopStatusEsQuery =
                JSON.parseObject(JSON.toJSONString(outsourcingSampleEsQuery), SampleEsQuery.class);
        stopStatusEsQuery.setIsAudit(null);
        stopStatusEsQuery.setStopStatus(Set.of(StopTestStatus.STOP_TEST_CHARGE.getCode()));
        List<BaseSampleEsModelDto> stopTestChargeEsModelDtos =
                elasticSearchSampleService.selectSamples(stopStatusEsQuery);
        List<SampleTestItemDto> sampleTestItemDtos = financialManagementService
                .handleEsDataToSampleTestItemDto(allSamples, stopTestChargeEsModelDtos, Collections.emptyList(),
                        new TestItemIncomeFilterDto(), false);
        if(CollectionUtils.isNotEmpty(allSendOrgIds)){
            sampleTestItemDtos = sampleTestItemDtos.stream().filter(e->allSendOrgIds.contains(e.getExportHspOrgId())).collect(Collectors.toList());
        }
        List<OutOrgStatisticsItemVo> vos = sampleTestItemDtos
                .stream().filter(obj -> Objects.nonNull(obj.getExportHspOrgId())).map(m -> {
                    OutOrgStatisticsItemVo vo = new OutOrgStatisticsItemVo();
                    vo.setExportOrgName(m.getExportHspOrgName());
                    vo.setExportOrgId(m.getExportHspOrgId());
                    vo.setCustomerName(m.getHspOrgName());
                    vo.setCustomerId(m.getHspOrgId());
                    vo.setPatientName(m.getPatientName());
                    vo.setInspectionDate(DateUtil.formatDate(m.getCreateDate()));
                    vo.setProjectId(m.getTestItemId());
                    vo.setProjectCode(m.getTestItemCode());
                    vo.setProjectName(m.getTestItemName());
                    vo.setBlueprintPrice(m.getPrice());
                    BigDecimal price = m.getPrice();
                    if (m.isSpecialOfferFlag()) {
                        // 该样本检验项目 参与了特价项目 结算金额为折后价格
                        price = ObjectUtils.defaultIfNull(m.getDiscountPrice(), BigDecimal.ZERO);
                        vo.setDiscountedPrice(price);
                    } else {
                        vo.setDiscountedPrice(ObjectUtils.defaultIfNull(price, BigDecimal.ZERO).multiply(m.getDiscount()));
                    }
                    if (Objects.equals(m.getIsFree(), YesOrNoEnum.YES.getCode())) {
                        vo.setDiscountedPrice(BigDecimal.ZERO);
                    }
                    vo.setDeliveryPrice(new BigDecimal("0"));
                    vo.setCount(ObjectUtils.defaultIfNull(m.getCount(), NumberUtils.INTEGER_ONE));
                    vo.setTestItemCreateDate(m.getCreateDate());
                    vo.setApplyTypeName(m.getApplyTypeName());
                    vo.setApplyTypeCode(m.getApplyTypeCode());

                    final Date createDate = vo.getTestItemCreateDate();
                    if (ref.minDate == null || createDate.before(ref.minDate)) {
                        ref.minDate = createDate;
                    }
                    if (ref.maxDate == null || createDate.after(ref.maxDate)) {
                        ref.maxDate = createDate;
                    }
                    return vo;
                }).collect(Collectors.toList());

        // 外送价格
        final Map<Long,
                Map<Long, List<ItemSendPriceDto>>> itemSendPriceMap = itemSendPriceService
                .selectByHspOrgIdsAndDateRange(allSendOrgIds, ref.minDate, ref.maxDate).stream()
                .collect(Collectors.groupingBy(ItemSendPriceDto::getHspOrgId,
                        Collectors.mapping(Function.identity(), Collectors.groupingBy(ItemSendPriceDto::getTestItemId))));

        final Map<Long,
                List<OutOrgStatisticsItemVo>> outSampleMap = vos.stream()
                .peek(m -> m.setDeliveryPrice(
                        ObjectUtils.defaultIfNull(calculateDeliveryPrice(m, itemSendPriceMap), m.getBlueprintPrice())))
                .collect(Collectors.groupingBy(OutOrgStatisticsItemVo::getExportOrgId));

        Set<Long> hspOrganizationIds = hspOrganizationMap.keySet();
        ArrayList<OutOrgStatisticsItemDTO> outOrgStatisticsItemDTOS = new ArrayList<OutOrgStatisticsItemDTO>();

     /*   String customerName = Objects.equals(query.getInvoiceType(), InvoiceTypeEnum.INVOICE_NAME.code)
                ? outOrgStatisticsItemDTO.getInvoice() : outOrgStatisticsItemDTO.getExportOrgName();
        customerName = StringUtils.defaultIfBlank(customerName, outOrgStatisticsItemDTO.getExportOrgName() + "(开发票名称)");
*/
        hspOrganizationIds.forEach(h -> {
            OutOrgStatisticsItemDTO outOrgStatisticsItemDTO = new OutOrgStatisticsItemDTO();
            List<OutOrgStatisticsItemVo> outOrgStatisticsItemVos = outSampleMap.get(h);
            outOrgStatisticsItemDTO.setOutOrgStatisticsItems(ObjectUtils.defaultIfNull(outOrgStatisticsItemVos, Collections.emptyList()));
            outOrgStatisticsItemDTO.setExportOrgId(h);

            // 根据发票名称/单位名称修改对应的送检机构
            HspOrganizationDto hspOrganizationDto = hspOrganizationMap.get(h);
            String customerName = Objects.equals(query.getInvoiceType(), InvoiceTypeEnum.INVOICE_NAME.code)
                    ? hspOrganizationDto.getInvoice() : hspOrganizationDto.getHspOrgName();
            customerName = StringUtils.defaultIfBlank(customerName, hspOrganizationDto.getHspOrgName());

            outOrgStatisticsItemDTO.setExportOrgName(customerName);
            outOrgStatisticsItemDTO.setInvoice(hspOrganizationDto.getInvoice());
            if (CollectionUtils.isNotEmpty(outOrgStatisticsItemVos)) {
                outOrgStatisticsItemDTO.setCount(outOrgStatisticsItemVos.stream()
                        .mapToInt(OutOrgStatisticsItemVo::getCount)
                        .sum());
            } else {
                outOrgStatisticsItemDTO.setCount(0);
            }
            outOrgStatisticsItemDTOS.add(outOrgStatisticsItemDTO);
        });
        outOrgStatisticsVo.setStartDate(query.getStartDate());
        outOrgStatisticsVo.setEndDate(query.getEndDate());
        // 外送机构列表
        outOrgStatisticsVo.setHspOrganizationMap(ObjectUtils.defaultIfNull(hspOrganizationMap, Collections.emptyMap()));
        // 外送机构统计列表
        outOrgStatisticsVo.setItemsList(ObjectUtils.defaultIfNull(outOrgStatisticsItemDTOS, Collections.emptyList()));
        // 外送机构样本列表
        outOrgStatisticsVo.setItems(ObjectUtils.defaultIfNull(outSampleMap, Collections.emptyMap()));
        return outOrgStatisticsVo;
    }

    /**
     * 获取外送价
     */
    private BigDecimal calculateDeliveryPrice(OutOrgStatisticsItemVo result,
                                              Map<Long, Map<Long, List<ItemSendPriceDto>>> itemSendPriceMap) {
        final Long testItemId = result.getProjectId();
        final Date testItemCreateDate = result.getTestItemCreateDate();
        if (Objects.isNull(testItemCreateDate)) {
            return null;
        }
        final Map<Long, List<ItemSendPriceDto>> itemSendPriceDtoMap =
                itemSendPriceMap.getOrDefault(result.getExportOrgId(), Collections.emptyMap());
        final List<ItemSendPriceDto> itemSendPrices =
                itemSendPriceDtoMap.getOrDefault(testItemId, Collections.emptyList());

        // 过滤出时间段范围内的
        return itemSendPrices.stream()
                .filter(m -> m.getStartDate().before(testItemCreateDate) && m.getEndDate().after(testItemCreateDate))
                .findFirst().map(ItemSendPriceDto::getSendPrice).orElse(null);
    }

    private ByPlatformStatisticsService getByplatformStatisticsService(){
        log.info("分平台统计version： {}", byPlatVersion);
        final ByPlatformStatisticsService byPlatformStatisticsService = BY_PLATFORM_STATISTICS_SERVICE_MAP.get(byPlatVersion);
        if(Objects.isNull(byPlatformStatisticsService)){
            throw new IllegalArgumentException("NACOS 配置错误");
        }
        return byPlatformStatisticsService;
    }
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        final Map<String, ByPlatformStatisticsService> beansOfType = applicationContext.getBeansOfType(ByPlatformStatisticsService.class);
        beansOfType.values().forEach(bean -> {
            final String beanName = bean.version();
            BY_PLATFORM_STATISTICS_SERVICE_MAP.put(beanName, bean);
        });
    }

    @AllArgsConstructor
    @Getter
    public enum DateTypeEnum {

        /**
         * 1 送检时间
         */
        SEND_DATE(1, "送检时间"),
        /**
         * 2 审核时间
         */
        AUDIT_DATE(2, "审核时间"),

        ;

        private final Integer code;
        private final String desc;

    }

    @AllArgsConstructor
    @Getter
    enum InvoiceTypeEnum {

        /**
         * 开票名称
         */
        INVOICE_NAME(1, "开票名称"),

        /**
         * 单位名称
         */
        ORG_NAME(2, "单位名称"),
        ;

        private final Integer code;

        private final String desc;

    }
}
