package com.labway.lims.statistics.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.labway.lims.api.config.EsConfig;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.DictEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.ScrollPage;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.base.api.dto.DictItemDto;
import com.labway.lims.base.api.service.DictService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.vo.ProjectAndApplyTypeRespVo;
import com.labway.lims.statistics.vo.SendItemAndApplyTypeVo;
import com.labway.lims.statistics.vo.SendItemQueryVo;
import com.labway.lims.statistics.vo.SendItemStatisticsRespVo;
import com.labway.lims.statistics.vo.SendItemStatisticsVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 送检项目统计
 */
@RestController
@RequestMapping("/send-project-statistics")
public class SendProjectStatisticsController extends BaseController {

    @DubboReference
    private DictService dictService;
    @Resource
    private EsConfig esConfig;

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;


    // region -------------------------------- 送检项目查询 -----------------------------


    /**
     * 项目及就诊类型统计 导出
     */
    @PostMapping("/project-and-apply-type-export")
    public Object projectAndApplyTypeExport(@RequestBody SendItemQueryVo queryVo) throws IOException {
        final Collection<SendItemAndApplyTypeVo> vos =
                ObjectUtils.defaultIfNull(projectAndApplyType(queryVo).getList(), new ArrayList<>());

        final File tempFile = File.createTempFile("send-item-and-apply-type-export-", null);


        try (final ExcelWriter writer = ExcelUtil.getBigWriter(); final FileOutputStream fos = new FileOutputStream(tempFile)) {
            LinkedList<Object> builder = new LinkedList<>(List.of(
                    //
                    "送检机构",
                    //
                    "签收时间",
                    //
                    "检验项目名称",
                    //
                    "就诊类型",
                    //
                    "签收数量",
                    //
                    "审核数量"
            ));

            writer.writeHeadRow(builder);

            for (final SendItemAndApplyTypeVo vo : vos) {
                builder.clear();
                builder.add(vo.getHspOrgName());
                builder.add(vo.getDate());
                builder.add(vo.getTestItemName());
                builder.add(vo.getApplyTypeName());
                builder.add(vo.getSignCount());
                builder.add(vo.getAuditCount());
                writer.writeRow(builder);
            }

            writer.flush(fos);
        }

        return ResponseEntity.ok().header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("项目及就诊类型统计.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel")).body(new FileSystemResource(tempFile));
    }

    /**
     * 项目及就诊类型统计
     */
    @PostMapping("/project-and-apply-type")
    public ProjectAndApplyTypeRespVo projectAndApplyType(@RequestBody SendItemQueryVo queryVo) {
        queryVo.defaultDate();
        // 查询条件
        AtomicInteger pageNo = new AtomicInteger(1);
        final SampleEsQuery query = SampleEsQuery.builder()
                .pageSize(10000)
                .startCreateDate(queryVo.getStartDate())
                .endCreateDate(queryVo.getEndDate())
                .build();

        final List<String> applyTypes = queryVo.getApplyTypes();
        final List<Long> testItemIds = queryVo.getTestItemIds();

        if (CollectionUtils.isNotEmpty(testItemIds)) {
            query.setTestItemIds(new HashSet<>(testItemIds));
        }

        if (Objects.nonNull(queryVo.getHspOrgId())) {
            query.setHspOrgIds(Set.of(queryVo.getHspOrgId()));
        }

        if (CollectionUtils.isNotEmpty(applyTypes)) {
            query.setApplyTypes(new HashSet<>(applyTypes));
        }

        // 这里的key跟value相同的原因是,是为了后面方便去重;value 方便获取数据
        final Map<SendItemAndApplyTypeVo, SendItemAndApplyTypeVo> sendItemStatisticsMap = new HashMap<>(20);

        // 查询所有的就诊类型
        final Map<String, DictItemDto> dictItemMap = dictService.selectByDictType(DictEnum.VISIT_TYPE.name()).stream().collect(Collectors.toMap(DictItemDto::getDictCode, v -> v, (a, b) -> a));

        final AtomicInteger auditTotal = new AtomicInteger();
        final AtomicInteger signTotal = new AtomicInteger();

        List<Object> searchAfter = null;
        do {
            // 自增页码
            query.setPageNo(pageNo.getAndIncrement());

            final ScrollPage<? extends BaseSampleEsModelDto> page = elasticSearchSampleService.searchAfter(searchAfter, query);
            searchAfter = page.getSearchAfter();
            final List<? extends BaseSampleEsModelDto> datas = page.getData();
            if (CollectionUtils.isEmpty(datas)) {
                break;
            }

            for (final BaseSampleEsModelDto data : datas) {
                final List<BaseSampleEsModelDto.TestItem> testItems = ObjectUtils.defaultIfNull(data.getTestItems(), new ArrayList<>());
                final Long hspOrgId = data.getHspOrgId();
                final String hspOrgName = data.getHspOrgName();
                final String applyType = data.getApplyTypeCode();
                final String signDate = DateUtil.format(data.getCreateDate(), "yyyy-MM-dd");
                final Integer sampleStatus = data.getSampleStatus();


                DictItemDto dictItem = dictItemMap.get(applyType);
                if (Objects.isNull(dictItem)) {
                    final DictItemDto unknown = new DictItemDto();
                    unknown.setDictCode("unknown");
                    unknown.setDictName("未知");
                    dictItem = unknown;
                }

                for (final BaseSampleEsModelDto.TestItem testItem : testItems) {
                    // 检验项目匹配
                    if (CollectionUtils.isNotEmpty(testItemIds) && !testItemIds.contains(testItem.getTestItemId())) {
                        continue;
                    }

                    final SendItemAndApplyTypeVo tmp = new SendItemAndApplyTypeVo();
                    tmp.setHspOrgId(hspOrgId);
                    tmp.setHspOrgName(hspOrgName);
                    tmp.setTestItemId(testItem.getTestItemId());
                    tmp.setTestItemName(testItem.getTestItemName());
                    tmp.setDate(signDate);
                    tmp.setApplyTypeCode(dictItem.getDictCode());
                    tmp.setApplyTypeName(dictItem.getDictName());
                    tmp.setSignCount(new AtomicInteger());
                    tmp.setAuditCount(new AtomicInteger());

                    final SendItemAndApplyTypeVo vo = sendItemStatisticsMap.computeIfAbsent(tmp, k -> tmp);

                    // 签收数量+1
                    vo.getSignCount().getAndIncrement();
                    signTotal.incrementAndGet();
                    // 审核数量+1E
                    if (Objects.equals(sampleStatus, SampleStatusEnum.AUDIT.getCode())) {
                        vo.getAuditCount().getAndIncrement();
                        auditTotal.incrementAndGet();
                    }
                }
            }

        } while (!Thread.currentThread().isInterrupted());

        ProjectAndApplyTypeRespVo respVo = new ProjectAndApplyTypeRespVo();
        respVo.setList(sendItemStatisticsMap.values().stream()
                .sorted(Comparator.comparing(SendItemAndApplyTypeVo::getHspOrgName)
                        .thenComparing(SendItemAndApplyTypeVo::getDate))
                .collect(Collectors.toList()));
        respVo.setAuditTotal(auditTotal);
        respVo.setSignTotal(signTotal);
        return respVo;
    }

    /**
     * 项目统计 导出
     */
    @PostMapping("/export")
    public ResponseEntity<org.springframework.core.io.Resource> export(@RequestBody SendItemQueryVo queryVo) throws IOException {

        final Collection<SendItemStatisticsVo> vos = ObjectUtils.defaultIfNull(query(queryVo).getList(), new ArrayList<>());

        final File tempFile = File.createTempFile("send-item-statistics-export-", null);

        List<List<String>> head = List.of(List.of("序号"), List.of("签收时间"), List.of("检验项目名称"), List.of("签收个数"), List.of("审核个数"));

        EasyExcelFactory
                .write(tempFile)
                .head(head)
                .registerWriteHandler(new MergeColRowWriteHandler())
                .registerWriteHandler(new AbstractColumnWidthStyleStrategy() {
                    @Override
                    protected void setColumnWidth(CellWriteHandlerContext context) {

                        context.getWriteSheetHolder().getSheet().setColumnWidth(context.getColumnIndex(), 5000);

                    }
                })
                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 30, (short) 30))
                .autoCloseStream(Boolean.TRUE)
                .sheet("sheet1")
                .doWrite(sendItemStatisticsExportConvert(vos));


        return ResponseEntity.ok().header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("项目统计.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel")).body(new FileSystemResource(tempFile));
    }

    /**
     * 项目统计
     */
    @PostMapping("/query")
    public SendItemStatisticsRespVo query(@RequestBody SendItemQueryVo queryVo) {

        queryVo.defaultDate();
        // 查询条件
        AtomicInteger pageNo = new AtomicInteger(1);
        final SampleEsQuery query = SampleEsQuery.builder()
                .pageSize(esConfig.getMaxPageSize())
                .startCreateDate(queryVo.getStartDate())
                .endCreateDate(queryVo.getEndDate())
                .build();

        final List<String> applyTypes = queryVo.getApplyTypes();
        final List<Long> testItemIds = queryVo.getTestItemIds();

        if (CollectionUtils.isNotEmpty(testItemIds)) {
            query.setTestItemIds(new HashSet<>(testItemIds));
        }

        if (CollectionUtils.isNotEmpty(applyTypes)) {
            query.setApplyTypes(new HashSet<>(applyTypes));
        }

        if (Objects.nonNull(queryVo.getHspOrgId())) {
            query.setHspOrgIds(Collections.singleton(queryVo.getHspOrgId()));
        }

        Map<Long, SendItemStatisticsVo> sendItemStatisticsMap = new HashMap<>(20);
        List<Object> searchAfter = null;

        // 合计数量
        AtomicInteger auditTotal = new AtomicInteger(0);
        AtomicInteger signTotal = new AtomicInteger(0);

        do {
            // 自增页码
            query.setPageNo(pageNo.getAndIncrement());

            final ScrollPage<? extends BaseSampleEsModelDto> page = elasticSearchSampleService.searchAfter(searchAfter, query);
            searchAfter = page.getSearchAfter();
            final List<? extends BaseSampleEsModelDto> datas = page.getData();
            if (CollectionUtils.isEmpty(datas)) {
                break;
            }

            // 聚合数据
            for (final BaseSampleEsModelDto data : datas) {
                final List<BaseSampleEsModelDto.TestItem> testItems = ObjectUtils.defaultIfNull(data.getTestItems(), new ArrayList<>());
                final Long hspOrgId = data.getHspOrgId();
                final String hspOrgName = data.getHspOrgName();
                final Integer sampleStatus = data.getSampleStatus();
                String signDate = DateUtil.format(data.getCreateDate(), "yyyy-MM-dd");

                for (final BaseSampleEsModelDto.TestItem testItem : testItems) {
                    // 页面过滤条件 检验项目在es中的样本属性是个List 所以直接通过testItemId不行，还需要手动过滤掉
                    final Long testItemId = testItem.getTestItemId();
                    if (CollectionUtils.isNotEmpty(testItemIds) && !testItemIds.contains(testItemId)) {
                        continue;
                    }

                    // 给默认值
                    final SendItemStatisticsVo sendItemStatisticsVo = sendItemStatisticsMap.computeIfAbsent(hspOrgId, k -> {
                        final SendItemStatisticsVo item = new SendItemStatisticsVo();
                        item.setHspOrgName(hspOrgName);
                        item.setHspOrgId(hspOrgId);

                        item.setItems(new HashSet<>());
                        item.setAuditCount(new AtomicInteger(0));
                        item.setSignCount(new AtomicInteger(0));

                        return item;
                    });

                    // 签收数量
                    sendItemStatisticsVo.getSignCount().incrementAndGet();

                    // 时间 + 检验项目id统计
                    final Set<SendItemStatisticsVo.SendItemStatisticsItem> items = sendItemStatisticsVo.getItems();
                    final SendItemStatisticsVo.SendItemStatisticsItem item = new SendItemStatisticsVo.SendItemStatisticsItem();
                    item.setDate(signDate);
                    item.setTestItemId(testItemId);
                    item.setTestItemName(testItem.getTestItemName());
                    item.setSignCount(new AtomicInteger(0));
                    item.setAuditCount(new AtomicInteger(0));

                    // 如果没有则生成一个
                    final SendItemStatisticsVo.SendItemStatisticsItem setItem =
                            items.stream().filter(f -> Objects.equals(f, item)).findFirst().orElse(item);

                    setItem.getSignCount().incrementAndGet();
                    signTotal.incrementAndGet();

                    if (Objects.equals(sampleStatus, SampleStatusEnum.AUDIT.getCode())) {
                        // 审核通过数量

                        // 合计
                        auditTotal.incrementAndGet();

                        // 机构合计
                        sendItemStatisticsVo.getAuditCount().incrementAndGet();

                        // 时间+项目 合计
                        setItem.getAuditCount().incrementAndGet();
                    }

                    // 重新添加
                    items.add(setItem);
                }

            }

        } while (!Thread.currentThread().isInterrupted());

        SendItemStatisticsRespVo respVo = new SendItemStatisticsRespVo();
        respVo.setList(sendItemStatisticsMap.values()
                .stream().sorted(Comparator.comparing(SendItemStatisticsVo::getHspOrgName)).collect(Collectors.toList()));
        respVo.setAuditTotal(auditTotal);
        respVo.setSignTotal(signTotal);

        return respVo;
    }

    //endregion -------------------------------送检项目查询-----------------------------


    public List<List<Object>> sendItemStatisticsExportConvert(final Collection<SendItemStatisticsVo> vos) {
        List<List<Object>> rows = new LinkedList<>();

        AtomicInteger index = new AtomicInteger();

        for (final SendItemStatisticsVo vo : vos) {

            final Set<SendItemStatisticsVo.SendItemStatisticsItem> items = vo.getItems();
            if (CollectionUtils.isEmpty(items)) {
                continue;
            }

            rows.add(List.of(String.format("  %s                 签收个数：%s              审核个数：%s", vo.getHspOrgName(), vo.getSignCount(), vo.getAuditCount())));

            final List<List<Object>> row = items.stream().map(m -> {
                final int i = index.incrementAndGet();

                List<Object> itemRow = new LinkedList<>();
                //
                itemRow.add(i);
                //
                itemRow.add(StringUtils.defaultString(m.getDate()));
                //
                itemRow.add(StringUtils.defaultString(m.getTestItemName()));
                //
                itemRow.add(ObjectUtils.defaultIfNull(m.getSignCount(), new AtomicInteger()).get());
                //
                itemRow.add(ObjectUtils.defaultIfNull(m.getAuditCount(), new AtomicInteger()).get());

                return itemRow;
            }).collect(Collectors.toList());

            rows.addAll(row);
        }

        return rows;
    }

    /**
     * 合并列 如果列头数量 + 实际的数据列数量 不等于 endCol 则合并
     */
    public static final class MergeColRowWriteHandler implements RowWriteHandler {


        @Override
        public void afterRowDispose(RowWriteHandlerContext context) {


            if (BooleanUtils.isTrue(context.getHead())) {
                return;
            }

            final int headSize = context.getWriteSheetHolder().getHead().size();

            final Row row = context.getRow();
            if (row.getLastCellNum() != 1 && (Objects.equals(headSize, 0) || Objects.equals(headSize, (int) row.getLastCellNum()))) {
                return;
            }

            CellRangeAddress cellRangeAddress = new CellRangeAddress(context.getRowIndex(), context.getRowIndex(), 0, headSize - 1);
            context.getWriteSheetHolder().getSheet().addMergedRegionUnsafe(cellRangeAddress);

        }

    }

}
