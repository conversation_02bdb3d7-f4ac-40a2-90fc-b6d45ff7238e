package com.labway.lims.statistics.service;

import com.labway.lims.apply.api.dto.OutsourcingTestItemStatisticsDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.TestItemIncomeFilterDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;

import java.util.List;

/**
 * <pre>
 * OutsourcingTestItemStatisticsService
 * 外送项目统计
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/2/18 13:18
 */
public interface OutsourcingTestItemStatisticsService {

    OutsourcingTestItemStatisticsDto getOutsourcingTestItemStatistics(
            SampleEsQuery dto, TestItemIncomeFilterDto filterDto, List<BaseSampleEsModelDto> baseSampleEsModelDtosAll);

}
