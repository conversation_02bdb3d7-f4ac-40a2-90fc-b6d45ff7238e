package com.labway.lims.statistics.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.OutsourcingTestItemStatisticsDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.TestItemIncomeFilterDto;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.service.OutsourcingTestItemStatisticsService;
import com.labway.lims.statistics.vo.OutsourcingTestItemStatisticsRequestVo;
import com.labway.lims.statistics.vo.OutsourcingTestItemStatisticsVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <pre>
 * OutsourcingTestItemStatisticsController
 * 外送项目统计
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/2/18 11:49
 */
@RestController
@RequestMapping("/outsourcing-testitem-statistics")
public class OutsourcingTestItemStatisticsController {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;
    @Resource
    private OutsourcingTestItemStatisticsService outsourcingTestItemStatisticsService;

    @PostMapping("/statistics")
    public OutsourcingTestItemStatisticsVo statistics(@RequestBody OutsourcingTestItemStatisticsRequestVo vo) {
        if (Objects.isNull(vo.getBeginTwoPickDate()) || Objects.isNull(vo.getEndTwoPickDate())) {
            throw new IllegalStateException("日期错误");
        }

        final SampleEsQuery query = buildQuery(vo);
        query.setItemTypes(Set.of(ItemTypeEnum.OUTSOURCING.name()));
        final List<BaseSampleEsModelDto> sampleList = elasticSearchSampleService.selectSamples(query);

        OutsourcingTestItemStatisticsDto outsourcingTestItemStatistics =
                outsourcingTestItemStatisticsService.getOutsourcingTestItemStatistics(query, new TestItemIncomeFilterDto(), sampleList);
        return JSON.parseObject(JSON.toJSONString(outsourcingTestItemStatistics), OutsourcingTestItemStatisticsVo.class);
    }

    private SampleEsQuery buildQuery(OutsourcingTestItemStatisticsRequestVo vo) {
        final SampleEsQuery query = new SampleEsQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNo(NumberUtils.INTEGER_ONE);

        // 外送分拣时间
        query.setStartTwoPickDate(vo.getBeginTwoPickDate());
        query.setEndTwoPickDate(vo.getEndTwoPickDate());
        query.setNotnullFields(Set.of("exportOrgId"));

        // 外送机构
        if (CollectionUtils.isNotEmpty(vo.getExportOrgIds())) {
            query.setExportOrgIds(vo.getExportOrgIds());
        }

        // 检验项目
        if (CollectionUtils.isNotEmpty(vo.getTestItemCodes())) {
            query.setTestItemCodes(vo.getTestItemCodes());
        }

        return query;
    }

}
