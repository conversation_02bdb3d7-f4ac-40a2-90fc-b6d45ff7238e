package com.labway.lims.statistics.controller;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.OutsourcingInspectionDto;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.vo.OutsourcingTatStatisticsRequestVo;
import com.labway.lims.statistics.vo.OutsourcingTatStatisticsVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 外送 TaT统计
 */
@RestController
@RequestMapping("/outsourcing-tat-statistics")
public class OutsourcingTatStatisticsController extends BaseController {

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;

    /**
     * 统计
     */
    @PostMapping("/statistics")
    public List<OutsourcingTatStatisticsVo> statistics(@RequestBody OutsourcingTatStatisticsRequestVo vo) {

        if (Objects.isNull(vo.getBeginSignDate()) || Objects.isNull(vo.getEndSignDate())) {
            throw new IllegalStateException("日期错误");
        }

        final SampleEsQuery query = new SampleEsQuery();
        query.setStartCreateDate(vo.getBeginSignDate());
        query.setEndCreateDate(vo.getEndSignDate());

        if (Objects.nonNull(vo.getExportOrgId())) {
            query.setExportOrgIds(Set.of(vo.getExportOrgId()));
        }

        if (Objects.nonNull(vo.getTestItemId())) {
            query.setTestItemIds(Set.of(vo.getTestItemId()));
        }

        if (StringUtils.isNotBlank(vo.getBarcode())) {
            query.setBarcodes(Set.of(vo.getBarcode()));
        }

        if (StringUtils.isNotBlank(vo.getPatientName())) {
            query.setPatientName(vo.getPatientName());
        }

        query.setItemTypes(Set.of(ItemTypeEnum.OUTSOURCING.name()));

        final List<OutsourcingInspectionDto> samples = elasticSearchSampleService.selectSamples(query)
                // 只要外送样本
                .stream().filter(OutsourcingInspectionDto.class::isInstance)
                // 转成外送样本
                .map(e -> (OutsourcingInspectionDto) e)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(samples)) {
            return Collections.emptyList();
        }

        return samples.stream().map(e -> {
            final OutsourcingTatStatisticsVo v = new OutsourcingTatStatisticsVo();

            BeanUtils.copyProperties(e, v);
            v.setTestItemNames(Collections.emptyList());

            // 终审时间
            v.setCheckDate(e.getFinalCheckDate());

            if (Objects.equals(e.getSampleStatus(), SampleStatusEnum.AUDIT.getCode())) {
                v.setBackReportDate(e.getTestDate());
            }

            if (Objects.equals(e.getIsOnePick(), YesOrNoEnum.YES.getCode())) {
                v.setHandoverDate(e.getOnePickDate());
            }

            if (Objects.equals(e.getIsTwoPick(), YesOrNoEnum.YES.getCode())) {
                v.setPickDate(e.getTwoPickDate());
            }

            if (CollectionUtils.isNotEmpty(e.getTestItems())) {
                v.setTestItemNames(e.getTestItems().stream().map(BaseSampleEsModelDto.TestItem::getTestItemName)
                        .collect(Collectors.toList()));
            }

            return v;
        }).collect(Collectors.toList());
    }


}
