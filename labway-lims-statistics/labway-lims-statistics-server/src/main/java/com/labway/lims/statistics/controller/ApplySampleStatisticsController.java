package com.labway.lims.statistics.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Lists;
import com.labway.business.center.compare.request.compare.UpdateApplySampleInfoRequest;
import com.labway.business.center.compare.service.TbOrgApplySampleMainService;
import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.PatientAges;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplySampleItemBloodCultureDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.service.ApplySampleItemBloodCultureService;
import com.labway.lims.statistics.service.ApplySampleService;
import com.labway.lims.statistics.vo.ApplySamplePageVo;
import com.labway.lims.statistics.vo.ApplySampleQueryVo;
import com.labway.lims.statistics.vo.ApplySampleTestItemVo;
import com.labway.lims.statistics.vo.ApplySampleVo;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * ApplySampleStatisticsController
 * 样本信息查询
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/9 14:39
 */
@Slf4j
@RestController
@RequestMapping("/apply-sample-statistics")
public class ApplySampleStatisticsController extends BaseController {

    @Resource
    private ApplySampleService applySampleService;

    @DubboReference
    private ApplySampleItemBloodCultureService applySampleItemBloodCultureService;
    @Resource
    private TbOrgApplySampleMainService tbOrgApplySampleMainService;
    @Resource
    private EnvDetector envDetector;

    /**
     * 查询申请单样本信息
     * @param vo
     * @return
     */
    @PostMapping("select")
    public Object selectApplySample(@RequestBody ApplySampleQueryVo vo) {
        final SampleEsQuery sampleEsQuery = buildSampleEsQuery(vo);
        ApplySamplePageVo applySamplePageVo = applySampleService.selectApplySample(sampleEsQuery);
        if (Objects.isNull(applySamplePageVo) || CollectionUtils.isEmpty(applySamplePageVo.getApplySamples())){
            return Collections.emptyList();
        }
        Set<Long> applySampleIds = applySamplePageVo.getApplySamples().stream().map(ApplySampleVo::getApplySampleId).collect(Collectors.toSet());
        List<ApplySampleItemBloodCultureDto> applySampleItemBloodCultureDtos = applySampleItemBloodCultureService.selectApplySampleIds(applySampleIds);
        Map<Long, ApplySampleItemBloodCultureDto>  applySampleItemBloodCultureMap = applySampleItemBloodCultureDtos.stream()
                .collect(Collectors.toMap(ApplySampleItemBloodCultureDto::getApplySampleId, Function.identity(), (a, b) -> b));
        applySamplePageVo.getApplySamples().forEach(a -> {
            ApplySampleItemBloodCultureDto applySampleItemBloodCultureDto = applySampleItemBloodCultureMap.get(a.getApplySampleId());
            a.setHasBloodCultureItem(!Objects.isNull(applySampleItemBloodCultureDto));
        });
        return applySamplePageVo;
    }

    /**
     * 查询检验项目
     * @param applySampleId
     * @return
     */
    @GetMapping("testitem")
    public Object selectTestItem(@RequestParam Long applySampleId) {
        return applySampleService.selectTestItems(applySampleId);
    }


    /**
     * 申请单样本信息 导出
     */
    @PostMapping("/export-data")
    public Object exportData(@RequestBody ApplySampleQueryVo vo) {
        final SampleEsQuery sampleEsQuery = buildSampleEsQuery(vo);

        // 头的策略
        WriteCellStyle headCellStyle = new WriteCellStyle();
        headCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());

        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("微软雅黑");
        headWriteFont.setFontHeightInPoints((short) 13);
        headCellStyle.setWriteFont(headWriteFont);

        // 内容策略
        WriteCellStyle contentCellStyle = new WriteCellStyle();
        contentCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentCellStyle.setBorderLeft(BorderStyle.THIN);
        contentCellStyle.setBorderTop(BorderStyle.THIN);
        contentCellStyle.setBorderRight(BorderStyle.THIN);
        contentCellStyle.setBorderBottom(BorderStyle.THIN);

        HorizontalCellStyleStrategy excelStyle = new HorizontalCellStyleStrategy(headCellStyle, contentCellStyle);

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try (ExcelWriter excelWriter =
                     EasyExcelFactory.write(out).excelType(ExcelTypeEnum.XLSX).registerWriteHandler(excelStyle)
                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build()) {

            long count = applySampleService.selectApplySampleCount(sampleEsQuery);

            List<List<Object>> list0 = new ArrayList<>((int) count);
            List<List<String>> header0 = Lists.newArrayList();

            // 设置表头
            List<String> headList =
                    Lists.newArrayList(
                            "序号", "急诊状态", "送检机构", "外部条码号", "条码号", "主条码号",
                            "申请项目数量", "就诊类型", "门诊/住院号", "患者姓名", "性别", "年龄", "科室", "床号", "送检医生",
                            "签收人", "签收时间", "样本类型", "检验状态", "当前环节", "管型", "出生日期", "申请时间", "采集时间",
                            "身份证号", "电话", "收费状态");
            for (String item : headList) {
                header0.add(List.of(item));
            }

            if (count > 0) {
                Object searchAfter;
                List<ApplySampleVo> applySamples;
                sampleEsQuery.setPageSize(1000);
                sampleEsQuery.setSearchAfter(null);
                do {
                    ApplySamplePageVo samplePageVo = applySampleService.selectApplySample(sampleEsQuery);
                    applySamples = samplePageVo.getApplySamples();
                    searchAfter = samplePageVo.getSearchAfter();
                    // 设置 放置数据
                    fillExcelContent(list0, headList, applySamples);

                    // 分页参数
                    if (Objects.nonNull(searchAfter)) {
                        sampleEsQuery.setSearchAfter(Collections.singletonList(searchAfter));
                    }
                } while (Objects.nonNull(searchAfter) && CollectionUtils.isNotEmpty(applySamples));
            }

            // 获取sheet对象
            WriteSheet sheet0 =
                    EasyExcelFactory.writerSheet(0, "样本信息查询结果").head(header0).needHead(Boolean.TRUE).build();

            excelWriter.write(list0, sheet0);
        } catch (Exception e) {
            log.error("导出数据错误", e);
            throw new LimsException(e.getMessage(), e);
        }

        byte[] data = out.toByteArray();

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        String.format("attachment; filename=%s", URLEncoder.encode("样本信息查询结果信息.xlsx", StandardCharsets.UTF_8)))
                .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
                .body(data);
    }

    private static SampleEsQuery buildSampleEsQuery(ApplySampleQueryVo vo) {
        final SampleEsQuery.SampleEsQueryBuilder builder = SampleEsQuery.builder();
        builder.pageNo(NumberUtils.INTEGER_ZERO)
                .pageSize(Math.min(100, Objects.requireNonNullElse(vo.getPageSize(), 100)));
        SampleEsQuery.Sort sort = new SampleEsQuery.Sort();
        // fix: dev-1.1.1 签收时间因为批量复核会重复， 根据signDate的时间戳当游标分页查询可能会丢失数据
        // sort.setFiledName("signDate");
        sort.setFiledName("createDate");
        sort.setOrder("ASC");
        builder.sorts(List.of(sort));

        // 高级查询
        if (Objects.nonNull(vo.getAdvanceQuery()) && vo.getAdvanceQuery().checkParam()) {
            builder.advancedEsQuery(vo.getAdvanceQuery());
            builder.startCreateDate(vo.getStartSignDate()).endCreateDate(vo.getEndSignDate());
        } else {
            if (Objects.nonNull(vo.getStartSignDate()) && Objects.nonNull(vo.getEndSignDate())) {
                builder.startCreateDate(vo.getStartSignDate()).endCreateDate(vo.getEndSignDate());
            }

            // 条码号
            if (StringUtils.isNotBlank(vo.getBarcode())) {
                builder.barcodeOrOutbarcodes(Set.of(vo.getBarcode()));
            }
            // 主条码号
            if (StringUtils.isNotBlank(vo.getMasterBarcode())) {
                builder.masterBarcodes(Set.of(vo.getMasterBarcode()));
            }
            // 门诊/住院号
            if (StringUtils.isNotBlank(vo.getPatientVisitCard())) {
                builder.patientVisitCard(vo.getPatientVisitCard());
            }
            // 病人名称
            if (StringUtils.isNotBlank(vo.getPatientName())) {
                builder.patientName(vo.getPatientName());
            }
            // 检验状态：未复核、已签收、正在检验、完成检验
            if (Objects.nonNull(vo.getStatus())) {
                builder.status(vo.getStatus());
            }
            // 项目状态 0:正常，1:已终止（收费），2:已终止（未收费）,3:禁止
            if (Objects.nonNull(vo.getItemStatus())) {
                builder.itemStatus(vo.getItemStatus());
            }
            // 申请单id
            if (StringUtils.isNotBlank(vo.getApplyId())) {
                Set<Long> applyIds = new HashSet<>();
                applyIds.add(Long.valueOf(vo.getApplyId()));
                builder.applyIds(applyIds);
            }
        }

        // 分页参数
        List<Object> searchAfter = null;
        if (Objects.nonNull(vo.getSearchAfter()) && !String.valueOf(vo.getSearchAfter()).isEmpty()) {
            searchAfter = Collections.singletonList(vo.getSearchAfter());
        }
        builder.searchAfter(searchAfter);
        return builder.build();
    }

    /**
     * 填充 excel 内容数据
     *
     * @param list0 excel 数据
     * @param headList 表头
     * @param contentList 内容来源
     */
    private void fillExcelContent(List<List<Object>> list0, List<String> headList, List<ApplySampleVo> contentList) {
        if (CollectionUtils.isEmpty(contentList)) {
            return;
        }
        for (ApplySampleVo contentData : contentList) {
            List<Object> content = Lists.newArrayListWithCapacity(headList.size());

            content.add(list0.size() + 1);
            UrgentEnum urgentEnum = UrgentEnum.getUrgentEnum(contentData.getUrgent());
            content.add(Objects.nonNull(urgentEnum) ? urgentEnum.getValue() : StringPool.EMPTY);
            content.add(contentData.getHspOrgName());
            content.add(contentData.getOutBarcode());
            content.add(contentData.getBarcode());
            content.add(contentData.getMasterBarcode());
            content.add(contentData.getTestItemNum());
            content.add(contentData.getApplyTypeName());
            content.add(contentData.getPatientVisitCard());
            content.add(contentData.getPatientName());
            content.add(Objects.nonNull(contentData.getPatientSex()) ? SexEnum.getByCode(contentData.getPatientSex()).getDesc() : StringPool.EMPTY);
            content.add(PatientAges.toText(contentData));
            content.add(contentData.getDept());
            content.add(contentData.getPatientBed());
            content.add(contentData.getSendDoctorName());
            content.add(contentData.getSignName());
            content.add(Objects.nonNull(contentData.getSignDate()) ? DateFormatUtils.format(contentData.getSignDate(), DATE_PATTERN) : StringPool.EMPTY);
            content.add(contentData.getSampleTypeName());
            content.add(Objects.nonNull(contentData.getStatus()) ? SampleStatusEnum.getStatusByCode(contentData.getStatus()).getDesc() : StringPool.EMPTY); // 检验状态
            content.add(contentData.getBarcodeLink());
            content.add(contentData.getTubeName());
            content.add(Objects.nonNull(contentData.getPatientBirthday()) ? DateFormatUtils.format(contentData.getPatientBirthday(), DATE_PATTERN) : StringPool.EMPTY);
            content.add(Objects.nonNull(contentData.getApplyDate()) ? DateFormatUtils.format(contentData.getApplyDate(), DATETIME_PATTERN) : StringPool.EMPTY);
            content.add(Objects.nonNull(contentData.getSamplingDate()) ? DateFormatUtils.format(contentData.getSamplingDate(), DATETIME_PATTERN) : StringPool.EMPTY);
            content.add(contentData.getPatientCard());
            content.add(contentData.getPatientMobile());
            content.add(contentData.getStopStatusName());

            list0.add(content.stream().map(obj -> {
                if (Objects.isNull(obj)) {
                    return StringPool.EMPTY;
                }
                return obj;
            }).collect(Collectors.toList()));
        }
    }

    private static final String DATE_PATTERN = "yyyy-MM-dd";
    private static final String DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 通知业务中台，同步更新申请单信息
     */
    @PostMapping("/syncApplySample")
    public Object syncApplySample(@RequestBody ApplySampleQueryVo vo) {

        // 同步申请单需要根据申请单id查询，如果没传applyId 则不进行同步
        if (StringUtils.isBlank(vo.getApplyId())){
            return ApplySamplePageVo.builder().build();
        }

        final SampleEsQuery sampleEsQuery = buildSampleEsQuery(vo);
        ApplySamplePageVo applySamplePageVo = applySampleService.selectApplySample(sampleEsQuery);

        List<ApplySampleVo> applySamples = applySamplePageVo.getApplySamples();
        if (CollectionUtils.isEmpty(applySamplePageVo.getApplySamples())){
            return applySamples;
        }

        LoginUserHandler.User user = LoginUserHandler.get();

        // 调用业务中台更新申请单信息  因为是根据applyId查询的，所有只会有一条数据
        for (ApplySampleVo applySample : applySamples) {

            // 判断是否是病理样本
            List<ApplySampleTestItemVo> applySampleTestItemVos = applySampleService.selectTestItems(applySample.getApplySampleId());
            if (CollectionUtils.isEmpty(applySampleTestItemVos)
                || applySampleTestItemVos.stream().noneMatch(ApplySampleTestItemVo::getIsBingliItem)){
                log.info("当前申请单没有病理项目，不进行业务中台同步，申请单id：{},条码号：{}", vo.getApplyId(), applySample.getBarcode());
                continue;
            }

            Response<?> response = tbOrgApplySampleMainService.updateApplySampleInfo(convertSampleInfo(applySample,user));
            if (!response.isSuccess()){
                log.error("通知业务中台更新申请单信息失败,申请单id：{},条码号：{},原因：{}", vo.getApplyId(), applySample.getBarcode(), response.getMsg());
                return Response.fail(response.getCode(), response.getMsg());
            }
            log.info("通知业务中台更新申请单信息成功,申请单id：{},条码号：{}", vo.getApplyId(), applySample.getBarcode());
        }

        return applySamplePageVo;
    }


    // 申请单信息转换
    private UpdateApplySampleInfoRequest convertSampleInfo(ApplySampleVo applySampleVo,LoginUserHandler.User user) {

        UpdateApplySampleInfoRequest updateApplySampleInfoRequest = new UpdateApplySampleInfoRequest();
        updateApplySampleInfoRequest.setBarcode(applySampleVo.getOutBarcode());
        updateApplySampleInfoRequest.setHspOrgCode(applySampleVo.getHspOrgCode());
        updateApplySampleInfoRequest.setHspOrgName(applySampleVo.getHspOrgName());
        updateApplySampleInfoRequest.setApplyType(applySampleVo.getApplyTypeName());
        updateApplySampleInfoRequest.setPatientVisitCard(applySampleVo.getPatientVisitCard());
        updateApplySampleInfoRequest.setUrgent(applySampleVo.getUrgent());
        updateApplySampleInfoRequest.setSampleType(applySampleVo.getSampleTypeName());
        updateApplySampleInfoRequest.setSampleProperty(applySampleVo.getSampleProperty());
        updateApplySampleInfoRequest.setDept(applySampleVo.getDept());
//        updateApplySampleInfoRequest.setInpatientArea(applySampleVo.geta);
        updateApplySampleInfoRequest.setPatientName(applySampleVo.getPatientName());
        updateApplySampleInfoRequest.setPatientSex(applySampleVo.getPatientSex());
        updateApplySampleInfoRequest.setPatientAge(applySampleVo.getPatientAge());
        updateApplySampleInfoRequest.setPatientSubage(applySampleVo.getPatientSubage());
        updateApplySampleInfoRequest.setPatientSubageUnit(applySampleVo.getPatientSubageUnit());
        updateApplySampleInfoRequest.setPatientBirthday(applySampleVo.getPatientBirthday());
        updateApplySampleInfoRequest.setPatientBed(applySampleVo.getPatientBed());
        updateApplySampleInfoRequest.setClinicalDiagnosis(applySampleVo.getDiagnosis());
        updateApplySampleInfoRequest.setPatientCard(applySampleVo.getPatientCard());
        updateApplySampleInfoRequest.setPatientCardType(applySampleVo.getPatientCardType());
        updateApplySampleInfoRequest.setPatientAsddress(applySampleVo.getPatientAddress());
        updateApplySampleInfoRequest.setPatientMobile(applySampleVo.getPatientMobile());
        updateApplySampleInfoRequest.setSendDoctor(applySampleVo.getSendDoctorName());
        updateApplySampleInfoRequest.setApplyDate(applySampleVo.getApplyDate());
        updateApplySampleInfoRequest.setSamplingDate(applySampleVo.getSamplingDate());
        updateApplySampleInfoRequest.setRemark(applySampleVo.getRemark());
        updateApplySampleInfoRequest.setTubeType(applySampleVo.getTubeName());
//        updateApplySampleInfoRequest.setVisitCardNo(applySampleVo.getPatientVisitCard());
        updateApplySampleInfoRequest.setSampleNum(applySampleVo.getSampleCount());

        updateApplySampleInfoRequest.setSignBarcode(applySampleVo.getBarcode());
        updateApplySampleInfoRequest.setSignOrgCode(envDetector.getBusinessCenterOrgCode());
        updateApplySampleInfoRequest.setLimsUserCode(user.getUsername());
        updateApplySampleInfoRequest.setLimsUserName(user.getNickname());

        return updateApplySampleInfoRequest;
    }


}
