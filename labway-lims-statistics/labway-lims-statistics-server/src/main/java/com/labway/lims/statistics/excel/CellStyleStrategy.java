package com.labway.lims.statistics.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.AbstractCellStyleStrategy;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.impl.CTFontImpl;

import java.io.ObjectInputStream;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static javax.swing.text.StyleConstants.setBold;
import static org.apache.poi.ss.util.CellUtil.setFont;

@Slf4j
public class CellStyleStrategy extends HorizontalCellStyleStrategy {

    private final List<Integer> mergeRows; //检测方数据条数小计行数

    private Map<Sheet, Set<Integer>> MERGED_ROW = new HashMap<>();

    public CellStyleStrategy(List<Integer> mergeRows) {
        super(headWriteCellStyle(), contentWriteCellStyle());
        this.mergeRows = mergeRows;
    }

    public static WriteCellStyle headWriteCellStyle() {
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 标题字体大小
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontHeightInPoints((short) 12);
        headWriteCellStyle.setWriteFont(contentWriteFont);
        //标题黄色底纹
        headWriteCellStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
        return headWriteCellStyle;
    }

    public static WriteCellStyle contentWriteCellStyle() {
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //边框
        // contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        // contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        // contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        // contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        // 水平居中
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置自动换行，前提内容中需要加「\n」才有效
        // contentWriteCellStyle.setWrapped(true);

        WriteFont writeFont = new WriteFont();
        // writeFont.setBold(true);
        // writeFont.setColor(IndexedColors.RED.getIndex());
        contentWriteCellStyle.setWriteFont(writeFont);

        return contentWriteCellStyle;
    }

    @Override
    public void afterCellCreate(CellWriteHandlerContext context) {
        if (mergeRows.contains(context.getRow().getRowNum())) {
            Cell cell = context.getCell();
            CellStyle cellStyle = cell.getCellStyle();
            Font font = cell.getSheet().getWorkbook().createFont();
            font.setBold(true);
            font.setColor(IndexedColors.BLUE.getIndex());
            cellStyle.setFont(font);
            cell.setCellStyle(cellStyle);
        }
        super.afterCellCreate(context);
    }

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
        Sheet sheet = writeSheetHolder.getSheet();
        Font font = sheet.getWorkbook().createFont();
        CellStyle cellStyle = sheet.getWorkbook().createCellStyle();

        super.beforeCellCreate(writeSheetHolder, writeTableHolder, row, head, columnIndex, relativeRowIndex, isHead);
    }
}