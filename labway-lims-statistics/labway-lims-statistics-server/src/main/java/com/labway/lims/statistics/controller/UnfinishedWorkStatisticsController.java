package com.labway.lims.statistics.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.apply.RackLogicPositionEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleAbnormalDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.SampleRackPositionDto;
import com.labway.lims.apply.api.dto.ScrollPage;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.RackLogicSpaceService;
import com.labway.lims.apply.api.service.SampleAbnormalService;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.vo.UnfinishedWorkStatisticsQueryVo;
import com.labway.lims.statistics.vo.UnfinishedWorkVo;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 未完成工作统计
 */
@RequestMapping("/unfinished-work-statistics")
@RestController
public class UnfinishedWorkStatisticsController extends BaseController {

    private static final List<String> ITEM_TYPE_LIST =
        List.of(ItemTypeEnum.ROUTINE.name(), ItemTypeEnum.GENETICS.name(), ItemTypeEnum.SPECIALTY.name());

    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;

    @DubboReference
    private RackLogicSpaceService rackLogicSpaceService;

    @DubboReference
    private GroupService groupService;

    @DubboReference
    private ApplySampleService applySampleService;

    @DubboReference
    private SampleAbnormalService sampleAbnormalService;

    /**
     * 条码环节 下拉框
     */
    @GetMapping("/links")
    public Object links() {
        return Arrays.stream(Link.values()).map(m -> Map.of("code", m.name(), "desc", m.getDesc()))
            .collect(Collectors.toList());
    }

    /**
     * 查询
     */
    @PostMapping("/export")
    public ResponseEntity<org.springframework.core.io.Resource>
        export(@RequestBody UnfinishedWorkStatisticsQueryVo queryVo) throws IOException {

        final List<UnfinishedWorkVo> vos = query(queryVo);

        final File tempFile = File.createTempFile("unfinished-work-statistics-export", null);

        try (ExcelWriter writer = ExcelUtil.getBigWriter(); FileOutputStream fos = new FileOutputStream(tempFile)) {

            List<Object> builder = new LinkedList<>(List.of(
                //
                "序号",
                //
                "条码号",
                //
                "送检机构",
                //
                "患者姓名",
                //
                "性别",
                //
                "年龄",
                //
                "就诊类型",
                //
                "操作专业组",
                //
                "操作用户",
                //
                "当前环节",
                //
                "送检时间"));

            writer.writeHeadRow(builder);

            for (int i = 0; i < vos.size(); i++) {
                final UnfinishedWorkVo vo = vos.get(i);

                builder.clear();

                builder.add(i + 1);
                builder.add(vo.getBarcode());
                builder.add(vo.getHspOrgName());
                builder.add(vo.getPatientName());
                builder.add(SexEnum.getByCode(vo.getPatientSex()).getDesc());
                builder.add(
                    String.format("%s岁%s%s", vo.getPatientAge(), vo.getPatientSubage(), vo.getPatientSubageUnit()));
                builder.add(vo.getApplyType());
                builder.add(vo.getGroupName());
                builder.add(vo.getOperatorName());
                builder.add(vo.getCurrentLink());
                builder.add(DateUtil.formatDate(vo.getCreateTime()));

                writer.writeRow(builder);
            }

            writer.flush(fos);
        }

        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION,
                String.format("attachment; filename=%s", URLEncoder.encode("送检标本查询.xlsx", StandardCharsets.UTF_8)))
            .contentType(MediaType.APPLICATION_OCTET_STREAM).contentType(new MediaType("application", "vnd.ms-excel"))
            .body(new FileSystemResource(tempFile));
    }

    /**
     * 查询
     */
    @PostMapping("/query")
    public List<UnfinishedWorkVo> query(@RequestBody UnfinishedWorkStatisticsQueryVo queryVo) {
        queryVo.defaultDate();

        final List<String> barcodeLinks = CollectionUtil.defaultIfEmpty(queryVo.getBarcodeLinks(), List.of());

        final Map<String, String> barcodeLinkMap =
            barcodeLinks.stream().collect(Collectors.toMap(String::toString, v -> v, (a, b) -> a));

        // 查询条件
        AtomicInteger pageNo = new AtomicInteger(1);
        final SampleEsQuery query = SampleEsQuery.builder().pageSize(10000).startCreateDate(queryVo.getStartDate())
            .endCreateDate(queryVo.getEndDate()).build();

        // 过滤已终止的样本
        query.setExcludeSampleStatus(List.of(SampleStatusEnum.STOP_TEST.getCode()));

        if (Objects.nonNull(queryVo.getHspOrgId())) {
            query.setHspOrgIds(Set.of(queryVo.getHspOrgId()));
        }

        // 分血组 SPLIT_BLOOD 是分血组的编码
        final ProfessionalGroupDto splitBloodGroup =
            groupService.selectSplitBloodGroup(LoginUserHandler.get().getOrgId());
        List<UnfinishedWorkVo> vos = new ArrayList<>();
        List<Object> searchAfter = null;

        do {
            // 自增页码
            query.setPageNo(pageNo.getAndIncrement());

            final ScrollPage<? extends BaseSampleEsModelDto> page =
                elasticSearchSampleService.searchAfter(searchAfter, query);
            searchAfter = page.getSearchAfter();
            final List<? extends BaseSampleEsModelDto> datas = page.getData();
            if (CollectionUtils.isEmpty(datas)) {
                break;
            }

            // 查询所有样本所在的逻辑试管架位置
            final Collection<Long> applySampleIds =
                datas.stream().map(BaseSampleEsModelDto::getApplySampleId).collect(Collectors.toSet());
            final Map<Long, SampleRackPositionDto> sampleRackPositionMap =
                rackLogicSpaceService.selectSamplePositionByApplySampleIds(applySampleIds).stream()
                    .collect(Collectors.toMap(SampleRackPositionDto::getApplySampleId, m -> m, (k1, k2) -> k1));

            // 查询所有的样本
            final Map<Long, ApplySampleDto> applySampleMap =
                applySampleService.selectByApplySampleIdsAsMap(applySampleIds);

            if (MapUtils.isEmpty(applySampleMap)) {
                continue;
            }

            //查询所有的异常信息
            Map<String, List<SampleAbnormalDto>> sampleAbnormalDtoMap =
                    sampleAbnormalService.selectByBarcodes(datas.stream().map(BaseSampleEsModelDto::getBarcode).collect(Collectors.toList())).stream()
                            .collect(Collectors.groupingBy(SampleAbnormalDto::getBarcode));

            for (final BaseSampleEsModelDto data : datas) {

                final ApplySampleDto applySample = applySampleMap.get(data.getApplySampleId());
                if (Objects.isNull(applySample)) {
                    continue;
                }

                // 当前环节 如果不存在则跳过
                final SampleRackPositionDto sampleRackPosition = sampleRackPositionMap.get(data.getApplySampleId());
                final Link currentLink = getCurrentLink(data, sampleRackPosition, splitBloodGroup, applySample);
                if (Objects.isNull(currentLink)) {
                    continue;
                }

                if (MapUtils.isNotEmpty(barcodeLinkMap) && !barcodeLinkMap.containsKey(currentLink.name())) {
                    continue;
                }

                UnfinishedWorkVo vo = new UnfinishedWorkVo();
                vo.setApplySampleId(data.getApplySampleId());
                vo.setHspOrgId(data.getHspOrgId());
                vo.setHspOrgName(data.getHspOrgName());
                vo.setBarcode(data.getBarcode());
                vo.setPatientName(data.getPatientName());
                vo.setPatientSex(data.getPatientSex());
                vo.setPatientAge(data.getPatientAge());
                vo.setPatientSubage(data.getPatientSubage());
                vo.setPatientSubageUnit(data.getPatientSubageUnit());
                vo.setApplyType(data.getApplyTypeCode());
                vo.setApplyTypeName(data.getApplyTypeName());
                vo.setGroupName(StringUtils.defaultIfBlank(data.getGroupName(), StringUtils.EMPTY));
                if (Objects.nonNull(sampleRackPosition) && StringUtils.isBlank(vo.getGroupName())) {
                    vo.setGroupName(
                        StringUtils.defaultIfBlank(sampleRackPosition.getCurrentGroupName(), StringUtils.EMPTY));
                }
                //检验项目
                if (CollectionUtils.isNotEmpty(data.getTestItems())){
                    vo.setTestItems(data.getTestItems().stream().map(BaseSampleEsModelDto.TestItem::getTestItemName)
                            .collect(Collectors.toList()));
                }
                //异常信息
                if (sampleAbnormalDtoMap != null && sampleAbnormalDtoMap.containsKey(data.getBarcode())) {
                    sampleAbnormalDtoMap.get(data.getBarcode()).forEach(e -> {
                        vo.addAbnormalType(e.getAbnormalReasonName());
                    });
                }

                vo.setOperatorName(data.getUpdaterName());
                vo.setCurrentLink(currentLink.getDesc());
                vo.setCreateTime(data.getCreateDate());

                vos.add(vo);
            }

        } while (!Thread.currentThread().isInterrupted());

        vos.sort(Comparator.comparing(UnfinishedWorkVo::getCreateTime));
        return vos;
    }

    private Link getCurrentLink(BaseSampleEsModelDto data, SampleRackPositionDto sampleRack,
        ProfessionalGroupDto splitBloodGroup, ApplySampleDto applySample) {

        final Integer applyStatus = data.getApplyStatus();
        final Integer sampleStatus = applySample.getStatus();
        final Long groupId = applySample.getGroupId();

        // 样本位置
        final Optional<SampleRackPositionDto> sampleRackPosition = Optional.ofNullable(sampleRack);

        /* @see RackLogicDto#getPosition() 逻辑试管架位置 */
        final Integer position = sampleRackPosition.map(SampleRackPositionDto::getPosition).orElse(null);

        // 未复核
        final boolean notCheck = Objects.equals(applyStatus, ApplyStatusEnum.WAIT_CHECK.getCode())
            || Objects.equals(applyStatus, ApplyStatusEnum.WAIT_DOUBLE_CHECK.getCode());
        if (BooleanUtils.isTrue(notCheck)) {
            return Link.NOT_CHECK;
        }

        // 未一次分拣 特殊场景（如果 position 为null，那就认为是一次分拣）
        final boolean notOnePick = Objects.equals(applySample.getIsOnePick(), YesOrNoEnum.NO.getCode());

        if (BooleanUtils.isTrue(notOnePick)) {
            return Link.NOT_ONE_PICK;
        }

        // 一次分拣完未下架
        final boolean onePickNotOff = Objects.equals(position, RackLogicPositionEnum.ONE_PICKING.getCode());
        if (BooleanUtils.isTrue(onePickNotOff)) {
            return Link.ONE_PICK_NOT_OFF_SHELVES;
        }

        // 一次分拣完交接中
        final boolean onePickNotHanover = Objects.equals(position, RackLogicPositionEnum.ONE_PICKED.getCode());
        if (BooleanUtils.isTrue(onePickNotHanover)) {
            return Link.ONE_PICK_NOT_HANDOVER;
        }

        // 未分血 没分血&在分血组
        final boolean notSplitBlood = Objects.equals(groupId, splitBloodGroup.getGroupId())
            && Objects.equals(applySample.getIsSplitBlood(), YesOrNoEnum.NO.getCode());
        if (BooleanUtils.isTrue(notSplitBlood)) {
            return Link.NOT_SPLIT_BLOOD;
        }

        // 分血完未交接
        final boolean splitBloodNotHandover = Objects.equals(position, RackLogicPositionEnum.SPLIT_BLOOD.getCode());
        if (BooleanUtils.isTrue(splitBloodNotHandover)) {
            return Link.SPLIT_BLOOD_NOT_HANDOVER;
        }

        // 未外送登记
        final boolean notSendRecord = Objects.equals(applySample.getIsTwoPick(), YesOrNoEnum.NO.getCode())
            && Objects.equals(applySample.getIsOutsourcing(), YesOrNoEnum.YES.getCode());
        if (BooleanUtils.isTrue(notSendRecord)) {
            return Link.NOT_OUT_REGISTER;
        }

        // 未组间交接
        final boolean notGroupHandover = Objects.equals(position, RackLogicPositionEnum.GROUP_PICKING.getCode());
        if (BooleanUtils.isTrue(notGroupHandover)) {
            return Link.NOT_GROUP_HANDOVER;
        }

        // 未二次分拣
        final boolean notTwoPick = Objects.equals(applySample.getIsTwoPick(), YesOrNoEnum.NO.getCode());
        if (BooleanUtils.isTrue(notTwoPick)) {
            return Link.NOT_TWO_PICK;
        }

        // 未一审
        final boolean notOneAudit = Objects.equals(sampleStatus, SampleStatusEnum.NOT_AUDIT.getCode());
        if (BooleanUtils.isTrue(notOneAudit)) {
            if (CollectionUtils.containsAny(ITEM_TYPE_LIST, data.getItemType())) {
                return Link.NOT_ONE_AUDIT;
            }
            return Link.NOT_FINAL_AUDIT;
        }

        // 未审核
        final boolean notFinalAudit = Objects.equals(sampleStatus, SampleStatusEnum.ONE_AUDIT.getCode());
        if (BooleanUtils.isTrue(notFinalAudit)) {
            return Link.NOT_FINAL_AUDIT;
        }

        // 未打印
        final boolean notPrint = Objects.equals(applySample.getIsPrint(), YesOrNoEnum.NO.getCode())
            && Objects.equals(sampleStatus, SampleStatusEnum.AUDIT.getCode());
        if (BooleanUtils.isTrue(notPrint)) {
            return Link.NOT_PRINT;
        }

        return null;
    }

    /**
     * 条码环节
     */
    @Getter
    private enum Link {

        /**
         * 未复核
         */
        NOT_CHECK("未复核"),

        /**
         * 未一次分拣
         */
        NOT_ONE_PICK("未一次分拣"),

        /**
         * 一次分拣未下架
         */
        ONE_PICK_NOT_OFF_SHELVES("一次分拣未下架"),

        /**
         * 一次分拣未交接
         */
        ONE_PICK_NOT_HANDOVER("一次分拣未交接"),
        /**
         * 未分血
         */
        NOT_SPLIT_BLOOD("未分血"),

        /**
         * 分血未交接
         */
        SPLIT_BLOOD_NOT_HANDOVER("分血未交接"),

        /**
         * 未外送登记
         */
        NOT_OUT_REGISTER("未外送登记"),

        /**
         * 未二次分拣
         */
        NOT_TWO_PICK("未二次分拣"),

        /**
         * 未组间交接
         */
        NOT_GROUP_HANDOVER("未组间交接"),

        /**
         * 未一审
         */
        NOT_ONE_AUDIT("未一审"),

        /**
         * 未审核
         */
        NOT_FINAL_AUDIT("未审核"),

        /**
         * 未打印
         */
        NOT_PRINT("未打印"),

        ;

        Link(String desc) {
            this.desc = desc;
        }

        private final String desc;
    }

}
