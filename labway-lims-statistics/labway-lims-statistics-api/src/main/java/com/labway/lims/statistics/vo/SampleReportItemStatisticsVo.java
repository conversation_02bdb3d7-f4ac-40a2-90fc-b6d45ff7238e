package com.labway.lims.statistics.vo;

import com.labway.lims.api.enums.routine.TestJudgeEnum;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <p>
 * SampleStatisticsVo
 * 报告项目信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/27 14:45
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SampleReportItemStatisticsVo extends BaseSampleEsModelDto.ReportItem {

    /**
     * 结果范围
     */
    private String range;
    /**
     * 结果 （经过一系列的计算 转换最终得到的结果值）
     */
    private String result;
    /**
     * 检验判定 UP  DOWN  NORMAL
     * @see TestJudgeEnum
     */
    private String judge;
    /**
     * 来源仪器
     */
    private Long instrumentId;
    /**
     * 仪器结果
     */
    private String instrumentResult;

}
