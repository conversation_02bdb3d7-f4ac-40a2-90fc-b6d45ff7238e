package com.labway.lims.statistics.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <pre>
 * DisplayType
 * 导出类型
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/1/9 11:23
 */
@Getter
@AllArgsConstructor
public enum ExportType {

    // 导出当前报表
    CURRENT(1),

    // 导出所有报表
    ALL(2),

    ;

    private final Integer type;

    public static ExportType getByType(Integer type) {
        for (ExportType exportType : ExportType.values()) {
            if (exportType.getType().equals(type)) {
                return exportType;
            }
        }
        throw new IllegalArgumentException("不支持的类型");
    }
}
