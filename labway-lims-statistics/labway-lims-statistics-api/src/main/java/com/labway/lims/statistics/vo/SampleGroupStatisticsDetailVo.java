package com.labway.lims.statistics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SampleStatusEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * SampleGroupStatisticsDetailVo
 * 申请单样本信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/27 14:45
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SampleGroupStatisticsDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请单id
     */
    private Long applyId;
    /**
     * 申请单样本id
     */
    private Long applySampleId;
    /**
     * 患者名称
     */
    private String patientName;
    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private Integer patientSex;
    /**
     * 患者年龄
     */
    private Integer patientAge;

    /**
     * 患者子年龄
     */
    private Integer patientSubage;

    /**
     * 患者子年龄单位
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;
    /**
     * 就诊类型 (申请类型)
     */
    private String applyType;
    /**
     * 科室
     */
    private String dept;
    /**
     * 就诊卡号 (门诊|住院号)
     */
    private String patientVisitCard;
    /**
     * 床号
     */
    private String patientBed;
    /**
     * 临床诊断
     */
    private String diagnosis;
    /**
     * 结果备注
     */
    private String resultRemark;
    /**
     * 是否打印:1已打印，0未打印
     */
    private Integer isPrint;
    /**
     * 采样时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date samplingDate;
    /**
     * 检验时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDate;
    /**
     * 签收日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date signDate;
    /**
     * 样本类型
     */
    private String sampleTypeCode;
    /**
     * 样本类型名称
     */
    private String sampleTypeName;
    /**
     * 专业组id
     */
    private Long groupId;
    /**
     * 专业组名称
     */
    private String groupName;
    /**
     * 样本状态
     *
     * @see SampleStatusEnum
     */
    private Integer sampleStatus;
    private String statusText;
    /**
     * 一审人id
     */
    private Long oneCheckerId;
    /**
     * 一审人
     */
    private String oneCheckerName;
    /**
     * 二审人id
     */
    private Long twoCheckerId;
    /**
     * 二审人
     */
    private String twoCheckerName;

    /**
     * 是否已经二次分拣 1是，0不是
     *
     * @see YesOrNoEnum
     */
    private Integer isTwoPick;
    /**
     * 通用报告信息
     */
    private List<BaseSampleEsModelDto.Report> reports;

    /**
     * 终审时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date finalCheckDate;
    /**
     * 项目类型 常规检验、微生物检验、院感检验 区分做什么检验的
     *
     * @see ItemTypeEnum
     */
    private String itemType;
    /**
     * 样本id （可能是常规检验|微生物|院感...）
     */
    private Long sampleId;
    /**
     * 样本条码
     */
    private String barcode;
    /**
     * 条码环节
     */
    private String barcodeLink;
    /**
     * 检验项目
     */
    private List<BaseSampleEsModelDto.TestItem> testItems;
    /**
     * 创建时间|录入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createDate;
    //--------------------------------------------
    /**
     * 报告时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date reportDate;

    /**
     * 主条码
     */
    private String masterBarcode;

    /**
     * 送检医生名称
     */
    private String sendDoctorName;

    /**
     * 送检医生编码
     */
    private String sendDoctorCode;

    /**
     * 原始机构编码
     */
    private String originalOrgCode;
    /**
     * 原始机构名称
     */
    private String originalOrgName;
}
