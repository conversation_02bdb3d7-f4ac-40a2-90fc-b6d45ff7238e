package com.labway.lims.statistics.vo;

import com.labway.lims.apply.api.dto.PathologyReportItemResponseDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 报告单 -病理报告单打印 响应
 */
@Getter
@Setter
public class SelectPathologyReportListResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 前端使用
     */
    private Long soleId;

    /**
     * 送检机构id
     */
    private Long hspOrgId;

    /**
     * 送检机构id
     */
    private String hspOrgCode;
    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 送检机构下具体患者信息
     */
    private List<PathologyReportItemResponseDto> reportItemList;

}
