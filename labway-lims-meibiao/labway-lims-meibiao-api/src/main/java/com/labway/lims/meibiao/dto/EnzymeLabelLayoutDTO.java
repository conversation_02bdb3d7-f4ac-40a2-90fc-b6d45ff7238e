package com.labway.lims.meibiao.dto;

import cn.hutool.core.lang.Assert;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 酶标板布局
 */
@Data
public class EnzymeLabelLayoutDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 布局ID
     */
    private Long layoutId;

    /**
     * 酶标板ID
     */
    private Long plateId;

    /**
     * 酶标板号
     */
    private String plateCode;

    /**
     * 引用模板ID
     */
    private Long refTemplateId;

    /**
     * 引用模板名称
     */
    private String refTemplateName;

    /**
     * 是否为模板
     */
    private Integer isTemplate;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 样本号日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private LocalDate sampleNoDate;

    /**
     * 样本开始号
     */
    private String sampleStartNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 放置规则编码(0横向 1竖向)
     */
    private String placeRuleCode;

    /**
     * 放置规则
     */
    private String placeRule;

    /**
     * 板面日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private LocalDate plateDate;

    @Getter
    public enum PlaceRule {
        LENGTHWISE("1", "纵向"),
        CROSSWISE("0", "横向"),

        ;

        private final String code;

        private final String name;

        PlaceRule(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public static String getNameByCode(String code) {
            for (PlaceRule value : values()) {
                if (value.getCode().equals(code)) {
                    return value.getName();
                }
            }
            return "";
        }

        public static List<String> codeList() {
            return Arrays.stream(values()).map(PlaceRule::getCode).collect(Collectors.toList());
        }
    }


    public void verifySaveParams() {
        Assert.notNull(this.getPlateId(), "酶免板不能为空");

        Assert.notNull(this.getIsTemplate(), "是否为模板不能为空");

        Assert.notNull(this.getPlateDate(), "版面日期不能为空");

        if (StringUtils.isBlank(this.getPlateCode())) {
            throw new IllegalArgumentException("板号不能为空");
        }

        if (this.getIsTemplate() == 1 && StringUtils.isBlank(this.getTemplateName())) {
            throw new IllegalArgumentException("模板名称不能为空");
        }

        Assert.notNull(this.getSampleNoDate(), "样本号日期不能为空");

        if (StringUtils.isBlank(this.getSampleStartNo())) {
            throw new IllegalArgumentException("样本开始号不能为空");
        }

        if (!PlaceRule.codeList().contains(this.getPlaceRuleCode())) {
            throw new IllegalArgumentException("防止规则编码不能为空 0横向 1竖向");
        }
        this.setPlaceRule(PlaceRule.getNameByCode(this.getPlaceRuleCode()));

        if (StringUtils.isNotBlank(this.getRemark()) && this.getRemark().length() > 50) {
            throw new IllegalArgumentException("备注最大长度为50字符");
        }
    }


}
