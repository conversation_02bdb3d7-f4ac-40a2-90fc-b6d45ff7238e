package com.labway.lims.meibiao.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.meibiao.dto.EnzymeLabelReportItemDTO;
import com.labway.lims.meibiao.dto.TemplateSampleDTO;
import com.taobao.api.internal.util.StringUtils;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
public class RawRecordResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 五个项目英文名
     */
    private EnzymeLabelReportItemVO oneItem;
    private EnzymeLabelReportItemVO twoItem;
    private EnzymeLabelReportItemVO threeItem;
    private EnzymeLabelReportItemVO fourItem;
    private EnzymeLabelReportItemVO fiveItem;

    private Set<String> reportCodeSet = new HashSet<>();

    private List<List<Result>> result;

    private List<Result> list;

    public List<Result> selectList() {
        list = new ArrayList<>();
        for (List<Result> r : result) {
            list.addAll(r);
        }
        return list;
    }

    public RawRecordResultVO() {
    }

    public RawRecordResultVO(List<EnzymeLabelReportItemVO> enzymeLabelReportItemVOS, List<Long> reportIdList) {
        Map<Long, EnzymeLabelReportItemVO> reportIdAndReportItemMap = enzymeLabelReportItemVOS.stream().collect(Collectors.toMap(EnzymeLabelReportItemDTO::getLabelReportId, Function.identity()));

        if (reportIdAndReportItemMap.size() != reportIdList.size()) {
            throw new IllegalArgumentException("请查看乙肝两对半项目是否都存在");
        }

        long qcNum = enzymeLabelReportItemVOS.stream().filter(EnzymeLabelReportItemDTO::getIsGenerateQc).count();
        long negativeNum = enzymeLabelReportItemVOS.stream().filter(EnzymeLabelReportItemDTO::getIsNegativeContrast).count();
        long positiveNum = enzymeLabelReportItemVOS.stream().filter(EnzymeLabelReportItemDTO::getIsPositiveContrast).count();
        long blankNum = enzymeLabelReportItemVOS.stream().filter(EnzymeLabelReportItemDTO::getIsGenerateBlank).count();
        long reservedNum = enzymeLabelReportItemVOS.stream().filter(EnzymeLabelReportItemDTO::getIsReservedSample).count();
        long standardNum = enzymeLabelReportItemVOS.stream().filter(EnzymeLabelReportItemDTO::getIsGenerateStandard).count();

        Set<Integer> negativeSet = enzymeLabelReportItemVOS.stream().filter(EnzymeLabelReportItemDTO::getIsNegativeContrast).map(EnzymeLabelReportItemDTO::getNegativeContrastNum).collect(Collectors.toSet());
        Set<Integer> positiveSet = enzymeLabelReportItemVOS.stream().filter(EnzymeLabelReportItemDTO::getIsPositiveContrast).map(EnzymeLabelReportItemDTO::getPositiveContrastNum).collect(Collectors.toSet());
        Set<Integer> blankSet = enzymeLabelReportItemVOS.stream().filter(EnzymeLabelReportItemDTO::getIsGenerateBlank).map(EnzymeLabelReportItemDTO::getGenerateBlankNum).collect(Collectors.toSet());
        Set<Integer> standarSet = enzymeLabelReportItemVOS.stream().filter(EnzymeLabelReportItemDTO::getIsGenerateStandard).map(EnzymeLabelReportItemDTO::getGenerateStandardNum).collect(Collectors.toSet());

        // 是否都为true  或者是否都为false
        if ((qcNum != 0 && qcNum != 5) ||
                (negativeNum != 0 && negativeNum != 5) ||
                (positiveNum != 0 && positiveNum != 5) ||
                (blankNum != 0 && blankNum != 5) ||
                (reservedNum != 0 && reservedNum != 5) ||
                (standardNum != 0 && standardNum != 5) ||

                // 有为true 要生成的 查看生成的数量是否相同
                negativeSet.size() > 1 || positiveSet.size() > 1 || blankSet.size() > 1 || standarSet.size() > 1
        ) {

            List<String> reportCodes = enzymeLabelReportItemVOS.stream().map(EnzymeLabelReportItemVO::getReportItemCode).collect(Collectors.toList());
            throw new IllegalArgumentException(StringUtils.join(reportCodes, ",") + "项目生成[质控][阴性对照][阳性对照][留样复测][空白][标准品]配置不同， 请先去修改配置");

        }


        this.oneItem = reportIdAndReportItemMap.get(reportIdList.get(0));
        this.twoItem = reportIdAndReportItemMap.get(reportIdList.get(1));
        this.threeItem = reportIdAndReportItemMap.get(reportIdList.get(2));
        this.fourItem = reportIdAndReportItemMap.get(reportIdList.get(3));
        this.fiveItem = reportIdAndReportItemMap.get(reportIdList.get(4));

        if (Objects.nonNull(oneItem)) {
            reportCodeSet.add(oneItem.getReportItemCode());
        }
        if (Objects.nonNull(twoItem)) {
            reportCodeSet.add(twoItem.getReportItemCode());
        }
        if (Objects.nonNull(threeItem)) {
            reportCodeSet.add(threeItem.getReportItemCode());
        }
        if (Objects.nonNull(fourItem)) {
            reportCodeSet.add(fourItem.getReportItemCode());
        }
        if (Objects.nonNull(fiveItem)) {
            reportCodeSet.add(fiveItem.getReportItemCode());
        }
    }


    @Data
    public static class Result implements Serializable{
        /**
         * 检验时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
        private LocalDateTime testDate;

        /**
         * 样本号
         */
        private String sampleNo;

        /**
         * 样本id
         */
        private Long sampleId;

        /**
         * 病人姓名
         */
        private String patientName;

        /**
         * 条码号
         */
        private String barcode;

        /**
         * 病人性别
         */
        private String patientSex;

        /**
         * 病人年龄
         */
        private Integer patientAge;

        /**
         * 质控类型
         */
        private String qcType;

//        /**
//         * 报告项目名
//         */
//        private String reportItemName;

        /**
         * 样本类型
         */
        private String sampleTypeCode;

        /**
         * 样本类型
         */
        private String sampleType;

        /**
         * 结果
         */
        private String resultValue;

        /**
         * 备注
         */
        private String remake;

        /**
         * 乙肝两对半的五个项目  是否做， 做的话null  不做的话 /
         */
        private String oneItem;
        private String twoItem;
        private String threeItem;
        private String fourItem;
        private String fiveItem;

        private List<Long> reportIds;

        public LocalDate getTestDate() {
            return testDate.toLocalDate();
        }

        public List<Long> getReportIds() {
            if (CollectionUtils.isEmpty(reportIds)) {
                this.setReportIds(new LinkedList<>());
            }
            return reportIds;
        }

        public String selectItem(String english){
            if(TemplateSampleDTO.ColEnum.ONE.getEnglish().equals(english)){
                return oneItem;
            }
            if(TemplateSampleDTO.ColEnum.TWO.getEnglish().equals(english)){
                return twoItem;
            }
            if(TemplateSampleDTO.ColEnum.THREE.getEnglish().equals(english)){
                return threeItem;
            }
            if(TemplateSampleDTO.ColEnum.FOUR.getEnglish().equals(english)){
                return fourItem;
            }
            if(TemplateSampleDTO.ColEnum.FIVE.getEnglish().equals(english)){
                return fiveItem;
            }
            throw new IllegalArgumentException();
        }

    }


    @Getter
    public enum QcType {
        QC_ADD("(+)"),
        QC_MINUS("(-)"),
        ;

        private final String symbol;

        QcType(String symbol) {
            this.symbol = symbol;
        }

    }


}
