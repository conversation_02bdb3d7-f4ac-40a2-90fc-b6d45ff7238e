package com.labway.lims.meibiao.service;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.meibiao.dal.EnzymeLabelLayoutDal;
import com.labway.lims.meibiao.dal.EnzymeLabelPlateDal;
import com.labway.lims.meibiao.dto.EnzymeLabelLayoutDTO;
import com.labway.lims.meibiao.dto.EnzymeLabelTemplateSampleDTO;
import com.labway.lims.meibiao.dto.StateDataDTO;
import com.labway.lims.meibiao.exception.LockException;
import com.labway.lims.meibiao.mapper.EnzymeLabelLayoutMapper;
import com.labway.lims.meibiao.model.TbEnzymeLabelLayout;
import com.labway.lims.meibiao.model.TbEnzymeLabelPlate;
import com.labway.lims.meibiao.vo.EnzymeLabelLayoutVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Slf4j
@DubboService
public class EnzymeLabelLayoutServiceImpl extends ServiceImpl<EnzymeLabelLayoutMapper, TbEnzymeLabelLayout>
        implements IEnzymeLabelLayoutService {

    @Resource
    private EnzymeLabelLayoutDal enzymeLabelLayoutDal;

    @Resource
    private EnzymeLabelPlateDal enzymeLabelPlateDal;

    @Resource
    private IEnzymeLabelTemplateSampleService iEnzymeLabelTemplateSampleService;

    @Resource
    private IEnzymeLabelSampleService iEnzymeLabelSampleService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private static final String ADD_LABEL_LAYOUT_AND_TEMPLATE_LOCK = "labway:lims:meibiao:layout:template:add:lock";
    private static final String ADD_LABEL_LAYOUT_AND_SAMPLE_LOCK = "labway:lims:meibiao:layout:sample:add:lock";

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addLabelLayoutAndTemplate(EnzymeLabelLayoutDTO labelLayoutDTO, List<EnzymeLabelTemplateSampleDTO> data) {
        LoginUserHandler.User user = LoginUserHandler.get();
        LocalDateTime nowDateTime = LocalDateTime.now();

        // 新增模板布局
        final String lockKey = ADD_LABEL_LAYOUT_AND_TEMPLATE_LOCK + ":" + labelLayoutDTO.getPlateId();
        Boolean lock = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "lock");

        try {

            if (!Boolean.TRUE.equals(lock)) {
                throw new LockException("正在保存模板中 请稍后再试");
            }

            // 查询模板重名
            EnzymeLabelLayoutVO enzymeLabelLayoutVO = iEnzymeLabelTemplateSampleService.selectByTemplateName(labelLayoutDTO.getTemplateName());
            if (enzymeLabelLayoutVO != null) {
                throw new IllegalArgumentException("模板名称已存在");
            }

            TbEnzymeLabelPlate tbEnzymeLabelPlate = enzymeLabelPlateDal.selectByPlateId(labelLayoutDTO.getPlateId());

            // 可以修改版号， 校验版号重复
            if (!Objects.equals(labelLayoutDTO.getPlateCode(), tbEnzymeLabelPlate.getPlateCode()) &&
                    Objects.nonNull(enzymeLabelPlateDal.selectByPlateCode(labelLayoutDTO.getPlateCode()))) {
                throw new IllegalArgumentException("版号已存在");
            }

            // 修改酶免板 备注
            Assert.isTrue(enzymeLabelPlateDal.updateRemakeAndPlateDateByPlateId(labelLayoutDTO.getPlateId(), labelLayoutDTO.getPlateCode(), labelLayoutDTO.getRemark(), labelLayoutDTO.getPlateDate()),
                    "酶免板备注修改失败");

            // 新增酶标板布局
            labelLayoutDTO.setIsTemplate(StateDataDTO.INT_ONE);
            TbEnzymeLabelLayout tbEnzymeLabelLayout = fillLayoutTemplateData(labelLayoutDTO, tbEnzymeLabelPlate, user, nowDateTime);
            if (!enzymeLabelLayoutDal.insertLabelLayout(tbEnzymeLabelLayout)) {
                log.info("新增酶免板布局失败 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), tbEnzymeLabelLayout);
                throw new IllegalStateException("新增酶免板布局失败");
            }

            // 填充layoutId
            for (EnzymeLabelTemplateSampleDTO datum : data) {
                datum.setLayoutId(tbEnzymeLabelLayout.getLayoutId());
            }

            // 新增模板结果
            if (!iEnzymeLabelTemplateSampleService.insertBatchTemplateSample(data)) {
                log.info("新增模板详情失败 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), data);
                throw new IllegalStateException("新增模板详情失败");
            }

            return true;
        } finally {
            stringRedisTemplate.delete(lockKey);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addLabelLayoutAndSample(EnzymeLabelLayoutDTO labelLayoutDTO, List<EnzymeLabelTemplateSampleDTO> data) {
        LoginUserHandler.User user = LoginUserHandler.get();
        LocalDateTime nowDateTime = LocalDateTime.now();

        final String lockKey = ADD_LABEL_LAYOUT_AND_SAMPLE_LOCK + ":" + labelLayoutDTO.getPlateId();
        Boolean lock = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "lock");

        try {

            if (!Boolean.TRUE.equals(lock)) {
                throw new LockException("正在保存样本中 请稍后再试");
            }

            TbEnzymeLabelPlate tbEnzymeLabelPlate = enzymeLabelPlateDal.selectByPlateId(labelLayoutDTO.getPlateId());

            // 可以修改版号， 校验版号重复
            if (!Objects.equals(labelLayoutDTO.getPlateCode(), tbEnzymeLabelPlate.getPlateCode()) &&
                    Objects.nonNull(enzymeLabelPlateDal.selectByPlateCode(labelLayoutDTO.getPlateCode()))) {
                throw new IllegalArgumentException("版号已存在");
            }

            // 修改酶免板 备注
            Assert.isTrue(enzymeLabelPlateDal.updateRemakeAndPlateDateByPlateId(labelLayoutDTO.getPlateId(), labelLayoutDTO.getPlateCode(), labelLayoutDTO.getRemark(), labelLayoutDTO.getPlateDate()),
                    "酶免板备注修改失败");

            // 新增酶标板布局
            labelLayoutDTO.setIsTemplate(StateDataDTO.INT_ZERO);
            TbEnzymeLabelLayout tbEnzymeLabelLayout = fillLayoutTemplateData(labelLayoutDTO, tbEnzymeLabelPlate, user, nowDateTime);
            if (!enzymeLabelLayoutDal.insertOrUpdateLabelLayout(tbEnzymeLabelLayout)) {
                log.info("保存酶免板布局失败 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), tbEnzymeLabelLayout);
                throw new IllegalStateException("保存酶免板布局失败");
            }

            // 填充plateId 和 plateName
            for (EnzymeLabelTemplateSampleDTO datum : data) {
                datum.setPlateId(tbEnzymeLabelPlate.getPlateId());
                datum.setPlateCode(tbEnzymeLabelPlate.getPlateCode());
            }

            // 新增模板结果
            if (!iEnzymeLabelSampleService.insertBatchSample(data, tbEnzymeLabelPlate.getPlateId(), labelLayoutDTO.getSampleNoDate())) {
                log.info("保存酶免板布局失败 ：userName:{}, 专业组:{}, 参数:{}", user.getNickname(), user.getGroupName(), data);
                throw new IllegalStateException("保存酶免板模板样本失败");
            }

            return true;
        } finally {
            stringRedisTemplate.delete(lockKey);
        }

    }


    @Override
    public List<EnzymeLabelLayoutVO> selectAllTemplate(Integer isTemplate) {
        return enzymeLabelLayoutDal.selectAll(isTemplate);
    }

    @Override
    public EnzymeLabelLayoutVO selectByPlateId(long plateId) {

        TbEnzymeLabelPlate tbEnzymeLabelPlate = enzymeLabelPlateDal.selectByPlateId(plateId);
        Assert.notNull(tbEnzymeLabelPlate, "布局id不存在");

        EnzymeLabelLayoutVO enzymeLabelLayoutVO = enzymeLabelLayoutDal.selectByPlateId(plateId);
        if (enzymeLabelLayoutVO == null) {
            enzymeLabelLayoutVO = new EnzymeLabelLayoutVO();
            enzymeLabelLayoutVO.setIsTemplate(StateDataDTO.INT_ZERO);
            enzymeLabelLayoutVO.setPlateId(tbEnzymeLabelPlate.getPlateId());
            enzymeLabelLayoutVO.setPlateCode(tbEnzymeLabelPlate.getPlateCode());
            enzymeLabelLayoutVO.setPlateDate(tbEnzymeLabelPlate.getPlateDate());
            enzymeLabelLayoutVO.setPlaceRuleCode(EnzymeLabelLayoutDTO.PlaceRule.LENGTHWISE.getCode());
            enzymeLabelLayoutVO.setPlaceRule(EnzymeLabelLayoutDTO.PlaceRule.LENGTHWISE.getName());
            enzymeLabelLayoutVO.setSampleStartNo(String.valueOf(StateDataDTO.INT_ONE));
            enzymeLabelLayoutVO.setSampleNoDate(LocalDate.now());
        } else {
            enzymeLabelLayoutVO.setPlateCode(tbEnzymeLabelPlate.getPlateCode());
            enzymeLabelLayoutVO.setPlateDate(tbEnzymeLabelPlate.getPlateDate());
        }
        return enzymeLabelLayoutVO;
    }

    @Override
    public EnzymeLabelLayoutVO selectByLayoutId(Long layoutId) {
        EnzymeLabelLayoutVO enzymeLabelLayoutVO = enzymeLabelLayoutDal.selectByLayoutId(layoutId);
        return enzymeLabelLayoutVO == null ? new EnzymeLabelLayoutVO() : enzymeLabelLayoutVO;
    }

    /**
     * 新增模板时布局数据填充
     */
    private synchronized TbEnzymeLabelLayout fillLayoutTemplateData(EnzymeLabelLayoutDTO dto, TbEnzymeLabelPlate tbEnzymeLabelPlate,
                                                                    LoginUserHandler.User user, LocalDateTime now) {


        TbEnzymeLabelLayout tbEnzymeLabelLayout = new TbEnzymeLabelLayout();
        tbEnzymeLabelLayout.setLayoutId(null);

        tbEnzymeLabelLayout.setPlateId(tbEnzymeLabelPlate.getPlateId());
        tbEnzymeLabelLayout.setPlateCode(dto.getPlateCode());

        tbEnzymeLabelLayout.setRefTemplateId(dto.getRefTemplateId() == null ? 0 : dto.getRefTemplateId());
        tbEnzymeLabelLayout.setRefTemplateName(dto.getRefTemplateId() == null ? Strings.EMPTY : dto.getRefTemplateName());

        tbEnzymeLabelLayout.setIsTemplate(dto.getIsTemplate());
        tbEnzymeLabelLayout.setTemplateName(StringUtils.defaultString(dto.getTemplateName()));

        tbEnzymeLabelLayout.setSampleNoDate(dto.getSampleNoDate());
        tbEnzymeLabelLayout.setSampleStartNo(dto.getSampleStartNo());

        tbEnzymeLabelLayout.setRemark(StringUtils.defaultString(dto.getRemark()));

        tbEnzymeLabelLayout.setPlaceRuleCode(dto.getPlaceRuleCode());
        tbEnzymeLabelLayout.setPlaceRule(dto.getPlaceRule());

        tbEnzymeLabelLayout.setIsDelete(StateDataDTO.INT_ZERO);

        tbEnzymeLabelLayout.setOrgId(user.getOrgId());
        tbEnzymeLabelLayout.setOrgName(user.getOrgName());

        tbEnzymeLabelLayout.setCreateDate(now);
        tbEnzymeLabelLayout.setUpdateDate(now);

        tbEnzymeLabelLayout.setCreatorId(user.getUserId());
        tbEnzymeLabelLayout.setCreatorName(user.getNickname());

        tbEnzymeLabelLayout.setUpdaterId(user.getUserId());
        tbEnzymeLabelLayout.setUpdaterName(user.getNickname());

        return tbEnzymeLabelLayout;
    }

}
