package com.labway.lims.base.api.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class CombinePackageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 基准包
     */
    private Long packageId;

    /**
     * 财务套餐基准包
     */
    private List<QueryCombinePackageListTestItemsVo> combinePackageVoList;

}
