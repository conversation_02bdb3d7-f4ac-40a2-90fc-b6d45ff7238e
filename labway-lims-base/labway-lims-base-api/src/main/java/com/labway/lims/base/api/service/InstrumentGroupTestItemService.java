package com.labway.lims.base.api.service;

import com.labway.lims.base.api.dto.InstrumentGroupTestItemDto;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/3/29 20:39
 */
public interface InstrumentGroupTestItemService {
    /**
     * 添加检验项目
     * 
     * @param dtos List<InstrumentGroupTestItemDto>
     * @return Set<instrumentGroupTestItemId>
     */
    Set<Long> addBatch(List<InstrumentGroupTestItemDto> dtos);

    /**
     * 根据专业小组ID查询
     * 
     * @param instrumentGroupId instrumentGroupId
     * @return List<InstrumentGroupTestItemDto>
     */
    List<InstrumentGroupTestItemDto> selectByInstrumentGroupId(long instrumentGroupId);

    /**
     * 根据专业小组ID查询
     * 
     * @return List<InstrumentGroupTestItemDto>
     */
    List<InstrumentGroupTestItemDto> selectByInstrumentGroupIds(Collection<Long> instrumentGroupIds);

    /**
     * 删除专业小组仪器下的检验项目
     * 
     * @param instrumentGroupTestItemId instrumentGroupTestItemId
     */
    boolean deleteInstrumentGroupTestItemId(long instrumentGroupTestItemId);

    /**
     * 根据专业小组ID删除检验项目
     * 
     * @param instrumentGroupId instrumentGroupId
     */
    boolean deleteByInstrumentGroupId(long instrumentGroupId);

    /**
     * 根据instrumentGroupInstrumentId查询
     *
     * @param instrumentGroupInstrumentId instrumentGroupInstrumentId
     * @return List<InstrumentGroupTestItemDto>
     */
    List<InstrumentGroupTestItemDto> selectByInstrumentGroupInstrumentId(long instrumentGroupInstrumentId);

    /**
     * 根据 testItemId 查询
     */
    List<InstrumentGroupTestItemDto> selectByTestItemId(long testItemId);

    /**
     * 根据专业组查询
     */
    List<InstrumentGroupTestItemDto> selectByGroupId(long groupId);

    /**
     * 根据专业组查询
     */
    List<InstrumentGroupTestItemDto> selectByGroupIds(Collection<Long> groupIds);

    /**
     * 根据专业小组检验项目ID查询
     */
    List<InstrumentGroupTestItemDto> selectByInstrumentGroupTestItemIds(Collection<Long> instrumentGroupTestItemIds);

}
