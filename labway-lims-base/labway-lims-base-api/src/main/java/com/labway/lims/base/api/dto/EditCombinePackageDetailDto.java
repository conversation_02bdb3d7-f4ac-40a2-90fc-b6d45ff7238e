package com.labway.lims.base.api.dto;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Set;

@Data
public class EditCombinePackageDetailDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 套餐信息
     */
    private String combinePackageCode;


    /**
     * 检验项目ids
     */
    private Set<Long> testItemIds;


    public void verifyParams() {
        if (StringUtils.isBlank(combinePackageCode)) {
            throw new IllegalArgumentException("财务套餐code不能为空");
        }
        if (CollectionUtils.isEmpty(testItemIds)) {
            throw new IllegalArgumentException("检验项目不能为空");
        }
    }
}
