<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.lims.base.mapper.TbInstrumentMapper">

    <select id="selectByInstrumentGroupId" resultType="com.labway.lims.base.api.dto.InstrumentDto">
        select ti.*
        from tb_instrument ti
        inner join tb_instrument_group_instrument ta on ti.instrument_id = ta.instrument_id and ta.is_delete = 0
        where ti.is_delete = 0
        and ta.instrument_group_id = #{instrumentGroupId}
    </select>
</mapper>
