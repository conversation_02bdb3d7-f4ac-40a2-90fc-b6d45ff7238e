package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.api.field.CompareUtils;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.dto.PackageDto;
import com.labway.lims.base.api.dto.PhysicalGroupDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.PackageService;
import com.labway.lims.base.api.service.PhysicalGroupService;
import com.labway.lims.base.vo.PhysicalGroupAddRequestVo;
import com.labway.lims.base.vo.PhysicalGroupListRequestVo;
import com.labway.lims.base.vo.PhysicalGroupUpdateRequestVo;
import com.labway.lims.base.vo.SelectPhysicalGroupListResponseVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 体检团体 API
 * 
 * <AUTHOR>
 * @since 2023/3/23 16:20
 */
@RestController
@RequestMapping("/physical-group")
public class PhysicalGroupController extends BaseController {

    @DubboReference
    private PhysicalGroupService physicalGroupService;

    @DubboReference
    private PackageService packageService;

    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    /**
     * 体检单位 新增
     */
    @PostMapping("/add")
    public Object physicalGroupAdd(@RequestBody PhysicalGroupAddRequestVo vo) {
        if (StringUtils.isBlank(vo.getPhysicalGroupName()) || Objects.isNull(vo.getEnable())
            || Objects.isNull(vo.getHspOrgId())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        checkVoWhenAddOrUpdate(vo);

        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        if (Objects
            .nonNull(physicalGroupService.selectByPhysicalGroupName(vo.getPhysicalGroupName(), loginUser.getOrgId()))) {
            throw new LimsException("当前体检单位名称已存在");
        }

        HspOrganizationDto hspOrganizationDto = hspOrganizationService.selectByHspOrgId(vo.getHspOrgId());
        if (Objects.isNull(hspOrganizationDto)) {
            throw new LimsException("选择送检机构不存在");
        }
        // 转换
        PhysicalGroupDto physicalGroupDto = JSON.parseObject(JSON.toJSONString(vo), PhysicalGroupDto.class);
        physicalGroupDto.setHspOrgName(hspOrganizationDto.getHspOrgName());
        long id = physicalGroupService.addPhysicalGroup(physicalGroupDto);
        // 记录操作日志
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
            TraceLog.newInstance().setModule(TraceLogModuleEnum.PHYSICAL_GROUP_LOG.getDesc())
                .setContent(String.format("新增 [%s] 体检单位", physicalGroupDto.getPhysicalGroupName())).toJSONString());

        return Map.of("id", id);

    }

    /**
     * 体检单位 删除
     */
    @PostMapping("/delete")
    public Object physicalGroupDelete(@RequestBody Set<Long> physicalGroupIds) {
        if (CollectionUtils.isEmpty(physicalGroupIds)) {
            return Collections.emptyMap();
        }

        if (CollectionUtils.isNotEmpty(packageService.selectByGroupIds(physicalGroupIds))) {
            throw new LimsException("已选体检单位存在已被套餐引用,不能删除");
        }
        List<PhysicalGroupDto> physicalGroupDtos = physicalGroupService.selectByPhysicalGroupIds(physicalGroupIds);

        physicalGroupService.deleteByPhysicalGroupIds(physicalGroupIds);

        physicalGroupDtos.forEach(item -> {
            // 记录操作日志
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.PHYSICAL_GROUP_LOG.getDesc())
                    .setContent(String.format("删除 [%s] 体检单位", item.getPhysicalGroupName())).toJSONString());

        });
        return Collections.emptyMap();
    }

    /**
     * 体检单位 修改
     */
    @PostMapping("/update")
    public Object physicalGroupUpdate(@RequestBody PhysicalGroupUpdateRequestVo vo) {
        if (StringUtils.isBlank(vo.getPhysicalGroupName()) || Objects.isNull(vo.getEnable())
            || Objects.isNull(vo.getPhysicalGroupId()) || Objects.isNull(vo.getHspOrgId())) {
            throw new IllegalArgumentException("存在必填项未填写");
        }
        // 检查 参数
        checkVoWhenAddOrUpdate(vo);

        // 判断体检单位是否存在
        final PhysicalGroupDto physicalGroupDtoNow =
            physicalGroupService.selectByPhysicalGroupId(vo.getPhysicalGroupId());
        if (Objects.isNull(physicalGroupDtoNow)) {
            throw new LimsException("体检单位不存在");
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        PhysicalGroupDto selectByPhysicalGroupName =
            physicalGroupService.selectByPhysicalGroupName(vo.getPhysicalGroupName(), loginUser.getOrgId());
        if (Objects.nonNull(selectByPhysicalGroupName)
            && !Objects.equals(vo.getPhysicalGroupId(), selectByPhysicalGroupName.getPhysicalGroupId())) {
            throw new LimsException("当前体检单位名称已存在");
        }
        final HspOrganizationDto hspOrganizationDto = hspOrganizationService.selectByHspOrgId(vo.getHspOrgId());
        if (Objects.isNull(hspOrganizationDto)) {
            throw new LimsException("选择送检机构不存在");
        }

        final PhysicalGroupDto physicalGroupDto = new PhysicalGroupDto();
        BeanUtils.copyProperties(physicalGroupDtoNow, physicalGroupDto);

        // 更新项
        physicalGroupDto.setHspOrgId(hspOrganizationDto.getHspOrgId());
        physicalGroupDto.setHspOrgName(hspOrganizationDto.getHspOrgName());
        physicalGroupDto.setPhysicalGroupName(vo.getPhysicalGroupName());
        physicalGroupDto.setContactUser(StringUtils.defaultString(vo.getContactUser()));
        physicalGroupDto.setContactPhone(StringUtils.defaultString(vo.getContactPhone()));
        physicalGroupDto.setEnable(vo.getEnable());

        physicalGroupService.updateByPhysicalGroupId(physicalGroupDto);

        String compare = new CompareUtils<PhysicalGroupDto>().compare(physicalGroupDtoNow, physicalGroupDto);
        // 记录操作日志
        if (StringUtils.isNotBlank(compare)) {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.PHYSICAL_GROUP_LOG.getDesc())
                    .setContent(String.format("修改体检单位: [%s]", compare)).toJSONString());
        }

        return Collections.emptyMap();

    }

    /**
     * 体检单位 获取 所有 查看
     */
    @PostMapping("/select-all")
    public Object physicalGroupList(@RequestBody(required = false) PhysicalGroupListRequestVo vo) {
        final LoginUserHandler.User loginUser = LoginUserHandler.get();
        List<PhysicalGroupDto> physicalGroupDtos = physicalGroupService.selectByOrgId(loginUser.getOrgId());

        if (Objects.nonNull(vo) && Objects.nonNull(vo.getEnable())) {
            physicalGroupDtos = physicalGroupDtos.stream()
                .filter(obj -> Objects.equals(obj.getEnable(), vo.getEnable())).collect(Collectors.toList());
        }

        // 所有 体检单位id
        Set<Long> physicalGroupIdList =
            physicalGroupDtos.stream().map(PhysicalGroupDto::getPhysicalGroupId).collect(Collectors.toSet());

        // 对应体检套餐
        List<PackageDto> packageDtos =
            packageService.selectByGroupIds(physicalGroupIdList);

        // 套餐以体检单位 分组
        Map<Long, List<PackageDto>> groupingByPhysicalGroupId = packageDtos.stream()
            .collect(Collectors.groupingBy(PackageDto::getPhysicalGroupId));

        List<SelectPhysicalGroupListResponseVo> targetList = Lists.newArrayListWithCapacity(physicalGroupDtos.size());

        physicalGroupDtos.forEach(item -> {
            SelectPhysicalGroupListResponseVo temp =
                JSON.parseObject(JSON.toJSONString(item), SelectPhysicalGroupListResponseVo.class);
            temp.setReferenceByGroupPackage(
                CollectionUtils.isNotEmpty(groupingByPhysicalGroupId.get(item.getPhysicalGroupId())));
            targetList.add(temp);
        });
        return targetList;
    }

    /**
     * 检查 体检单位 新增 或 修改 参数 公共部分
     *
     */
    private <T extends PhysicalGroupAddRequestVo> void checkVoWhenAddOrUpdate(T vo) {
        if (StringUtils.length(vo.getPhysicalGroupName()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("体检单位名称不能超过 %s 个字符", INPUT_MAX_LENGTH));
        }
        if (StringUtils.isNotBlank(vo.getContactUser()) && StringUtils.length(vo.getContactUser()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("单位联系人不能超过 %s 个字符", INPUT_MAX_LENGTH));
        }
        if (StringUtils.isNotBlank(vo.getContactPhone())
            && StringUtils.length(vo.getContactPhone()) > INPUT_MAX_LENGTH) {
            throw new IllegalArgumentException(String.format("联系电话不能超过 %s 个字符", INPUT_MAX_LENGTH));
        }
        if (!(Objects.equals(vo.getEnable(), YesOrNoEnum.NO.getCode())
            || Objects.equals(vo.getEnable(), YesOrNoEnum.YES.getCode()))) {
            throw new IllegalArgumentException("是否启用参数错误");
        }
    }
}
