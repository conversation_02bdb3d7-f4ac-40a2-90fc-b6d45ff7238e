package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.config.ThreadPoolConfig;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.InstrumentReportItemCommonPhraseDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemCommonPhraseService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.mapper.TbInstrumentReportItemCommonPhraseMapper;
import com.labway.lims.base.mapstruct.InstrumentReportItemCommonPhraseConverter;
import com.labway.lims.base.model.TbInstrumentReportItemCommonPhrase;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@CacheConfig(cacheNames = "instrument-report-item-common-phrase")
public class InstrumentReportItemCommonPhraseServiceImpl implements InstrumentReportItemCommonPhraseService {
    @Resource
    private TbInstrumentReportItemCommonPhraseMapper instrumentReportItemCommonPhraseMapper;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private InstrumentReportItemCommonPhraseService instrumentReportItemCommonPhraseService;

    @DubboReference
    private RabbitMQService rabbitMQService;
    @Resource
    private InstrumentReportItemCommonPhraseConverter converter;
    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @Override
    @CacheEvict(allEntries = true)
    public long addInstrumentReportItemCommonPhrase(InstrumentReportItemCommonPhraseDto dto) {
        final Long instrumentReportItemId = dto.getInstrumentReportItemId();

        final InstrumentReportItemDto instrumentReportItem = instrumentReportItemService.selectByInstrumentReportItemId(instrumentReportItemId);

        if (Objects.isNull(instrumentReportItem)) {
            throw new IllegalArgumentException("报告项目不存在");
        }

        if (selectByInstrumentReportItemId(instrumentReportItemId).stream().anyMatch(e -> Objects.equals(e.getKeyShort(), dto.getKeyShort()))) {
            throw new IllegalArgumentException(String.format("快捷键 [%s] 已存在", dto.getKeyShort()));
        }


        final TbInstrumentReportItemCommonPhrase instrumentReportItemCommonPhrase = new TbInstrumentReportItemCommonPhrase();
        instrumentReportItemCommonPhrase.setInstrumentReportItemCommonPhraseId(snowflakeService.genId());
        instrumentReportItemCommonPhrase.setInstrumentReportItemId(instrumentReportItemId);
        instrumentReportItemCommonPhrase.setReportItemCode(instrumentReportItem.getReportItemCode());
        instrumentReportItemCommonPhrase.setReportItemName(instrumentReportItem.getReportItemName());
        instrumentReportItemCommonPhrase.setInstrumentId(instrumentReportItem.getInstrumentId());
        instrumentReportItemCommonPhrase.setInstrumentCode(instrumentReportItem.getInstrumentCode());
        instrumentReportItemCommonPhrase.setInstrumentName(instrumentReportItem.getInstrumentName());
        instrumentReportItemCommonPhrase.setKeyShort(StringUtils.defaultString(dto.getKeyShort()));
        instrumentReportItemCommonPhrase.setSort(ObjectUtils.defaultIfNull(dto.getSort(), 1));
        instrumentReportItemCommonPhrase.setContent(StringUtils.defaultString(dto.getContent()));
        instrumentReportItemCommonPhrase.setIsDefault(ObjectUtils.defaultIfNull(dto.getIsDefault(), YesOrNoEnum.NO.getCode()));
        instrumentReportItemCommonPhrase.setIsDelete(YesOrNoEnum.NO.getCode());
        instrumentReportItemCommonPhrase.setCreateDate(new Date());
        instrumentReportItemCommonPhrase.setUpdateDate(new Date());
        instrumentReportItemCommonPhrase.setCreatorId(LoginUserHandler.get().getUserId());
        instrumentReportItemCommonPhrase.setCreatorName(LoginUserHandler.get().getNickname());
        instrumentReportItemCommonPhrase.setUpdaterId(LoginUserHandler.get().getUserId());
        instrumentReportItemCommonPhrase.setUpdaterName(LoginUserHandler.get().getNickname());

        // 校验是否冲突
        if (Objects.equals(instrumentReportItemCommonPhrase.getIsDefault(), YesOrNoEnum.YES.getCode())) {
            if (selectByInstrumentReportItemId(instrumentReportItemId).stream().anyMatch(e -> Objects.equals(e.getIsDefault(), 1))) {
                throw new IllegalStateException("默认值冲突，只能存在一个默认值");
            }
        }

        if (instrumentReportItemCommonPhraseMapper.insert(instrumentReportItemCommonPhrase) < 1) {
            throw new IllegalStateException("添加常用短语失败");
        }

        log.info("用户 [{}] 新增常用短语 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(instrumentReportItemCommonPhrase));


        // 日志
        String logContent = String.format("用户 [%s] 添加了[%s]仪器-[%s]报告项目常用短语: 快捷键 [%s] 是否默认 [%s] 排序号 [%s] 短语内容 [%s]",
                LoginUserHandler.get().getNickname(),
                instrumentReportItemCommonPhrase.getInstrumentName(),
                instrumentReportItemCommonPhrase.getReportItemName(),
                dto.getKeyShort(),
                YesOrNoEnum.selectByCode(dto.getIsDefault()).getDesc(),
                dto.getSort(),
                dto.getContent());
        rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM_COMMON_PHRASE.getDesc())
                        .setContent(logContent).toJSONString());

        return instrumentReportItemCommonPhrase.getInstrumentReportItemCommonPhraseId();
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean deleteByInstrumentReportItemCommonPhraseId(long instrumentReportItemCommonPhraseId) {
        return instrumentReportItemCommonPhraseMapper.deleteById(instrumentReportItemCommonPhraseId) > 0;
    }

    @Override
    @Cacheable(key = "'selectByInstrumentReportItemId:' + #instrumentReportItemId")
    public List<InstrumentReportItemCommonPhraseDto> selectByInstrumentReportItemId(long instrumentReportItemId) {
        return instrumentReportItemCommonPhraseMapper.selectList(new LambdaQueryWrapper<TbInstrumentReportItemCommonPhrase>()
                        .orderByDesc(TbInstrumentReportItemCommonPhrase::getInstrumentReportItemCommonPhraseId)
                        .eq(TbInstrumentReportItemCommonPhrase::getInstrumentReportItemId, instrumentReportItemId))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<InstrumentReportItemCommonPhraseDto> selectByInstrumentReportItemIds(Collection<Long> instrumentReportItemIds) {
        if (CollectionUtils.isEmpty(instrumentReportItemIds)) {
            return Collections.emptyList();
        }
        return instrumentReportItemCommonPhraseMapper.selectList(new LambdaQueryWrapper<TbInstrumentReportItemCommonPhrase>()
                        .orderByDesc(TbInstrumentReportItemCommonPhrase::getInstrumentReportItemCommonPhraseId)
                        .in(TbInstrumentReportItemCommonPhrase::getInstrumentReportItemId, instrumentReportItemIds))
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    @CacheEvict(allEntries = true)
    public boolean updateByInstrumentReportItemCommonPhraseId(InstrumentReportItemCommonPhraseDto dto) {

        final InstrumentReportItemCommonPhraseDto old = selectByInstrumentReportItemCommonPhraseId(dto.getInstrumentReportItemCommonPhraseId());
        if (Objects.isNull(old)) {
            throw new IllegalStateException("常用短语不存在");
        }

        final TbInstrumentReportItemCommonPhrase instrumentReportItemCommonPhrase = new TbInstrumentReportItemCommonPhrase();
        instrumentReportItemCommonPhrase.setInstrumentReportItemCommonPhraseId(dto.getInstrumentReportItemCommonPhraseId());
        instrumentReportItemCommonPhrase.setKeyShort(dto.getKeyShort());
        instrumentReportItemCommonPhrase.setSort(dto.getSort());
        instrumentReportItemCommonPhrase.setContent(dto.getContent());
        instrumentReportItemCommonPhrase.setIsDefault(dto.getIsDefault());
        instrumentReportItemCommonPhrase.setUpdateDate(new Date());
        instrumentReportItemCommonPhrase.setUpdaterId(LoginUserHandler.get().getUserId());
        instrumentReportItemCommonPhrase.setUpdaterName(LoginUserHandler.get().getNickname());


        final List<InstrumentReportItemCommonPhraseDto> phrases = selectByInstrumentReportItemId(old.getInstrumentReportItemId());
        // 校验是否冲突
        if (Objects.equals(instrumentReportItemCommonPhrase.getIsDefault(), YesOrNoEnum.YES.getCode())) {
            if (phrases.stream().anyMatch(e -> Objects.equals(e.getIsDefault(), 1)
                    && !Objects.equals(old.getInstrumentReportItemCommonPhraseId(), e.getInstrumentReportItemCommonPhraseId()))) {
                throw new IllegalStateException("默认值冲突，只能存在一个默认值");
            }
        }


        if (phrases.stream().filter(e -> !Objects.equals(dto.getInstrumentReportItemCommonPhraseId(), e.getInstrumentReportItemCommonPhraseId()))
                .anyMatch(e -> Objects.equals(e.getKeyShort(), dto.getKeyShort()))) {
            throw new IllegalArgumentException(String.format("快捷键 [%s] 已存在", dto.getKeyShort()));
        }


        if (instrumentReportItemCommonPhraseMapper.updateById(instrumentReportItemCommonPhrase) < 1) {
            throw new IllegalStateException("修改常用短语失败");
        }

        log.info("用户 [{}] 修改常用短语 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(instrumentReportItemCommonPhrase));

        return true;
    }

    @Nullable
    @Override
    @Cacheable(key = "'selectByInstrumentReportItemCommonPhraseId:' + #instrumentReportItemCommonPhraseId")
    public InstrumentReportItemCommonPhraseDto selectByInstrumentReportItemCommonPhraseId(long instrumentReportItemCommonPhraseId) {
        return convert(instrumentReportItemCommonPhraseMapper.selectById(instrumentReportItemCommonPhraseId));
    }

    @Override
    @Cacheable(key = "'selectByInstrumentGroupId:' + #instrumentGroupId")
    public List<InstrumentReportItemCommonPhraseDto> selectByInstrumentGroupId(long instrumentGroupId) {
        return instrumentReportItemCommonPhraseMapper.selectByInstrumentGroupId(instrumentGroupId);
    }

    @Override
    public List<InstrumentReportItemCommonPhraseDto> selectByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return instrumentReportItemCommonPhraseMapper.selectBatchIds(ids)
                .stream().map(this::convert).collect(Collectors.toList());
    }

    @CacheEvict(allEntries = true)
    @Override
    public void deleteByInstrumentReportItemId(long instrumentReportItemId) {
        instrumentReportItemCommonPhraseMapper.delete(new LambdaQueryWrapper<TbInstrumentReportItemCommonPhrase>()
                .eq(TbInstrumentReportItemCommonPhrase::getInstrumentReportItemId, instrumentReportItemId));
    }

    private InstrumentReportItemCommonPhraseDto convert(TbInstrumentReportItemCommonPhrase phrase) {
        if (Objects.isNull(phrase)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(phrase), InstrumentReportItemCommonPhraseDto.class);
    }

    /**
     * 拷贝 仪器报告项目常用短语
     * @param fromInstrumentReportItemId
     * @param instrumentReportItemDto
     * @return
     */
    @Override
    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public List<Long> copyReportItemCommonPhrase(Long fromInstrumentReportItemId, InstrumentReportItemDto instrumentReportItemDto) {
        List<Long> ids = new ArrayList<>();
        // 仪器报告项目ID
        Long instrumentReportItemId = instrumentReportItemDto.getInstrumentReportItemId();

        {
            // 删除原有的 仪器报告项目常用短语
            LambdaQueryWrapper<TbInstrumentReportItemCommonPhrase> wrapper =
                    Wrappers
                            .lambdaQuery(TbInstrumentReportItemCommonPhrase.class)
                            .eq(TbInstrumentReportItemCommonPhrase::getInstrumentReportItemId, instrumentReportItemId);
            instrumentReportItemCommonPhraseMapper.delete(wrapper);
        }

        Date current = new Date();
        List<InstrumentReportItemCommonPhraseDto> commonPhraseDtos =
                selectByInstrumentReportItemId(fromInstrumentReportItemId);
        for (InstrumentReportItemCommonPhraseDto commonPhraseDto : commonPhraseDtos) {
            TbInstrumentReportItemCommonPhrase commonPhrase = converter.convertDto2Entity(commonPhraseDto);

            commonPhrase.setInstrumentReportItemCommonPhraseId(snowflakeService.genId());
            commonPhrase.setInstrumentReportItemId(instrumentReportItemId);

            commonPhrase.setReportItemCode(instrumentReportItemDto.getReportItemCode());
            commonPhrase.setReportItemName(instrumentReportItemDto.getReportItemName());
            commonPhrase.setInstrumentId(instrumentReportItemDto.getInstrumentId());
            commonPhrase.setInstrumentCode(instrumentReportItemDto.getInstrumentCode());
            commonPhrase.setInstrumentName(instrumentReportItemDto.getInstrumentName());
            // 快捷键
            commonPhrase.setIsDelete(YesOrNoEnum.NO.getCode());
            commonPhrase.setCreateDate(current);
            commonPhrase.setCreatorId(LoginUserHandler.get().getUserId());
            commonPhrase.setCreatorName(LoginUserHandler.get().getNickname());
            commonPhrase.setUpdateDate(current);
            commonPhrase.setUpdaterId(LoginUserHandler.get().getUserId());
            commonPhrase.setUpdaterName(LoginUserHandler.get().getNickname());

            // 校验是否冲突
            if (Objects.equals(commonPhrase.getIsDefault(), YesOrNoEnum.YES.getCode())) {
                if (selectByInstrumentReportItemId(instrumentReportItemId).stream().anyMatch(e -> Objects.equals(e.getIsDefault(), 1))) {
                    throw new IllegalStateException("默认值冲突，只能存在一个默认值");
                }
            }

            if (instrumentReportItemCommonPhraseMapper.insert(commonPhrase) < 1) {
                throw new IllegalStateException("拷贝常用短语失败");
            }

            log.info("用户 [{}] 拷贝常用短语 [{}] 成功", LoginUserHandler.get().getNickname(), JSON.toJSONString(commonPhrase));

            // 日志
            String logContent = String.format("用户 [%s] 拷贝了[%s]仪器-[%s]报告项目常用短语: 快捷键 [%s] 是否默认 [%s] 排序号 [%s] 短语内容 [%s]",
                    LoginUserHandler.get().getNickname(),
                    commonPhrase.getInstrumentName(),
                    commonPhrase.getReportItemName(),
                    commonPhrase.getKeyShort(),
                    YesOrNoEnum.selectByCode(commonPhrase.getIsDefault()).getDesc(),
                    commonPhrase.getSort(),
                    commonPhrase.getContent());


            // 异步发送消息
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                    TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_REPORT_ITEM_COMMON_PHRASE.getDesc())
                            .setContent(logContent).toJSONString());

            ids.add(commonPhrase.getInstrumentReportItemCommonPhraseId());
        }

        return ids;
    }

    @Override
    public List<InstrumentReportItemCommonPhraseDto> selectByReportItemCodes(Collection<String> reportItemCodes) {
        if (CollectionUtils.isEmpty(reportItemCodes)) {
            return Collections.emptyList();
        }
        return instrumentReportItemCommonPhraseMapper.selectList(Wrappers.<TbInstrumentReportItemCommonPhrase>lambdaQuery()
                .in(TbInstrumentReportItemCommonPhrase::getReportItemCode, reportItemCodes))
                .stream().map(this::convert).collect(Collectors.toList());
    }
}
