package com.labway.lims.base.service;

import com.labway.lims.api.EnvDetector;
import com.labway.lims.api.Snowflake;
import com.labway.lims.api.service.SnowflakeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.beans.factory.InitializingBean;

import javax.annotation.Resource;
import java.util.LinkedList;

/**
 * SnowflakeServiceImpl
 */
@Slf4j
@DubboService(interfaceClass = SnowflakeService.class)
class SnowflakeServiceImpl implements SnowflakeService, InitializingBean {

    private Snowflake snowflake;

    @Resource
    private EnvDetector envDetector;

    @Trace
    @Override
    public long genId() {
        // log.info("TraceLog {}", TraceLog.newInstance());
        return snowflake.nextId();
    }

    @Override
    public LinkedList<Long> genIds(int count) {
        count = Math.max(count, 1);
        final LinkedList<Long> ids = new LinkedList<>();
        for (int i = 0; i < count; i++) {
            ids.add(genId());
        }
        return ids;
    }

    @Override
    public long getGenerateDateTime(long id) {
        return snowflake.getGenerateDateTime(id);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        final long workerId = RandomUtils.nextLong(0, 32);
        final long dataCenterId = RandomUtils.nextLong(0, 32);

        snowflake = new Snowflake(workerId, dataCenterId);

        log.info("init snowflake workerId {} dataCenterId {}", workerId, dataCenterId);

    }

}
