package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/5/8 17:26
 */
@Getter
@Setter
public class GroupMaterialVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物料ID
     */
    private Long groupMaterialId;

    /**
     * 物资编号
     */
    private String materialCode;

    /**
     * 物资类别
     */
    private String type;

    /**
     * 物资名称
     */
    private String materialName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 方法学
     */
    private String methodology;

    /**
     * 注册证号
     */
    private String registrationNumber;

    /**
     * 注册证名称
     */
    private String registrationName;

    /**
     * 储存温度
     */
    private String storageTemperature;

    /**
     * 厂家
     */
    private String manufacturers;

    /**
     * 主单位总库存
     */
    private Integer mainUnitInventory;

    /**
     * 主单位
     */
    private String mainUnit;

    /**
     * 辅单位
     */
    private String assistUnit;

    /**
     *
     */
    private BigDecimal assistUintInventory;

    /**
     * 主辅单位换算率
     */
    private String unitConversionRate;

    /**
     * 有效期
     */
    private Date validDate;

    /**
     * 有效期提醒时间
     */
    private Date validRemindDate;

    /**
     * 开瓶有效期
     */
    private Date openValidDate;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * group_name
     */
    private String groupName;

    /**
     * org_id
     */
    private Long orgId;

    /**
     * org_name
     */
    private String orgName;

    /**
     * create_date
     */
    private Date createDate;

    /**
     * update_date
     */
    private Date updateDate;

    /**
     * updater_id
     */
    private Long updaterId;

    /**
     * updater_name
     */
    private String updaterName;

    /**
     * creator_id
     */
    private Long creatorId;

    /**
     * creator_name
     */
    private String creatorName;

    /**
     * is_delete
     */
    private Integer isDelete;

}
