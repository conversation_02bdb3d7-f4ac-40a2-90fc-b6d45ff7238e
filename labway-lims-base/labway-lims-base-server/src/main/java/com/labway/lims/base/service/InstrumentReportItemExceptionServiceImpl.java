package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.InstrumentReportItemExceptionDto;
import com.labway.lims.base.api.service.InstrumentReportItemExceptionService;
import com.labway.lims.base.mapper.TbInstrumentReportItemExceptionMapper;
import com.labway.lims.base.mapstruct.InstrumentReportItemExceptionConvert;
import com.labway.lims.base.model.TbInstrumentReportItemException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class InstrumentReportItemExceptionServiceImpl implements InstrumentReportItemExceptionService {
    @Resource
    private TbInstrumentReportItemExceptionMapper instrumentReportItemExceptionMapper;
    @DubboReference
    private SnowflakeService snowflakeService;
    @Resource
    private InstrumentReportItemExceptionConvert instrumentReportItemExceptionConvert;

    @Override
    public List<InstrumentReportItemExceptionDto> selectByInstrumentReportItemId(long instrumentReportItemId) {
        return instrumentReportItemExceptionMapper.selectList(new LambdaQueryWrapper<TbInstrumentReportItemException>()
                        .eq(TbInstrumentReportItemException::getInstrumentReportItemId, instrumentReportItemId)
                        .orderByDesc(TbInstrumentReportItemException::getInstrumentReportItemExceptionId))
                .stream().map(instrumentReportItemExceptionConvert::convert).collect(Collectors.toList());
    }

    @Override
    public void deleteByInstrumentReportItemExceptionId(long instrumentReportItemExceptionId) {
        instrumentReportItemExceptionMapper.deleteById(instrumentReportItemExceptionId);
        log.info("用户 [{}] 删除异常结果成功 [{}]", LoginUserHandler.get().getNickname(), instrumentReportItemExceptionId);
    }

    @Override
    public boolean updateByInstrumentReportItemExceptionId(InstrumentReportItemExceptionDto dto) {

        if (selectByInstrumentReportItemId(dto.getInstrumentReportItemId()).stream()
                .anyMatch(e -> Objects.equals(e.getFormulaMax(), dto.getFormulaMax())
                        && Objects.equals(e.getFormulaMaxValue(), dto.getFormulaMaxValue())
                        && Objects.equals(e.getFormulaMin(), dto.getFormulaMin())
                        && Objects.equals(e.getFormulaMinValue(), dto.getFormulaMinValue())
                        && !Objects.equals(e.getInstrumentReportItemExceptionId(), dto.getInstrumentReportItemExceptionId())
                )) {
            throw new IllegalArgumentException(String.format("结果值已存在！", dto.getResult()));
        }

        if (instrumentReportItemExceptionMapper.updateById(instrumentReportItemExceptionConvert.convert(dto)) > 0) {
            log.info("用户 [{}] 修改异常结果成功 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(dto));
            return true;
        }
        log.error("用户 [{}] 修改异常结果失败 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(dto));
        return false;
    }

    @Override
    public long addInstrumentReportItemException(InstrumentReportItemExceptionDto dto) {

        if (selectByInstrumentReportItemId(dto.getInstrumentReportItemId()).stream()
//                .anyMatch(e -> Objects.equals(e.getResult(), dto.getResult()))
                .anyMatch(e -> Objects.equals(e.getFormulaMax(), dto.getFormulaMax())
                        && Objects.equals(e.getFormulaMaxValue(), dto.getFormulaMaxValue())
                        && Objects.equals(e.getFormulaMin(), dto.getFormulaMin())
                        && Objects.equals(e.getFormulaMinValue(), dto.getFormulaMinValue()))
        ) {
//            throw new IllegalArgumentException(String.format("结果值 [%s] 已存在", dto.getResult()));
            throw new IllegalArgumentException("异常结果值已存在！");
        }

        final TbInstrumentReportItemException convert = instrumentReportItemExceptionConvert.convert(dto);

        convert.setIsDelete(YesOrNoEnum.NO.getCode());
        convert.setCreateDate(new Date());
        convert.setUpdateDate(new Date());
        convert.setCreatorId(LoginUserHandler.get().getUserId());
        convert.setCreatorName(LoginUserHandler.get().getNickname());
        convert.setUpdaterId(LoginUserHandler.get().getUserId());
        convert.setUpdaterName(LoginUserHandler.get().getNickname());

        convert.setInstrumentReportItemExceptionId(Optional.ofNullable(convert.getInstrumentReportItemExceptionId()).orElseGet(() -> snowflakeService.genId()));

        instrumentReportItemExceptionMapper.insert(convert);

        log.info("用户 [{}] 新增异常结果成功 [{}]", LoginUserHandler.get().getNickname(), JSON.toJSONString(convert));

        return convert.getInstrumentReportItemExceptionId();
    }
}
