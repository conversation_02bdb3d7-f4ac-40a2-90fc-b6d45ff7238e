package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <p>
 * ReportItemCopyVo
 * 报告项目复制
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/26 15:50
 */
@Getter
@Setter
public class ReportItemCopyVo {

    /**
     * 仪器ID
     */
    private Long originInstrumentId;

    /**
     * 仪器报告项目ID
     */
    private List<Long> originInstrumentReportItemIds;

    /**
     * 拷贝到 专业组-仪器
     */
    private List<Long> targetInstrumentIds;

}
