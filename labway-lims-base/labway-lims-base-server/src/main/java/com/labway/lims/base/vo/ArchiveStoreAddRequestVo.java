package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 归档库 新增 请求 Vo
 * 
 * <AUTHOR>
 * @since 2023/4/3 11:06
 */
@Getter
@Setter
public class ArchiveStoreAddRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 归档库编码
     */
    private String archiveStoreCode;

    /**
     * 归档库名称
     */
    private String archiveStoreName;
    /**
     * 是否启用(0未启用 1启用)
     * @see YesOrNoEnum
     */
    private Integer enable;


}
