package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户阶梯折扣信息
 * 
 * <AUTHOR>
 * @since 2023/5/4 13:24
 */
@Getter
@Setter
@TableName("tb_hsp_org_pricing")
public class TbHspOrgPricing implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 阶梯折扣ID
     */
    @TableId
    private Long tieredPriceId;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 折前总额上限
     */
    private BigDecimal beforeMaxPrice;

    /**
     * 折前总额下限
     */
    private BigDecimal beforeMinPrice;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 折扣率
     */
    private BigDecimal discount;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 1:删除，0:未删
     */
    private Integer isDelete;

}
