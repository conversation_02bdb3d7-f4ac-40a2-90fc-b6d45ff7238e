package com.labway.lims.base.controller;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.lang.tree.parser.NodeParser;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.Orgs;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.base.api.dto.MenuDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.MenuService;
import com.labway.lims.base.mapper.*;
import com.labway.lims.base.model.*;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * 条码
 */
@Slf4j
@RestController
@RequestMapping("/copy")
public class CopyController extends BaseController {

    @Resource
    private GroupService groupService;
    @Resource
    private MenuService menuService;
    @Resource
    private SnowflakeService snowflakeService;
    @Resource
    private DataSource dataSource;
    @Resource
    private MybatisPlusProperties mybatisPlusProperties;

    @GetMapping(value = "/all")
    public Object all(long targetOrgId) throws Exception {
        final Orgs org = Orgs.getOrgByOrgCode(targetOrgId);
        final Long orgId = LoginUserHandler.get().getOrgId();
        final StringWriter sw = new StringWriter();
        final PrintWriter pw = new PrintWriter(sw);
        final List<TbMenu> _menus = new LinkedList<>();
        final List<TbProfessionalGroup> _groups = new LinkedList<>();
        final TbRole _role;

        final SqlSessionFactory sqlSessionFactory = getSqlSessionFactory(pw);

        // 专业组
        try (SqlSession sqlSession = sqlSessionFactory.openSession()) {
            pw.println("-- tb_professional_group");
            pw.println();
            final List<TbProfessionalGroup> groups = JSON.parseArray(JSON.toJSONString(groupService.selectByOrgId(orgId)), TbProfessionalGroup.class);
            final LinkedList<Long> ids = snowflakeService.genIds(groups.size());
            for (TbProfessionalGroup group : groups) {
                try {
                    group.setGroupId(ids.pop());
                    group.setOrgId(org.getOrgCode());
                    _groups.add(group);
                    sqlSession.getMapper(TbProfessionalGroupMapper.class)
                            .insert(group);
                } catch (Exception e) {
                    if (e.getCause() instanceof WriteException) {
                        log.info("生成SQL: {}", e.getCause().getMessage());
                    } else {
                        throw e;
                    }
                }
            }
        }

        pw.println();
        pw.println();
        pw.println();

        // 菜单
        try (SqlSession sqlSession = sqlSessionFactory.openSession()) {
            pw.println("-- tb_menu");
            pw.println();

            final TbMenuMapper menuMapper = sqlSession.getMapper(TbMenuMapper.class);
            final List<MenuDto> menus = menuService.selectAllMenu(orgId);
            TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
            treeNodeConfig.setIdKey("menuId");
            treeNodeConfig.setParentIdKey("proMenuId");
            treeNodeConfig.setChildrenKey("childList");
            final List<Tree<Long>> tree = TreeUtil.build(menus, 0L, treeNodeConfig, new NodeParser<MenuDto, Long>() {
                @Override
                public void parse(MenuDto object, Tree<Long> treeNode) {
                    treeNode.setId(object.getMenuId());
                    treeNode.setParentId(object.getProMenuId());
                    treeNode.putExtra("menu", object);
                }
            });

            findChildrenMenu(menuMapper, tree, org, 0, _menus);

        }

        pw.println();
        pw.println();
        pw.println();

        // 角色 & 角色权限
        try (SqlSession sqlSession = sqlSessionFactory.openSession()) {
            pw.println("-- tb_role");
            pw.println();

            _role = new TbRole();
            _role.setRoleId(snowflakeService.genId());
            _role.setRoleName("超级管理员");
            _role.setRoleDesc("");
            _role.setStatus(YesOrNoEnum.YES.getCode());
            _role.setOrgName(org.getOrgName());
            _role.setOrgId(org.getOrgCode());
            _role.setCreateDate(new Date());
            _role.setUpdateDate(new Date());
            _role.setUpdaterId(LoginUserHandler.get().getUserId());
            _role.setUpdaterName(LoginUserHandler.get().getNickname());
            _role.setCreatorId(LoginUserHandler.get().getUserId());
            _role.setCreatorName(LoginUserHandler.get().getNickname());
            _role.setIsDelete(YesOrNoEnum.NO.getCode());

            try {
                sqlSession.getMapper(TbRoleMapper.class).insert(_role);
            } catch (Exception ignored) {

            }

            pw.println();
            pw.println();
            pw.println();

            pw.println("-- tb_role_menu");
            pw.println();
            final LinkedList<Long> ids = snowflakeService.genIds(_menus.size());
            for (TbMenu menu : _menus) {
                final TbRoleMenu roleMenu = new TbRoleMenu();
                roleMenu.setRoleMenuId(ids.pop());
                roleMenu.setMenuId(menu.getMenuId());
                roleMenu.setRoleId(_role.getRoleId());

                roleMenu.setOrgName(org.getOrgName());
                roleMenu.setOrgId(org.getOrgCode());
                roleMenu.setCreateDate(new Date());
                roleMenu.setUpdateDate(new Date());
                roleMenu.setUpdaterId(LoginUserHandler.get().getUserId());
                roleMenu.setUpdaterName(LoginUserHandler.get().getNickname());
                roleMenu.setCreatorId(LoginUserHandler.get().getUserId());
                roleMenu.setCreatorName(LoginUserHandler.get().getNickname());
                roleMenu.setIsDelete(YesOrNoEnum.NO.getCode());

                try {
                    sqlSession.getMapper(TbRoleMenuMapper.class).insert(roleMenu);
                } catch (Exception ignored) {

                }
            }
        }


        pw.println();
        pw.println();
        pw.println();

        // 用户
        try (SqlSession sqlSession = sqlSessionFactory.openSession()) {
            pw.println("-- tb_user");
            pw.println();

            final TbUser user = new TbUser();
            user.setUserId(snowflakeService.genId());
            user.setUsername(IdUtil.simpleUUID());
            user.setNickname("密码: " + user.getUsername());
            user.setPassword(DigestUtils.md5Hex(user.getUsername() + user.getUsername()));
            user.setOrgId(org.getOrgCode());
            user.setOrgName(org.getOrgName());
            user.setStatus(YesOrNoEnum.YES.getCode());
            user.setCreateDate(new Date());
            user.setUpdateDate(new Date());
            user.setCreatorId(0L);
            user.setCreatorName("");
            user.setUpdaterId(0L);
            user.setUpdaterName("");
            user.setEnSign("");
            user.setCnSign("");
            user.setIsDelete(YesOrNoEnum.NO.getCode());
            user.setSex(0);
            user.setOrgCode(String.valueOf(org.getOrgCode()));

            try {
                sqlSession.getMapper(TbUserMapper.class).insert(user);
            } catch (Exception ignored) {
            }

            pw.println();
            pw.println();
            pw.println();

            pw.println("-- tb_user_role");
            pw.println();

            final TbUserRole userRole = new TbUserRole();
            userRole.setUserRoleId(snowflakeService.genId());
            userRole.setUserId(user.getUserId());
            userRole.setRoleId(_role.getRoleId());
            userRole.setIsDefault(YesOrNoEnum.YES.getCode());
            userRole.setOrgId(org.getOrgCode());
            userRole.setOrgName(org.getOrgName());
            userRole.setCreateDate(new Date());
            userRole.setUpdateDate(new Date());
            userRole.setUpdaterId(0L);
            userRole.setUpdaterName("");
            userRole.setCreatorId(0L);
            userRole.setCreatorName("");
            userRole.setIsDelete(YesOrNoEnum.NO.getCode());
            try {
                sqlSession.getMapper(TbUserRoleMapper.class).insert(userRole);
            } catch (Exception ignored) {

            }


            pw.println();
            pw.println();
            pw.println();

            pw.println("-- tb_user_group");
            pw.println();

            final LinkedList<Long> ids = snowflakeService.genIds(_groups.size());
            for (TbProfessionalGroup group : _groups) {
                final TbUserGroup userGroup = new TbUserGroup();
                userGroup.setUserGroupId(ids.pop());
                userGroup.setUserId(user.getUserId());
                userGroup.setGroupId(group.getGroupId());
                userGroup.setIsDefault(0);
                userGroup.setOrgId(org.getOrgCode());
                userGroup.setOrgName(org.getOrgName());
                userGroup.setCreateDate(new Date());
                userGroup.setUpdateDate(new Date());
                userGroup.setUpdaterId(0L);
                userGroup.setUpdaterName("");
                userGroup.setCreatorId(0L);
                userGroup.setCreatorName("");
                userGroup.setIsDelete(YesOrNoEnum.NO.getCode());
                try {
                    sqlSession.getMapper(TbUserGroupMapper.class).insert(userGroup);
                } catch (Exception ignored) {
                }

            }

        }

        IOUtils.closeQuietly(pw, sw);

        return ResponseEntity.status(HttpStatus.OK).header(HttpHeaders.CONTENT_TYPE, "text/x-sql;charset=utf-8").body(sw.toString());
    }

    void findChildrenMenu(TbMenuMapper menuMapper, List<Tree<Long>> tree, Orgs org, long newParentMenuId, List<TbMenu> _menus) {
        if (CollectionUtils.isEmpty(tree)) {
            return;
        }

        final LinkedList<Long> ids = snowflakeService.genIds(tree.size());
        for (Tree<Long> e : tree) {
            final MenuDto menu = (MenuDto) e.get("menu");
            final TbMenu m = JSON.parseObject(JSON.toJSONString(menu), TbMenu.class);
            try {
                m.setOrgId(org.getOrgCode());
                m.setProMenuId(newParentMenuId);
                m.setMenuId(ids.pop());
                _menus.add(m);
                menuMapper.insert(m);
            } catch (Exception ignored) {

            }

            findChildrenMenu(menuMapper, e.getChildren(), org, m.getMenuId(), _menus);
        }

    }

    private SqlSessionFactory getSqlSessionFactory(PrintWriter pw) throws Exception {
        final MybatisSqlSessionFactoryBean ssfb = new MybatisSqlSessionFactoryBean();
        ssfb.setDataSource(dataSource);
        ssfb.setGlobalConfig(mybatisPlusProperties.getGlobalConfig());
        ssfb.setMapperLocations(mybatisPlusProperties.resolveMapperLocations());

        final MybatisConfiguration mybatisConfiguration = new MybatisConfiguration(mybatisPlusProperties.getConfiguration().getEnvironment());
        mybatisConfiguration.setVariables(mybatisPlusProperties.getConfiguration().getVariables());
        mybatisConfiguration.setConfigurationFactory(mybatisPlusProperties.getConfiguration().getConfigurationFactory());
        mybatisConfiguration.setAutoMappingBehavior(mybatisPlusProperties.getConfiguration().getAutoMappingBehavior());
        for (Class<?> mapper : mybatisPlusProperties.getConfiguration().getMapperRegistry().getMappers()) {
            mybatisConfiguration.getMapperRegistry().addMapper(mapper);
        }
        ssfb.setConfiguration(mybatisConfiguration);

        ssfb.setPlugins(new InsertInterceptor(pw));
        ssfb.afterPropertiesSet();
        return Objects.requireNonNull(ssfb.getObject());
    }

    static class WriteException extends RuntimeException {
        WriteException(String msg) {
            super(msg);
        }
    }

    private static class IMybatisConfiguration extends MybatisConfiguration {
        @Delegate
        private final MybatisConfiguration configuration;

        private IMybatisConfiguration(MybatisConfiguration configuration) {
            this.configuration = configuration;
        }

    }

    @Intercepts(@Signature(type = Executor.class, method = "update", args = {MappedStatement.class,
            Object.class}))
    private class InsertInterceptor implements Interceptor {

        private final PrintWriter pw;

        InsertInterceptor(PrintWriter pw) {
            this.pw = pw;
        }


        @Override
        public Object intercept(Invocation invocation) throws Throwable {
            final MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
            if (mappedStatement.getSqlCommandType() != SqlCommandType.INSERT) {
                return invocation.proceed();
            }

            Object parameter = null;
            if (invocation.getArgs().length > 1) {
                parameter = invocation.getArgs()[1];
            }

            showSql(mybatisPlusProperties.getConfiguration(), mappedStatement.getBoundSql(parameter));

            return invocation.proceed();
        }


        private void showSql(Configuration configuration, BoundSql boundSql) {
            Object parameterObject = boundSql.getParameterObject();
            List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
            //替换空格、换行、tab缩进等
            String sql = boundSql.getSql().replaceAll("[\\s]+", " ");
            if (!parameterMappings.isEmpty() && parameterObject != null) {
                TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
                if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
                    sql = sql.replaceFirst("\\?", getParameterValue(parameterObject));
                } else {
                    MetaObject metaObject = configuration.newMetaObject(parameterObject);
                    for (ParameterMapping parameterMapping : parameterMappings) {
                        String propertyName = parameterMapping.getProperty();
                        if (metaObject.hasGetter(propertyName)) {
                            Object obj = metaObject.getValue(propertyName);
                            sql = sql.replaceFirst("\\?", getParameterValue(obj));
                        } else if (boundSql.hasAdditionalParameter(propertyName)) {
                            Object obj = boundSql.getAdditionalParameter(propertyName);
                            sql = sql.replaceFirst("\\?", getParameterValue(obj));
                        }
                    }
                }
            }

            pw.print(sql);
            pw.println(';');

            throw new WriteException(sql);
        }

        private String getParameterValue(Object obj) {
            String value;
            if (obj instanceof String) {
                value = "'" + obj.toString() + "'";
            } else if (obj instanceof Date) {
                value = "'" + DateFormatUtils.format((Date) obj, "yyyy-MM-dd HH:mm:ss.SSS") + "'";
            } else {
                if (obj != null) {
                    value = obj.toString();
                } else {
                    value = "";
                }
            }
            return value.replace("$", "\\$");
        }

    }


}
