package com.labway.lims.base.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 项目基准包 检验项目修改
 *
 * <AUTHOR>
 * @since 2023/7/31 19:05
 */
@Getter
@Setter
public class UpdateItemPriceBasePackageDetailRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 明细id
     */
    private Long detailId;

    /**
     * 收费价格
     */
    private String price;

}
