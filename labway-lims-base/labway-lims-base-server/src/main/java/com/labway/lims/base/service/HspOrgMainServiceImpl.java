package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.HspOrgMainDto;
import com.labway.lims.base.api.service.HspOrgMainService;
import com.labway.lims.base.mapper.TbHspOrgMainMapper;
import com.labway.lims.base.mapstruct.HspOrgMainConverter;
import com.labway.lims.base.model.TbHspOrgMain;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/27 11:19
 */
@Slf4j
@DubboService
public class HspOrgMainServiceImpl implements HspOrgMainService {
    @Resource
    private TbHspOrgMainMapper tbHspOrgMainMapper;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Resource
    private HspOrgMainConverter hspOrgMainConverter;

    @Override
    public List<HspOrgMainDto> selectAll() {

        final LambdaQueryWrapper<TbHspOrgMain> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbHspOrgMain::getOrgId, LoginUserHandler.get().getOrgId()).orderByAsc(TbHspOrgMain::getHspOrgMainId);

        return hspOrgMainConverter.hspOrgMainDtoListFromTbObj(tbHspOrgMainMapper.selectList(wrapper));
    }

    @Override
    public List<Long> addBatch(Collection<HspOrgMainDto> dtos) {

        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }
        final LinkedList<Long> ids = snowflakeService.genIds(dtos.size());
        final LoginUserHandler.User user = LoginUserHandler.get();
        dtos.forEach(e -> {
            e.setHspOrgMainId(ObjectUtils.defaultIfNull(e.getHspOrgMainId(), ids.pop()));
            e.setOrgId(user.getOrgId());
            e.setOrgName(user.getOrgName());
            e.setUpdateDate(new Date());
            e.setUpdaterId(user.getUserId());
            e.setUpdaterName(user.getNickname());
            e.setCreateDate(new Date());
            e.setCreatorId(user.getUserId());
            e.setCreatorName(user.getNickname());
            e.setIsDelete(YesOrNoEnum.NO.getCode());
        });

        if (tbHspOrgMainMapper.addBatch(dtos) < 1) {
            throw new IllegalStateException("添加失败");
        }
        log.info("用户 [{}] 添加 送检机构成功 [{}]", user.getUsername(), JSON.toJSONString(dtos));

        return dtos.stream().map(HspOrgMainDto::getHspOrgMainId).collect(Collectors.toList());
    }

    @Override
    public void deleteByHspOrgMainIds(Collection<Long> hspOrgMainIds) {

        if (CollectionUtils.isEmpty(hspOrgMainIds)) {
            return;
        }
        tbHspOrgMainMapper.deleteBatchIds(hspOrgMainIds);
    }

    @Override
    public List<HspOrgMainDto> selectByHspOrgId(long hspOrgId) {

        final LambdaQueryWrapper<TbHspOrgMain> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbHspOrgMain::getOrgId, LoginUserHandler.get().getOrgId()).eq(TbHspOrgMain::getHspOrgId, hspOrgId);

        return hspOrgMainConverter.hspOrgMainDtoListFromTbObj(tbHspOrgMainMapper.selectList(wrapper));
    }

    @Override
    public List<HspOrgMainDto> selectByHspOrgIds(Collection<Long> hspOrgMainIds) {
        if (CollectionUtils.isEmpty(hspOrgMainIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbHspOrgMain> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbHspOrgMain::getHspOrgMainId, hspOrgMainIds);
        queryWrapper.eq(TbHspOrgMain::getIsDelete, YesOrNoEnum.NO.getCode());
        return hspOrgMainConverter.hspOrgMainDtoListFromTbObj(tbHspOrgMainMapper.selectList(queryWrapper));
    }

}
