package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.HspOrgDeptDto;
import com.labway.lims.base.api.service.HspOrgDeptService;
import com.labway.lims.base.api.service.HspOrgDoctorService;
import com.labway.lims.base.vo.HspOrgDeptAddVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/7/27 11:31
 */
@Slf4j
@RestController
@RequestMapping("/hsp-org-dept")
public class HspOrgDeptController extends BaseController {
    @Resource
    private HspOrgDeptService hspOrgDeptService;
    @Resource
    private HspOrgDoctorService hspOrgDoctorService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    @PostMapping("/hsp-org-depts")
    public Object hspOrgDepts(@RequestParam("hspOrgMainId") Long hspOrgMainId) {
        return hspOrgDeptService.selectByHspOrgMainId(hspOrgMainId);
    }

    @PostMapping("/add")
    public Object add(@RequestBody List<HspOrgDeptAddVo> vos) {

        if (CollectionUtils.isEmpty(vos)) {
            return Map.of();
        }

        if (vos.stream().anyMatch(e -> Objects.isNull(e.getHspOrgMainId()))
            || vos.stream().anyMatch(e -> Objects.isNull(e.getHspOrgId()))) {
            throw new IllegalArgumentException("参数错误");
        }

        if (vos.stream().anyMatch(e -> StringUtils.isBlank(e.getDept()))) {
            throw new IllegalArgumentException("科室名称不能为空");
        }

        final List<HspOrgDeptDto> dtos = JSON.parseArray(JSON.toJSONString(vos), HspOrgDeptDto.class);
        dtos.forEach(e -> e.setDeptCode(StringUtils.EMPTY));

        hspOrgDeptService.addBatch(dtos);
        // 记录操作日志
        dtos.forEach(item -> {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.HSP_DEPT_DOCTOR.getDesc())
                    .setContent(String.format("送检机构 [%s] 新增部门 [%s] ", item.getHspOrgName(), item.getDept()))
                    .toJSONString());
        });
        return Map.of();
    }

    @PostMapping("/delete")
    public Object delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("请选择需要删除的数据");
        }
        List<HspOrgDeptDto> hspOrgDeptDtos = hspOrgDeptService.selectByHspOrgDeptIds(ids);

        hspOrgDeptService.deleteByHspOrgDeptIds(ids);
        hspOrgDoctorService.deleteByHspOrgDeptIds(ids);
        hspOrgDeptDtos.forEach(item -> {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.HSP_DEPT_DOCTOR.getDesc())
                    .setContent(String.format("送检机构 [%s] 删除部门 [%s] 及部门下医生", item.getHspOrgName(), item.getDept()))
                    .toJSONString());
        });
        return Map.of();
    }
}
