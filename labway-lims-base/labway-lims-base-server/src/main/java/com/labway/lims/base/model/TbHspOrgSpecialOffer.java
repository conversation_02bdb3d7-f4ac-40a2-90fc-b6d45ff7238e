package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@TableName("tb_hsp_org_special_offer")
public class TbHspOrgSpecialOffer implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 优惠ID
     */
    @TableId
    private Long offerId;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构名称
     */
    private String hspOrgName;

    /**
     * 项目ID
     */
    private Long testItemId;

    /**
     * 项目编码
     */
    private String testItemCode;

    /**
     * 送检类型编码
     */
    private String sendTypeCode;

    /**
     * 送检类型名称
     */
    private String sendType;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 折扣率
     */
    private BigDecimal discount;

    /**
     * 检验机构
     */
    private Long orgId;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 1:删除，0:未删
     */
    private Integer isDelete;

    /**
     * 是否参与阶梯折扣
     */
    private Integer isTieredPricing;
    /**
     * 折后价格
     */
    private BigDecimal discountPrice;
    /**
     * 折前价格
     */
    private BigDecimal feePrice;
}
