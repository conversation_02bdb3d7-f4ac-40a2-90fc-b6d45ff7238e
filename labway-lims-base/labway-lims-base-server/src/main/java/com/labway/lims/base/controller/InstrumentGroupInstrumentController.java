package com.labway.lims.base.controller;

import com.alibaba.fastjson.JSON;
import com.labway.lims.api.trace.TraceLog;
import com.labway.lims.api.trace.TraceLogModuleEnum;
import com.labway.lims.api.web.BaseController;
import com.labway.lims.apply.api.service.RabbitMQService;
import com.labway.lims.base.api.dto.InstrumentGroupDto;
import com.labway.lims.base.api.dto.InstrumentGroupInstrumentDto;
import com.labway.lims.base.api.service.InstrumentGroupInstrumentService;
import com.labway.lims.base.api.service.InstrumentGroupService;
import com.labway.lims.base.vo.InstrumentGroupInstrumentVo;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/29 20:16
 */
@RestController
@RequestMapping("/instrument-group-instrument")
public class InstrumentGroupInstrumentController extends BaseController {
    @Resource
    private InstrumentGroupService instrumentGroupService;
    @Resource
    private InstrumentGroupInstrumentService instrumentGroupInstrumentService;
    @DubboReference
    private RabbitMQService rabbitMQService;

    @PostMapping("/add")
    public Object add(@RequestBody List<InstrumentGroupInstrumentVo> vos) {

        if (vos.stream().anyMatch(e -> Objects.isNull(e.getInstrumentId()))
            || vos.stream().anyMatch(e -> Objects.isNull(e.getInstrumentCode()))) {
            throw new IllegalArgumentException("仪器不能为空");
        }

        final List<InstrumentGroupInstrumentDto> dtos =
            JSON.parseArray(JSON.toJSONString(vos), InstrumentGroupInstrumentDto.class);
        instrumentGroupInstrumentService.addBatch(dtos);

        Set<Long> instrumentGroupIds =
            dtos.stream().map(InstrumentGroupInstrumentDto::getInstrumentGroupId).collect(Collectors.toSet());
        Map<Long, String> instrumentGroupNameById =
            instrumentGroupService.selectByInstrumentGroupIds(instrumentGroupIds).stream().collect(
                Collectors.toMap(InstrumentGroupDto::getInstrumentGroupId, InstrumentGroupDto::getInstrumentGroupName));
        dtos.forEach(item -> {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_GROUP_LOG.getDesc())
                    .setContent(String.format(" [%s] 专业小组添加 [%s] 仪器", instrumentGroupNameById.getOrDefault(
                        item.getInstrumentGroupId(), item.getInstrumentGroupId().toString()), item.getInstrumentName()))
                    .toJSONString());

        });
        return Map.of();
    }

    @PostMapping("/instruments")
    public Object getInstruments(@RequestParam Long instrumentGroupId) {
        return Map.of("instruments", instrumentGroupInstrumentService.selectByInstrumentGroupId(instrumentGroupId));
    }

    @PostMapping("/delete")
    public Object delete(@RequestBody Set<Long> instrumentGroupInstrumentIds) {
        if (Objects.isNull(instrumentGroupInstrumentIds)) {
            return Collections.emptyMap();
        }
        List<InstrumentGroupInstrumentDto> dtos =
            instrumentGroupInstrumentService.selectByIds(instrumentGroupInstrumentIds);
        for (Long id : instrumentGroupInstrumentIds) {
            instrumentGroupInstrumentService.deleteById(id);
        }

        Set<Long> instrumentGroupIds =
            dtos.stream().map(InstrumentGroupInstrumentDto::getInstrumentGroupId).collect(Collectors.toSet());
        Map<Long, String> instrumentGroupNameById =
            instrumentGroupService.selectByInstrumentGroupIds(instrumentGroupIds).stream().collect(
                Collectors.toMap(InstrumentGroupDto::getInstrumentGroupId, InstrumentGroupDto::getInstrumentGroupName));
        dtos.forEach(item -> {
            rabbitMQService.convertAndSend(TraceLog.EXCHANGE, TraceLog.ROUTING_KEY,
                TraceLog.newInstance().setModule(TraceLogModuleEnum.INSTRUMENT_GROUP_LOG.getDesc())
                    .setContent(String.format(" [%s] 专业小组删除 [%s] 仪器", instrumentGroupNameById.getOrDefault(
                        item.getInstrumentGroupId(), item.getInstrumentGroupId().toString()), item.getInstrumentName()))
                    .toJSONString());

        });

        return Collections.emptyMap();
    }

}
