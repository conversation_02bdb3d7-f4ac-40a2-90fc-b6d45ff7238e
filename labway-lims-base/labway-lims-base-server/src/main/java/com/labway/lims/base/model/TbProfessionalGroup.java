package com.labway.lims.base.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.lims.api.enums.YesOrNoEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 专业组
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Getter
@Setter
@TableName("tb_professional_group")
public class TbProfessionalGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 专业组ID
     */
    @TableId
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 专业组说明
     */
    private String groupRemark;

    /**
     * 专业组编码
     */
    private String groupCode;

    /**
     * 专业组类别ID
     */
    private String groupTypeCode;

    /**
     * 专业组类别名称
     */
    private String groupTypeName;

    /**
     * 批准者名字
     */
    private String approverName;

    /**
     * 批准者签名图片
     */
    private String approverSign;

    /**
     * 是否启动(0未启动 1启动)
     * @see YesOrNoEnum
     */
    private Integer enable;

    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 逻辑删除字段
     * @see YesOrNoEnum
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;

}
