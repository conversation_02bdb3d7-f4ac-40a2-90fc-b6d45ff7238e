package com.labway.lims.base.vo;

import com.labway.lims.api.enums.base.ItemTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 主数据的检验项目
 */
@Getter
@Setter
public class MainDataTestItemVo {
    /**
     * 检验项目ID
     */
    private Long testItemId;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 检验项目编码
     */
    private String testItemCode;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 项目类型名称
     *
     * @see ItemTypeEnum
     */
    private String itemType;


    /**
     * 存放说明
     */
    private String stashRemark;

    /**
     * 检验方法
     */
    private String testMethodName;

    /**
     * 报告项目
     */
    private List<ReportItem> reportItems;


    /**
     * 主数据关联的报告项目
     */
    @Getter
    @Setter
    public static class ReportItem {
        /**
         * id
         */
        private Long reportItemId;

        /**
         * 编码
         */
        private String reportItemCode;

        /**
         * 名称
         */
        private String reportItemName;
    }

}
