package com.labway.lims.base.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * (TbItemPriceCombinePackageDetail)表实体类
 *
 * <AUTHOR>
 * @since 2024-06-04 19:56:44
 */
@EqualsAndHashCode(callSuper = false)
@SuppressWarnings("serial")
@Data
@Accessors(chain = true)
@TableName("tb_item_price_combine_package_detail")
public class TbItemPriceCombinePackageDetail extends Model<TbItemPriceCombinePackageDetail> {
    //套餐包明细表主键id
    @TableId(type = IdType.INPUT)
    private Long detailId;
    //报餐包code
    private String combinePackageCode;
    //检验项目id
    private Long testItemId;
    //创建时间
    private Date createDate;
    //更新时间
    private Date updateDate;
    //跟新人id
    private Long updateId;
    //更新人名称
    private String updateName;
    //创建人id
    private Long createId;
    //创建人名称
    private String createName;
    //删除标识 0否1是
    @TableLogic(value = "0", delval = "1")
    private Integer isDelete;

}

