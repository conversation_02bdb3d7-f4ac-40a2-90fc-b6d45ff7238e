package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.base.api.dto.QcBatchDto;
import com.labway.lims.base.api.dto.QcBatchReportItemDto;
import com.labway.lims.base.api.service.QcBatchReportItemService;
import com.labway.lims.base.api.service.QcBatchService;
import com.labway.lims.base.mapper.TbQcBatchReportItemMapper;
import com.labway.lims.base.mapstruct.QcBatchReportItemConverter;
import com.labway.lims.base.model.TbQcBatchReportItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 质控批号报告项目 service impl
 * 
 * <AUTHOR>
 * @since 2023/7/4 14:38
 */
@Slf4j
@DubboService
@CacheConfig(cacheNames = " qc-batch-report-item")
public class QcBatchReportItemServiceImpl implements QcBatchReportItemService {

    @Resource
    private QcBatchService qcBatchService;

    @Resource
    private TbQcBatchReportItemMapper tbQcBatchReportItemMapper;

    @Resource
    private QcBatchReportItemConverter qcBatchReportItemConverter;

    @Override
    public void checkAddQcBatchReportItemDtos(QcBatchDto qcBatchDto, Map<String, String> reportItemCodes) {
        // 增加 样本号 检验 去掉
        return;
        /*if (reportItemCodes.isEmpty()) {
            return;
        }
        // 获取 质控批号下 此专业组 下 仪器 下与其时间存在交集的 质控批号记录
        List<QcBatchDto> qcBatchDtos = qcBatchService.selectIntersectionData(qcBatchDto.getGroupId(),
            qcBatchDto.getInstrumentCode(), qcBatchDto.getBeginDate(), qcBatchDto.getEndDate());
        Set<Long> qcBatchIds = qcBatchDtos.stream().map(QcBatchDto::getQcBatchId).collect(Collectors.toSet());
        // 这些质控批号下已有的 报告项目
        List<QcBatchReportItemDto> qcBatchReportItemDtos = this.selectByQcBatchIds(qcBatchIds);
        Set<String> selectReportItemCodes =
            qcBatchReportItemDtos.stream().map(QcBatchReportItemDto::getReportItemCode).collect(Collectors.toSet());
        String exitReportItemCodes =
            reportItemCodes.entrySet().stream().filter(obj -> selectReportItemCodes.contains(obj.getKey()))
                .map(Map.Entry::getValue).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(exitReportItemCodes)) {
            throw new LimsException(String.format("[%s] 已存在于其他相同生效时间范围的批号中，不可重复添加", exitReportItemCodes));
        }*/
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(allEntries = true)
    public void addQcBatchReportItemDtos(List<QcBatchReportItemDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        // 要添加的 物料库存
        List<TbQcBatchReportItem> targetList = qcBatchReportItemConverter.tbQcBatchReportItemListFromTbDto(list);
        // 数量 分区批次插入
        List<List<TbQcBatchReportItem>> partitionList = ListUtils.partition(targetList, 500);

        partitionList.forEach(item -> tbQcBatchReportItemMapper.batchAddQcBatchReportItem(item));

        log.info("用户 [{}] 新增物料库存[{}]成功", loginUser.getNickname(), JSON.toJSONString(targetList));
    }

    @Override
    @Cacheable(key = "'selectByQcBatchId:' + #qcBatchId")
    public List<QcBatchReportItemDto> selectByQcBatchId(long qcBatchId) {
        LambdaQueryWrapper<TbQcBatchReportItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TbQcBatchReportItem::getQcBatchId, qcBatchId);
        queryWrapper.eq(TbQcBatchReportItem::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByAsc(TbQcBatchReportItem::getCreateDate);

        return qcBatchReportItemConverter
            .qcBatchReportItemDtoListFromTbObj(tbQcBatchReportItemMapper.selectList(queryWrapper));
    }

    @Override
    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void deleteByBatchReportItemIds(Collection<Long> batchReportItemIds) {
        if (CollectionUtils.isEmpty(batchReportItemIds)) {
            return;
        }
        final LoginUserHandler.User loginUser = LoginUserHandler.get();

        log.info("用户 [{}] 删除质控批号报告项目成功 [{}] 结果 [{}]", loginUser.getNickname(), batchReportItemIds,
            tbQcBatchReportItemMapper.deleteBatchIds(batchReportItemIds) > 0);
    }

    @Override
    public List<QcBatchReportItemDto> selectByQcBatchIds(Collection<Long> qcBatchIds) {
        if (CollectionUtils.isEmpty(qcBatchIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbQcBatchReportItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TbQcBatchReportItem::getQcBatchId, qcBatchIds);
        queryWrapper.eq(TbQcBatchReportItem::getIsDelete, YesOrNoEnum.NO.getCode());
        queryWrapper.orderByAsc(TbQcBatchReportItem::getCreateDate);

        return qcBatchReportItemConverter
            .qcBatchReportItemDtoListFromTbObj(tbQcBatchReportItemMapper.selectList(queryWrapper));
    }
}
