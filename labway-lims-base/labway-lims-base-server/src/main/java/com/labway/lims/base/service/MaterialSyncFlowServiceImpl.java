package com.labway.lims.base.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.base.api.dto.MaterialSyncFlowDto;
import com.labway.lims.base.api.service.MaterialSyncFlowService;
import com.labway.lims.base.mapper.TbMaterialSyncFlowMapper;
import com.labway.lims.base.model.TbMaterialSyncFlow;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/5/10 15:20
 */
@Slf4j
@DubboService
public class MaterialSyncFlowServiceImpl implements MaterialSyncFlowService {
    @Resource
    private TbMaterialSyncFlowMapper tbMaterialSyncFlowMapper;
    @DubboReference
    private SnowflakeService snowflakeService;

    @Override
    public MaterialSyncFlowDto recentFlowRecord() {
        final LambdaQueryWrapper<TbMaterialSyncFlow> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TbMaterialSyncFlow::getStatus, YesOrNoEnum.YES.getCode())
            .eq(TbMaterialSyncFlow::getEnable, YesOrNoEnum.YES.getCode())
            .eq(TbMaterialSyncFlow::getOrgId, LoginUserHandler.get().getOrgId())
            .orderByDesc(TbMaterialSyncFlow::getCreateDate).last("limit 1");

        return convert(tbMaterialSyncFlowMapper.selectOne(wrapper));
    }

    @Override
    public long addFlowRecord(MaterialSyncFlowDto dto) {

        final LoginUserHandler.User user = LoginUserHandler.get();

        final TbMaterialSyncFlow flow = JSON.parseObject(JSON.toJSONString(dto), TbMaterialSyncFlow.class);
        flow.setMaterialSyncFlowId(ObjectUtils.defaultIfNull(dto.getMaterialSyncFlowId(), snowflakeService.genId()));
        flow.setCreatorId(user.getUserId());
        flow.setCreateDate(new Date());
        flow.setCreatorName(user.getNickname());
        flow.setUpdaterId(user.getUserId());
        flow.setResultMessage(StringUtils.substring(flow.getResultMessage(), 0, 255));
        flow.setUpdateDate(new Date());
        flow.setUpdaterName(user.getNickname());
        flow.setIsDelete(YesOrNoEnum.NO.getCode());
        flow.setOrgId(user.getOrgId());
        flow.setOrgName(user.getOrgName());
        if (tbMaterialSyncFlowMapper.insert(flow) < 1) {
            throw new IllegalStateException("添加失败");
        }

        log.info("用户 [{}] 新增流水记录成功 [{}]", user.getNickname(), JSON.toJSONString(flow));
        return flow.getMaterialSyncFlowId();

    }

    public MaterialSyncFlowDto convert(TbMaterialSyncFlow flow) {
        if (Objects.isNull(flow)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(flow), MaterialSyncFlowDto.class);
    }
}
