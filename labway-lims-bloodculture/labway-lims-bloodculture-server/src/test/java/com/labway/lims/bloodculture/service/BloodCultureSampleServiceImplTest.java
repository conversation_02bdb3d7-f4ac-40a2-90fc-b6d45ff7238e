package com.labway.lims.bloodculture.service;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.bloodculture.api.dto.BloodCultureSampleDto;
import com.labway.lims.bloodculture.api.service.BloodCultureSampleService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest
public class BloodCultureSampleServiceImplTest {
    @Resource
    private BloodCultureSampleService bloodCultureSampleService;

    @Test
    public void test() {
        final BloodCultureSampleDto sample = new BloodCultureSampleDto();
        sample.setBloodCultureSampleId(IdUtil.getSnowflakeNextId());
        sample.setApplySampleId(0L);
        sample.setApplyId(0L);
        sample.setBarcode("");
        sample.setSampleNo("");
        sample.setGroupName("");
        sample.setGroupId(0L);
        sample.setInstrumentGroupId(0L);
        sample.setInstrumentGroupName("");
        sample.setInstrumentCode("");
        sample.setInstrumentName("");
        sample.setInstrumentId(0L);
        sample.setTestDate(new Date());
        sample.setOneCheckerId(0L);
        sample.setOneCheckerName("");
        sample.setOneCheckDate(new Date());
        sample.setTwoCheckerId(0L);
        sample.setTwoCheckerName("");
        sample.setTwoCheckDate(new Date());
        sample.setOneCheckSampleReportId(0L);
        sample.setTwoCheckSampleReportId(0L);
        sample.setCreateDate(new Date());
        sample.setUpdateDate(new Date());
        sample.setUpdaterId(0L);
        sample.setUpdaterName("");
        sample.setCreatorId(0L);
        sample.setCreatorName("");
        sample.setHspOrgId(0L);
        sample.setHspOrgName("");
        sample.setOrgId(0L);
        sample.setOrgName("");
        sample.setIsDelete(0);

        bloodCultureSampleService.addBloodCultureSample(sample);
    }
}