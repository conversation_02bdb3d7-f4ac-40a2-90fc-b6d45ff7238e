<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.lims.microbiology.mapper.TbMicrobiologySampleGermMapper">


    <insert id="addBatch" parameterType="java.util.List">
        insert into tb_microbiology_sample_germ (
        microbiology_sample_germ_id,
        apply_id,
        microbiology_sample_id,
        test_item_id,
        test_item_code,
        test_item_name,
        is_delete,
        germ_id,
        germ_code,
        germ_name,
        germ_count,
        test_method,
        germ_remark,
        updater_id,
        updater_name,
        creator_id,
        creator_name,
        update_date,
        create_date,
        apply_sample_id,
        germ_genus_id,
        germ_genus_code,
        germ_count_code,
        test_method_code,
        germ_remark_code
        ) values
        <foreach collection="germs" item="item" separator=",">
            (
            #{item.microbiologySampleGermId},
            #{item.applyId},
            #{item.microbiologySampleId},
            #{item.testItemId},
            #{item.testItemCode},
            #{item.testItemName},
            #{item.isDelete},
            #{item.germId},
            #{item.germCode},
            #{item.germName},
            #{item.germCount},
            #{item.testMethod},
            #{item.germRemark},
            #{item.updaterId},
            #{item.updaterName},
            #{item.creatorId},
            #{item.creatorName},
            #{item.updateDate},
            #{item.createDate},
            #{item.applySampleId},
            #{item.germGenusId},
            #{item.germGenusCode},
            #{item.germCountCode},
            #{item.testMethodCode},
            #{item.germRemarkCode}
            )
        </foreach>
    </insert>

    <update id="updateByMicrobiologySampleGermIds">
        update tb_microbiology_sample_germ

        <trim prefix="set" suffixOverrides=",">

            <trim prefix="test_method_code =case" suffix="end,">
                <foreach collection="germs" item="i" index="index">
                    <if test="i.testMethodCode!=null">
                        when microbiology_sample_germ_id=#{i.microbiologySampleGermId} then #{i.testMethodCode}
                    </if>
                </foreach>
            </trim>

            <trim prefix="test_method =case" suffix="end,">
                <foreach collection="germs" item="i" index="index">
                    <if test="i.testMethod!=null">
                        when microbiology_sample_germ_id=#{i.microbiologySampleGermId} then #{i.testMethod}
                    </if>
                </foreach>
            </trim>

            <trim prefix="germ_remark_code =case" suffix="end,">
                <foreach collection="germs" item="i" index="index">
                    <if test="i.germRemarkCode!=null">
                        when microbiology_sample_germ_id=#{i.microbiologySampleGermId} then #{i.germRemarkCode}
                    </if>
                </foreach>
            </trim>

            <trim prefix="germ_remark =case" suffix="end,">
                <foreach collection="germs" item="i" index="index">
                    <if test="i.germRemark!=null">
                        when microbiology_sample_germ_id=#{i.microbiologySampleGermId} then #{i.germRemark}
                    </if>
                </foreach>
            </trim>

        </trim>

        where microbiology_sample_germ_id in
        <foreach collection="germs" item="i" index="index" open="(" separator="," close=")">
            #{i.microbiologySampleGermId}
        </foreach>

    </update>
</mapper>
