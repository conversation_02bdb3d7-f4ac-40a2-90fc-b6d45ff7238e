<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.lims.infection.mapper.TbInfectionSampleMapper">
    <update id="updateByInfectionSampleIds">
        update tb_infection_sample
        <set>
            <if test="infectionSample.checkDate != null">
                check_date = #{infectionSample.checkDate},
            </if>
            <if test="infectionSample.checkerName != null">
                checker_name = #{infectionSample.checkerName},
            </if>
            <if test="infectionSample.checkerId != null">
                checker_id = #{infectionSample.checkerId},
            </if>
            <if test="infectionSample.testDate != null">
                test_date = #{infectionSample.testDate},
            </if>
        </set>
        where infection_sample_id in
        <foreach collection="infectionSampleIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
</mapper>
