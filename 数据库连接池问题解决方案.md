# 数据库连接池耗尽问题解决方案

## 问题描述
生产环境出现 `HikariPool-1 - Connection is not available, request timed out after 30000ms` 错误，导致数据库查询失败。

## 根本原因分析
1. **连接池配置不足** - 原配置只有 `minimum-idle: 5`，缺少关键参数
2. **高并发访问** - 生产环境并发请求超过连接池容量
3. **连接泄漏可能** - 长时间运行可能存在连接未正确释放

## 已实施的解决方案

### 1. 优化HikariCP连接池配置

#### labway-lims-base服务配置
```yaml
spring:
  datasource:
    hikari:
      minimum-idle: 10                    # 最小空闲连接数
      maximum-pool-size: 50               # 最大连接池大小
      connection-timeout: 20000           # 连接超时时间(20秒)
      idle-timeout: 300000                # 空闲连接超时时间(5分钟)
      max-lifetime: 1200000               # 连接最大生命周期(20分钟)
      leak-detection-threshold: 60000     # 连接泄漏检测阈值(1分钟)
      connection-test-query: SELECT 1
      validation-timeout: 3000
      auto-commit: true
      read-only: false
      pool-name: HikariPool-LIMS-Base
```

#### labway-lims-routine服务配置
```yaml
spring:
  shardingsphere:
    datasource:
      master:
        minimum-idle: 15                    # routine服务查询较多，设置更高
        maximum-pool-size: 60               # 最大连接池大小
        connection-timeout: 20000
        idle-timeout: 300000
        max-lifetime: 1200000
        leak-detection-threshold: 60000
        connection-test-query: SELECT 1
        validation-timeout: 3000
        auto-commit: true
        read-only: false
        pool-name: HikariPool-LIMS-Routine
```

### 2. 配置参数说明

| 参数 | 说明 | 推荐值 |
|------|------|--------|
| `minimum-idle` | 最小空闲连接数 | 10-15 |
| `maximum-pool-size` | 最大连接池大小 | 50-60 |
| `connection-timeout` | 获取连接超时时间 | 20000ms |
| `idle-timeout` | 空闲连接超时时间 | 300000ms (5分钟) |
| `max-lifetime` | 连接最大生命周期 | 1200000ms (20分钟) |
| `leak-detection-threshold` | 连接泄漏检测 | 60000ms (1分钟) |

## 立即执行步骤

### 1. 重启相关服务
```bash
# 重启base服务
sudo systemctl restart labway-lims-base

# 重启routine服务  
sudo systemctl restart labway-lims-routine
```

### 2. 监控连接池状态
```bash
# 查看应用日志中的连接池信息
tail -f /var/log/labway-lims/application.log | grep -i hikari

# 监控数据库连接数
psql -h db21094d9c2847c692172e06e3f0864cin03.internal.cn-east-3.postgresql.rds.myhuaweicloud.com \
     -U labway_lims -d labway-lims \
     -c "SELECT count(*) as active_connections FROM pg_stat_activity WHERE datname='labway-lims';"
```

### 3. 应用层面优化建议

#### 代码优化
1. **检查连接泄漏**
   - 确保所有数据库操作都在事务中正确提交或回滚
   - 使用 `@Transactional` 注解管理事务
   - 避免长时间持有数据库连接

2. **查询优化**
   - 检查 `SampleCriticalResultServiceImpl.selectByGroupId` 方法的查询效率
   - 添加必要的数据库索引
   - 考虑分页查询大结果集

#### 监控和告警
1. **添加连接池监控**
```java
// 在应用中添加连接池状态监控
@Component
public class HikariPoolMonitor {
    
    @Autowired
    private HikariDataSource dataSource;
    
    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void monitorConnectionPool() {
        HikariPoolMXBean poolBean = dataSource.getHikariPoolMXBean();
        log.info("连接池状态 - 活跃连接: {}, 空闲连接: {}, 等待连接: {}, 总连接: {}", 
                poolBean.getActiveConnections(),
                poolBean.getIdleConnections(), 
                poolBean.getThreadsAwaitingConnection(),
                poolBean.getTotalConnections());
    }
}
```

2. **设置告警阈值**
   - 当活跃连接数 > 80% 最大连接数时告警
   - 当等待连接的线程数 > 5 时告警

## 长期优化建议

### 1. 数据库层面
- 检查数据库服务器的 `max_connections` 配置
- 优化慢查询，添加必要索引
- 考虑读写分离减轻主库压力

### 2. 应用架构层面
- 实施连接池分离：读操作和写操作使用不同连接池
- 考虑引入缓存减少数据库查询
- 实施异步处理减少同步数据库操作

### 3. 监控体系
- 集成 Micrometer 监控连接池指标
- 设置 Grafana 仪表板监控数据库连接状态
- 配置告警规则及时发现问题

## 验证步骤
1. 重启服务后观察错误日志是否还有连接超时
2. 监控连接池使用情况是否正常
3. 压力测试验证高并发场景下的稳定性

## 紧急回滚方案
如果新配置导致问题，可以快速回滚到原配置：
```yaml
spring:
  datasource:
    hikari:
      minimum-idle: 5  # 原配置
```

---
**创建时间**: 2025-09-13  
**负责人**: 系统管理员  
**状态**: 已实施，待验证
